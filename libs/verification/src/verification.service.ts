import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { VerifyFaceResponseDto } from 'src/integration/verification/src/dto';
import { BvnDto } from 'src/utils';

@Injectable()
export class VerificationService {
  constructor(private readonly httpService: HttpService) {
  }

  async verifyFace(
    bvn: string,
    file: Express.Multer.File,
  ): Promise<VerifyFaceResponseDto> {
    const body = {};
    const form = new FormData();
    form.append('bvn', bvn);
    form.append(
      'file',
      new Blob([Uint8Array.from(file.buffer)], { type: file.mimetype }),
    );
    const { data } = await firstValueFrom(
      this.httpService
        .post(`/verifications/bvn/face`, form, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new UnprocessableEntityException(
              error.response?.data ?? error,
            );
          }),
        ),
    );
    console.log(data);

    return data?.data as VerifyFaceResponseDto;
  }

  async uploadImage(file: Express.Multer.File): Promise<string | null> {
    const body = {};
    const form = new FormData();
    form.append('folder', 'credpal-verification');
    form.append('isPrivate', 'false');
    form.append(
      'file',
      new Blob([Uint8Array.from(file.buffer)], { type: file.mimetype }),
    );
    const { data } = await firstValueFrom(
      this.httpService
        .post(`/v1/uploader/upload`, form, {
          baseURL: 'https://fileservice.credpal.com/api',
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-api-key': 'ba0204f6-b861-4685-a73d-9566aa9a7a13',
            // Authorization: 'Bearer ${GetIt.I<Env>().uploadToken}',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new UnprocessableEntityException(
              error.response?.data ?? error,
            );
          }),
        ),
    );
    console.log(data);

    return data?.url;
  }

  async uploadBlob(file: Blob): Promise<string | null> {
    const body = {};
    const form = new FormData();
    form.append('folder', 'credpal-verification');
    form.append('isPrivate', 'false');
    form.append('file', file); // new Blob((await file.bytes), { type: file.mimetype }));
    const { data } = await firstValueFrom(
      this.httpService
        .post(`/v1/uploader/upload`, form, {
          baseURL: 'https://fileservice.credpal.com/api',
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-api-key': 'ba0204f6-b861-4685-a73d-9566aa9a7a13',
            // Authorization: 'Bearer ${GetIt.I<Env>().uploadToken}',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new UnprocessableEntityException(
              error.response?.data ?? error,
            );
          }),
        ),
    );
    console.log(data);

    return data?.url;
  }

  async getBvn(bvn: string): Promise<BvnDto> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          '/verifications/bnv',
          { bvn },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new UnprocessableEntityException(
              error.response?.data ?? error,
            );
          }),
        ),
    );
    console.log(data);

    return data?.data as BvnDto;
  }
}
