import { Module } from '@nestjs/common';
import { MbsService } from './mbs.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.mbs.uri,
      headers: {
        'Client-ID': `${config.mbs.clientId}`,
        'Client-Secret': `${config.mbs.secret}`,
        'Content-type': 'application/json',
      },
    }),
  ],
  providers: [MbsService],
  exports: [MbsService],
})
export class MbsModule {}
