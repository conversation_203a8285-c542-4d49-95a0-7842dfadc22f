import { BadRequestException, Injectable } from '@nestjs/common';
import config from 'src/config';

import axios from 'axios';
import {
  ConfirmStatementDto,
  RequestStatementDto,
} from 'src/bank-statement/dto/statement.dto';
import { FeedbackInterface } from 'src/bank-statement/dto/feedback.dto';
// error occured { status: '00', message: 'Successful', result: ******** }
// { status: '00', message: 'Successful', result: ******** }

@Injectable()
export class MbsService {
  constructor() {
    // this.getBanks().then(console.log);
    // this.getBanks().then(console.log);
    // this.requestStatement({
    //   accountNo: '**********',
    //   bankId: '6',
    //   phone: '***********',
    //   applicantName: 'Wisdom Ekeh'
    // }).then(console.log);
    // this.confirmRequestStatement({
    //   ticketNo: '3382548-6',
    //   otp: '7795',
    // }).then(console.log);
    // this.getJson({
    //   ticketNo: '3382548-6',
    //   password: '7795',
    // }).then(console.log);
    //  this.getCsv('2546101-5').then(console.log);
    // this.confirmRequestStatement({
    //   ticketNo: '2546101-5',
    //   otp: '4878',
    // }).then(console.log);
    // this.getPDF('3382548-6').then(console.log);
    const endDate = new Date();
    // start date is end date - 6 months
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - 6);
    // console.log('Dates', startDate.toISOString(), endDate.toISOString());
  }

  async requestStatement(
    data: RequestStatementDto & { applicantName: string },
  ) {
    //controller
    const endDate = new Date();
    // start date is end date - 6 months
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - 3);
    try {
      const response = await this.axiosCall('RequestStatement', 'POST', {
        accountNo: data.accountNo,
        bankId: data.bankId,
        destinationId: config.mbs.clientId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        role: 'Applicant', /// role; Applicant, Sponsor or Guarantor
        phone: data.phone,
        country: 'NG',
        username: config.mbs.corporateEmail,
        applicants: [{ name: data.applicantName }],
      });
      console.log('error occured', response);

      return response;
    } catch (error) {
      console.log('error occured', error);
      // throw error;
      //       {
      //   status: '-2',
      //   message: 'Invalid input',
      //   result: [
      //     "bankId is invalid. Kindly use the bank's e-channels to make this request"
      //   ]
      // }
      if (error?.result && error.result[0]) {
        throw new BadRequestException(error?.result[0]);
      }

       if (error?.options) {
        throw new BadRequestException(error?.options?.message);
      }

      throw new BadRequestException('An error ocurred', error);
    }
    // save result to entity
  }

  async confirmRequestStatement(data: ConfirmStatementDto) {
    try {
      const response = await this.axiosCall('ConfirmStatement', 'POST', {
        ticketNo: data.ticketNo,
        password: data.otp,
      });

      return response;
    } catch (error) {
        const err = error;
      console.log('error occured', err);
      if (error?.options) {
        throw new BadRequestException(error?.options?.message);
      }
      throw new BadRequestException('An error ocurred', error);
    }
  }

  async getPDF(ticketNo: string) {
    // get ticketNo using userId, pending request, one pending request per user
    try {
      const response = await this.axiosCall('GetPDFStatement', 'POST', {
        ticketNo,
      });

      return response;
    } catch (error) {
      const err = error;
      console.log('error occured', err);
      if (err?.options) {
        throw new BadRequestException(err?.options?.message);
      }

       if (error?.result && error.result[0]) {
        throw new BadRequestException(error?.result[0]);
      }

      throw new BadRequestException('An error ocurred', error);
    }
  }

  async getJson(data: { ticketNo: string; password: string }) {
    // get ticketNo using userId, pending request, one pending request per user
    try {
      const response = await this.axiosCall('GetStatementJSONObject?', 'POST', {
        ticketNo: data.ticketNo,
        password: data.password,
      });

      return response;
    } catch (error) {
      throw new BadRequestException('An error ocurred', error);
    }
  }

  async getCsv(
    ticketNo: string,
  ): Promise<{ status: string; message: string; result: string } | null> {
    // get ticketNo using userId, pending request, one pending request per user
    try {
      const response = await this.axiosCall('GetCSVStatement', 'POST', {
        ticketNo,
      });

      return response;
    } catch (error) {
      throw new BadRequestException('An error ocurred', error);
    }
  }

  async getFeedback(requestId: string): Promise<FeedbackInterface> {
    try {
      const response = await this.axiosCall('GetFeedback', 'POST', {
        ID: requestId,
      });

      return response;
    } catch (error) {
      throw new BadRequestException('An error ocurred', error);
    }
  }

  async axiosCall(uri: string, method: string = 'GET', data: any) {
    const options = {
      method,
      url: `${config.mbs.uri}/${uri}`,
      headers: {
        'Client-ID': `${config.mbs.clientId}`,
        'Client-Secret': `${config.mbs.secret}`,
        'Content-type': 'application/json',
      },
      data,
      // validateStatus: () => true,
    };

    try {
      const response = await axios(options);
      return response.data;
    } catch (error) {
      console.log(error?.response?.data);
      throw error?.response?.data;
      //  new BadRequestException('External API request failed', error);
    }
  }

  async getBanks() {
    try {
      const banks = await this.axiosCall(
        'SelectActiveRequestBanks',
        'POST',
        {},
      );

      return banks.result;
    } catch (error) {
      throw new BadRequestException('An error ocurred', error);
    }
  }
}
