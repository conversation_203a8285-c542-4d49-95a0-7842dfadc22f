import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { catchError, firstValueFrom } from 'rxjs';
import config from 'src/config';
import { TransactionsQueryDTO } from 'src/tranx-request/dto/get-transaction-query.dto';
import { TransferQueryDTO } from 'src/tranx-request/dto/get-transfer-query.dto';
import {
  ICreateDirectRequest,
  PaystackAuthorizationResponse,
  PaystackAuthorizationTransaction,
  PaystackCustomerResponse,
  PaystackTransactionResponse,
} from './dto';

@Injectable()
export class PaystackService {
  instance: AxiosInstance;

  constructor(private readonly httpService: HttpService) {
    this.instance = axios.create({
      baseURL: config.paystack.baseUrl,
      headers: {
        Authorization: ` Bearer ${config.paystack.secretKey}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async initiateDirectDebit(dto: ICreateDirectRequest): Promise<{
    redirect_url: string;
    access_code: string;
    reference: string;
  } | null> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(`/customer/authorization/initialize`, {
          channel: 'direct_debit',
          email: dto.email,
          callback_url: dto.callbackUrl,
          // reference: dto.reference,
        })
        .pipe(
          catchError((error) => {
            console.log(error?.response?.data ?? error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data?.data;
  }

  async verifyTransaction(
    reference: string,
  ): Promise<PaystackTransactionResponse | null> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/verify/${reference}`).pipe(
        catchError((error) => {
          console.log(error?.response?.data ?? error);

          throw error.response?.data ?? error;
        }),
      ),
    );
    console.log(data);

    return data;
  }

  async verifyAuthorization(
    reference: string,
  ): Promise<PaystackAuthorizationResponse | null> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/customer/authorization/verify/${reference}`).pipe(
        catchError((error) => {
          console.log(error?.response?.data ?? error);

          throw error.response?.data ?? error;
        }),
      ),
    );
    console.log(data);

    return data;
  }

  async getCustomer(email: string): Promise<PaystackCustomerResponse | null> {
    const { data } = await firstValueFrom(
      this.httpService.get(`/customer/${email}`).pipe(
        catchError((error) => {
          console.log(error?.response?.data ?? error);

          throw error.response?.data ?? error;
        }),
      ),
    );
    console.log(data);

    return data;
  }

  async retryAuthorizationCharge(
    customerId: string,
  ): Promise<PaystackCustomerResponse | null> {
    const { data } = await firstValueFrom(
      this.httpService
        .put(`/directdebit/activation-charge`, {
          customer_ids: [customerId],
        })
        .pipe(
          catchError((error) => {
            console.log(error?.response?.data ?? error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data;
  }

  async getTransactions(queryParams: TransactionsQueryDTO) {
    try {
      const response = await this.instance.get('/transaction', {
        params: queryParams,
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  }

  async getTransfers(queryParams: TransferQueryDTO) {
    try {
      const response = await this.instance.get('/transfer', {
        params: queryParams,
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  }
}
