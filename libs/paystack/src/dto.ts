export interface ICreateDirectRequest {
  email: string;
  //   reference: string;
  callbackUrl?: string;
}

export interface PaystackTransactionLogHistory {
  type: string;
  message: string;
  time: number;
}

export interface PaystackTransactionLog {
  start_time: number;
  time_spent: number;
  attempts: number;
  errors: number;
  success: boolean;
  mobile: boolean;
  input: any[];
  history: PaystackTransactionLogHistory[];
}

export interface PaystackTransactionAuthorization {
  authorization_code: string;
  bin: string;
  last4: string;
  exp_month: string;
  exp_year: string;
  channel: string;
  card_type: string;
  bank: string;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string | null;
}

export interface PaystackTransactionCustomer {
  id: number;
  first_name: string | null;
  last_name: string | null;
  email: string;
  customer_code: string;
  phone: string | null;
  metadata: any;
  risk_action: string;
  international_format_phone: string | null;
}

export interface PaystackTransactionData {
  id: number;
  domain: string;
  status: string;
  reference: string;
  receipt_number: string | null;
  amount: number;
  message: string | null;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  currency: string;
  ip_address: string;
  metadata: any;
  log: PaystackTransactionLog;
  fees: number;
  fees_split: any;
  authorization: PaystackTransactionAuthorization;
  customer: PaystackTransactionCustomer;
  plan: any;
  split: any;
  order_id: string | null;
  paidAt: string;
  createdAt: string;
  requested_amount: number;
  pos_transaction_data: any;
  source: any;
  fees_breakdown: any;
  connect: any;
  transaction_date: string;
  plan_object: any;
  subaccount: any;
}

export interface PaystackTransactionResponse {
  status: boolean;
  message: string;
  data: PaystackTransactionData;
}

export interface PaystackAuthorizationCustomer {
  first_name: string;
  last_name: string;
  code: string;
  email: string;
  phone: string | null;
  metadata: any;
  risk_action: string;
}

export interface PaystackAuthorizationTransaction {
  authorization_code: string;
  domain: string;
  active: boolean;
  last4: string;
  channel: string;
  card_type: string;
  bank: string;
  exp_month: number;
  exp_year: number;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string;
  customer: PaystackAuthorizationCustomer;
}

export interface PaystackAuthorizationResponse {
  status: boolean;
  message: string;
  data: PaystackAuthorizationTransaction;
}

export interface PaystackCustomerResponse {
  status: boolean;
  message: string;
  data: PaystackCustomer;
}

export interface PaystackCustomer {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  customer_code: string;
  phone: string | null;
  metadata: any;
  risk_action: string;
  created_at: string;
  authorizations: PaystackAuthorization[];
}

export interface PaystackAuthorization {
  authorization_code: string;
  bin: string;
  last4: string;
  exp_month: string;
  exp_year: string;
  channel: string;
  card_type: string;
  bank: string;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string;
}
