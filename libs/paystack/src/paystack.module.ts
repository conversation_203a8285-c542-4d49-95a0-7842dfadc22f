import { Module } from '@nestjs/common';
import { PaystackService } from './paystack.service';
import { HttpModule, HttpService } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: `${config.paystack.baseUrl}`,
      headers: {
        Authorization: ` Bearer ${config.paystack.secretKey}`,
        'Content-Type': 'application/json',
      },
    }),
  ],
  providers: [PaystackService],
  exports: [PaystackService],
})
export class PaystackModule {}
