import { Module } from '@nestjs/common';
import { LoanbotService } from './loanbot.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: `${config.loanbot.uri}`,
      headers: {
        Authorization: `Bearer ${config.loanbot.apiKey}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [LoanbotService],
  exports: [LoanbotService],
})
export class LoanbotModule {}
