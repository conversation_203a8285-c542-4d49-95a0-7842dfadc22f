# Base image
FROM node:20.18.0-alpine

RUN apk --no-cache add curl

# Install tzdata for timezone configuration
RUN apk add --no-cache tzdata

# Set the desired timezone in the container
ENV TZ=Africa/Lagos

EXPOSE 4001
# Create app directory  
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./
COPY .npmrc ./

# Install app dependencies
RUN yarn install

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN yarn build

# Start the server using the production build
CMD ["yarn", "start" ] 

