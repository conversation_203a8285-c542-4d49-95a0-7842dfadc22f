name: Notify Google Space

on:
  pull_request:
    types: [opened]

jobs:
  notify_space:
    runs-on: ubuntu-latest
    steps:
    - name: New Pull Request
      if: ${{ always() }} # Use always to ensure that the notification is also send on failure of former steps
      uses: SimonScholz/google-chat-action@main
      with:
        webhookUrl: '${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}'
        title:  New Pull Request
       
  test:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.18.0'
          cache: 'yarn'
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile
        
      - name: Run tests
        run: yarn test
