name: Vulnerability Scan

on:
  schedule:
    - cron: '0 9 * * *'

jobs:
  scan:
    name: Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - name: Login to SWR
        uses: huaweicloud/swr-login@v2.1.0
        with:
          access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          access-key-secret: ${{ secrets.ACCESS_KEY_SECRET }}
          region: af-south-1
      - name: Pull docker image
        run: docker pull swr.af-south-1.myhuaweicloud.com/credpal-prod/credpal-credit-companion:prod

      - uses: lazy-actions/gitrivy@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          image: swr.af-south-1.myhuaweicloud.com/credpal-prod/credpal-credit-companion:prod  