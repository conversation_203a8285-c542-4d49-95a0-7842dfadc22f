import { IsNotEmpty, IsString, Matches, IsEnum } from 'class-validator';
import { VerificationMethod } from '../entities/kyc.entity';
import { ApiProperty } from '@nestjs/swagger';

export class SendOtpDto {
  @IsNotEmpty()
  @IsString()
  @Matches(/^(234|0)(70|80|81|90|91|70|81|90|91)[0-9]{8}$/, {
    message: 'Phone number must be in E.164 format (e.g., 2348134567890)',
  })
  phoneNumber: string;

  @ApiProperty({ enum: VerificationMethod })
  @IsEnum(VerificationMethod)
  verificationMethod: VerificationMethod;
}
