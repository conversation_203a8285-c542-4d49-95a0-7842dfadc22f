import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class VerifyBVNFaceIDDTO {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: Express.Multer.File;
}

export class ResetDeviceDTO {
  // @ApiProperty()
  // @IsString()
  // @IsNotEmpty()
  // token: string;

  @ApiProperty({ type: 'string', format: 'binary' })
  file: Express.Multer.File;
}

export class ResetPinDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pin: string;

  @ApiProperty({ type: 'string', format: 'binary' })
  file: Express.Multer.File;
}
