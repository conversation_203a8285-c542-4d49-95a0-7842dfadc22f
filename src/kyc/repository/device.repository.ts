import { Injectable } from "@nestjs/common";
import { Configurations } from "../../config/entities/Configurations";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { Devices } from "src/config/entities/Devices";

@Injectable()
export class DeviceRepository extends TypeOrmRepository<Devices> {
  constructor(private readonly dataSource: DataSource) {
    super(Devices, dataSource.createEntityManager());
  }
}
