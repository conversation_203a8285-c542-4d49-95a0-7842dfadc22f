import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { UsedTransactionPins } from "src/config/entities/UsedTransactionPins";

@Injectable()
export class UsedPinRepository extends TypeOrmRepository<UsedTransactionPins> {
  constructor(private readonly dataSource: DataSource) {
    super(UsedTransactionPins, dataSource.createEntityManager());
  }
}
