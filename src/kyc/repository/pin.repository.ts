import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { TransactionPins } from "src/config/entities/TransactionPins";

@Injectable()
export class PinRepository extends TypeOrmRepository<TransactionPins> {
  constructor(private readonly dataSource: DataSource) {
    super(TransactionPins, dataSource.createEntityManager());
  }
}
