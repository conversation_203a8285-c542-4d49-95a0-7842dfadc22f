import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { SelfieAttempt } from 'src/config/entities/SelfieAttempt';

@Injectable()
export class SelfieAttemptRepository extends TypeOrmRepository<SelfieAttempt> {
  constructor(private readonly dataSource: DataSource) {
    super(SelfieAttempt, dataSource.createEntityManager());
  }
}
