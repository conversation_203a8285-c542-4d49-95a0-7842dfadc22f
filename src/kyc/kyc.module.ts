import { Module } from '@nestjs/common';
import { KycService } from './kyc.service';
import { KycController } from './kyc.controller';
import { WhatsAppModule } from 'src/integration/whatsapp/whatsapp.module';
import { SmsModule } from 'src/integration/sms/sms.module';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { UserRepository } from 'src/user/repository/user.repository';
import { VerificationModule } from '@app/verification';
import { DeviceRepository } from './repository/device.repository';
import { UsedPinRepository } from './repository/used-pin.repository';
import { PinRepository } from './repository/pin.repository';
import { TelegramModule } from 'src/integration/telegram/telegram.module';
import { SelfieAttemptRepository } from './repository/selfie-attempt.repository';
@Module({
  imports: [WhatsAppModule, SmsModule, VerificationModule, TelegramModule],
  controllers: [KycController],
  providers: [
    KycService,
    UserRepository,
    UserProfileRepository,
    DeviceRepository,
    PinRepository,
    UsedPinRepository,
    SelfieAttemptRepository
  ],
})
export class KycModule {}
