import {
  Controller,
  Post,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Headers,
} from '@nestjs/common';
import { KycService } from './kyc.service';
import { AuthData, GetAuthData, JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { VerifyOtpDtoOtpDto } from './dto/verify-otp.dto';
import { SendOtpDto } from './dto/send-otp.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ResetDeviceDTO,
  ResetPinDTO,
  VerifyBVNFaceIDDTO,
} from './dto/verify_face_dto';

@Controller('kyc')
export class KycController {
  constructor(private readonly kycService: KycService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT')
  @Post('/send-otp')
  sendOtp(@Body() dto: SendOtpDto, @GetAuthData() auth: AuthData) {
    return this.kycService.sendOtp(auth, dto.phoneNumber, dto.verificationMethod);
  }

  @Post('/verify-otp')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT')
  verifyOtp(
    @Body() verifyOtpDto: VerifyOtpDtoOtpDto,
    @GetAuthData() auth: AuthData,
  ) {
    return this.kycService.verifyOtp(auth, verifyOtpDto);
  }

  @Post('bvn/face')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Image file',
    type: VerifyBVNFaceIDDTO,
  })
  async verifyBVNFace(
    @Body() payload: VerifyBVNFaceIDDTO,
    @UploadedFile() file: Express.Multer.File,
    @GetAuthData() auth: AuthData,
  ) {
    if (!file.mimetype.match(/\/(jpg|jpeg|png)$/)) {
      throw new BadRequestException(
        'Only JPG, JPEG, and PNG files are allowed!',
      );
    }

    return this.kycService.verifyFace(file, auth.id.toString());
  }

  @Post('pin/reset')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Image file',
    type: ResetPinDTO,
  })
  async resetPin(
    @Body() payload: ResetPinDTO,
    @UploadedFile() file: Express.Multer.File,
    @GetAuthData() auth: AuthData,
  ) {
    if (!file.mimetype.match(/\/(jpg|jpeg|png)$/)) {
      throw new BadRequestException(
        'Only JPG, JPEG, and PNG files are allowed!',
      );
    }

    return this.kycService.resetPin(file, auth.id.toString(), payload.pin);
  }

  @Post('device/reset')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Image file',
    // type: VerifyBVNFaceIDDTO,
  })
  async resetDevice(
    @Body() payload: ResetDeviceDTO,
    @UploadedFile() file: Express.Multer.File,
    @Headers() headers: object,
  ) {
    if (!file.mimetype.match(/\/(jpg|jpeg|png)$/)) {
      throw new BadRequestException(
        'Only JPG, JPEG, and PNG files are allowed!',
      );
    }

    return this.kycService.resetDevice(
      headers['authorization'].split(' ')[1],
      file,
    );
  }
}
