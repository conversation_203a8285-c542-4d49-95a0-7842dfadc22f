import { Module } from '@nestjs/common';
import { CreditService } from './credit.service';
import { CreditController } from './credit.controller';
import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';
import { UserPlansRepository } from './repository/user-plan.repository';
import { CpcashWalletsRepository } from './repository/cp-cash-wallets.repository';
import { UserRepository } from 'src/user/repository/user.repository';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { DocumentsRepository } from './repository/documents.repository';
import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
import { StatementService } from './statement.service';
import { CreditConsumer } from './consumers/credit.consumer';
import { CashEvents, Events, Exchanges } from 'src/utils';
import config from 'src/config';
import { PaymentCacheModule, RabbitmqModule } from '@crednet/utils';
import { ConfigurationRepository } from './repository/configuration.repository';
import { BullModule } from '@nestjs/bullmq';
import { SyncStatementConsumer } from './consumers/sync-statement.consumer';
import { PersonalPlansRepository } from './repository/personal-plans.repository';

@Module({
  imports: [
    PaymentCacheModule,
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'credit.queue',
      prefetchCount: 10,
      deadLetterQueueInterval: 100000,
      consumeDeadLetterQueue: config.isCronEnabled,
      showLog: true,
      producer: {
        name: Exchanges.PAYMENT,
        durable: true,
      },
      subscriptions: [
        `${Exchanges.CREDIT}.${CashEvents.REFUND_TRANSACTION}`,
        `${Exchanges.PAYMENT}.${CashEvents.REVERSE_TRANSACTION}`,
      ],
    }),
    BullModule.registerQueue({ name: Events.SYNC_STATEMENT }),
  ],
  controllers: [CreditController],
  providers: [
    CreditService,
    PersonalCardAccountsRepository,
    UserPlansRepository,
    UserRepository,
    CpcashWalletsRepository,
    UserPlansRepository,
    UserProfileRepository,
    DocumentsRepository,
    StatementService,
    CreditConsumer,
    PersonalAccountStatementsRepository,
    PersonalCardATransactionsRepository,
    ConfigurationRepository,
    SyncStatementConsumer,
    PersonalPlansRepository
  ],
})
export class CreditModule {}
