import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { CreditService } from './credit.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { InitiatePurchaseDto } from './dto/initiate-purchase.dto';

@Controller('credit')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class CreditController {
  constructor(private readonly creditService: CreditService) {}

  @Post('/initiate')
  @UseGuards(PinGuard)
  @PinRequirement('pin')
  initAirtimePurchase(
    @Body() dto: InitiatePurchaseDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.creditService.initiateWalletCharge(dto.reference, auth);
  }

  @Post('/activate')
  activate(@GetAuthData() auth: AuthData): Promise<any> {
    return this.creditService.activateCreditCard(auth.id + '');
  }
}
