import { Injectable } from '@nestjs/common';
import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
import { PersonalCardTransactions } from '../config/entities/PersonalCardTransactions';
import { EntityManager, In } from 'typeorm';
import { UserPlansRepository } from './repository/user-plan.repository';
import { PersonalAccountStatements } from '../config/entities/PersonalAccountStatements';
import { DateTime } from 'luxon';
import { ConfigurationRepository } from './repository/configuration.repository';
import { UserProfiles } from '../config/entities/UserProfiles';
import { PersonalCardAccounts } from '../config/entities/PersonalCardAccounts';
import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';

export enum CONDITION {
  PAID = 'paid',
  OPEN = 'open',
  UNPAID = 'unpaid',
  PENDING = 'pending',
  PART_PAID = 'part_paid',
  CLOSE = 'close',
}

export enum STATUS {
  ERROR = 'error',
  FAILED = 'failed',
  PENDING = 'pending',
  REVERSED = 'reversed',
  SUCCESS = 'success',
}

/**
 * Service responsible for managing credit card statements and related operations.
 * Handles statement generation, transaction tracking, and balance calculations.
 */
@Injectable()
export class StatementService {
  constructor(
    private readonly userPlansRepository: UserPlansRepository,
    private readonly personalAccountStatementsRepository: PersonalAccountStatementsRepository,
    private readonly personalCardATransactionsRepository: PersonalCardATransactionsRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly configurationRepository: ConfigurationRepository,
  ) {}

  /**
   * Retrieves all debit transactions for a given statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of debit transactions with status 'success' or 'pending'
   */
  public debitTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        type: 'debit',
        status: In(['success', 'pending']),
      },
    });
  }

  /**
   * Retrieves all credit transactions for a given statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of credit transactions with status 'success'
   */
  public creditTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        type: 'credit',
        status: 'success',
      },
    });
  }

  /**
   * Calculates the sum of all credit transactions for a given statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the total sum of credit transactions
   */
  public creditTransactionsSum(statementId: string) {
    return this.personalCardATransactionsRepository.sum('amount' as any, {
      statement: { id: statementId },
      type: 'credit',
      status: 'success',
    });
  }

  /**
   * Calculates the total outstanding balance for a statement.
   * Includes opening balance, carry-over balance, deferred plan amount, fees, and interest.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the total outstanding balance
   */
  public async totalOutstanding(statementId: string): Promise<{
    total: number;
    interest: number;
    aggBalance: number;
    carryOverBalance: number;
    deferredPlanAmount: number;
    feesAndCharges: number;
  }> {
    const statement = await this.personalAccountStatementsRepository.findOne({
      where: {
        id: statementId,
      },
    });
    const aggBalance = await this.aggBalance(statementId, true);
    const interest = await this.interest(
      statementId,
      statement.userId,
      aggBalance,
    );
    const carryOverBalance = parseFloat(statement.carryOverBalance ?? '0');
    const deferredPlanAmount = parseFloat(statement.deferredPlanAmount ?? '0');
    const feesAndCharges = parseFloat(statement.feesAndCharges ?? '0');
    console.log(
      'total',
      // total,
      statement.openingBalance,
      statement.carryOverBalance,
      statement.deferredPlanAmount,
      statement.feesAndCharges,
      // await this.aggBalance(statementId),
      // await this.interest(statementId, statement.userId),
    );
    const total =
      aggBalance +
      interest +
      carryOverBalance +
      deferredPlanAmount +
      feesAndCharges;
    return {
      total,
      interest,
      aggBalance,
      carryOverBalance,
      deferredPlanAmount,
      feesAndCharges,
    };
  }

  /**
   * Calculates the total credit amount for a statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the total credit amount
   */
  public async totalCredit(statementId: string): Promise<number> {
    return await this.personalCardATransactionsRepository.sum('amount' as any, {
      statementId,
      type: 'credit',
      status: 'success',
    });
  }

  /**
   * Calculates the net outstanding balance by subtracting total credits from total outstanding.
   * A negative result indicates more credit transactions than debits.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the net outstanding balance
   */
  public async netOutstanding(statementId: string): Promise<number> {
    return (
      (await this.totalOutstanding(statementId)).total -
      (await this.totalCredit(statementId))
    );
  }

  /**
   * Calculates the aggregate balance of all user spending transactions.
   * @param statementId - The unique identifier of the statement
   * @param ignorePending - Whether to exclude pending transactions from the calculation
   * @returns Promise containing the aggregate balance
   */
  public async aggBalance(
    statementId: string,
    ignorePending = false,
  ): Promise<number> {
    // console.log('aggBalance', statementId);
    // agg balance is the amount actually spent by the user, ensure you add filters as more categories are added to include transactions/amounts spent by user, rather incurred
    return await this.personalCardATransactionsRepository.sum('amount' as any, {
      statement: { id: statementId },
      type: 'debit',
      status: ignorePending ? 'success' : In(['success', 'pending']),
      category: In([
        'transfer',
        'trips',
        'bolt_ride',
        'checkout',
        'wallet_transaction',
        'transfer',
        'bills',
        'virtual_card',
        // 'repayment_outstanding',
      ]),
    });
  }

  /**
   * Calculates the carryover balance from previous statements.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the carryover balance
   */
  public async carryoverBalance(statementId: string): Promise<number> {
    console.log('aggBalance', statementId);
    // agg balance is the amount actually spent by the user, ensure you add filters as more categories are added to include transactions/amounts spent by user, rather incurred
    return await this.personalCardATransactionsRepository.sum('amount' as any, {
      statement: { id: statementId },
      type: 'debit',
      status: In(['success', 'pending']),
      category: In(['repayment_outstanding']),
    });
  }

  /**
   * Calculates the interest amount based on the user's plan and spending.
   * @param statementId - The unique identifier of the statement
   * @param userId - The unique identifier of the user
   * @returns Promise containing the calculated interest amount
   */
  public async interest(
    statementId: string,
    userId: string,
    aggBalance?: number,
  ): Promise<number> {
    const activeUserPlan = await this.userPlansRepository.findOne({
      where: {
        user: { id: userId },
        status: 'active',
      },
    });
    const spend =
      (aggBalance ?? (await this.aggBalance(statementId, true)) ?? 0) +
      ((await this.carriedOverTransactions(statementId)) ?? 0);
    // console.log('spend', spend, 'interest', activeUserPlan);

    return (activeUserPlan.interest * spend) / 100;
  }

  /**
   * Retrieves the sum of carried over transactions.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing the sum of carried over transactions
   */
  public async carriedOverTransactions(statementId: string) {
    return await this.personalCardATransactionsRepository.sum('amount' as any, {
      statement: { id: statementId },
      // type: 'credit',
      carriedOver: true,
      status: 'success',
    });
  }

  /**
   * Retrieves all clearance transactions for a statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of clearance transactions
   */
  public clearanceTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        type: 'credit',
        category: 'repayment',
      },
    });
  }

  /**
   * Retrieves all repayment transactions for a statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of repayment transactions
   */
  public repaymentTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        //   type: 'credit',
        category: In(['repayment', 'admin_repayment']),
      },
    });
  }

  /**
   * Retrieves all successful repayment transactions for a statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of successful repayment transactions
   */
  public successfulRepaymentTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        //   type: 'credit',
        status: 'success',
        category: In(['repayment', 'admin_repayment']),
      },
    });
  }

  /**
   * Retrieves all pending repayment transactions for a statement.
   * @param statementId - The unique identifier of the statement
   * @returns Promise containing an array of pending repayment transactions
   */
  public pendingRepaymentTransactions(statementId: string) {
    return this.personalCardATransactionsRepository.find({
      where: {
        statement: { id: statementId },
        //   type: 'credit',
        status: 'pending',
        category: In(['repayment', 'admin_repayment']),
      },
    });
  }

  /**
   * Creates a new transaction record in the database.
   * @param manager - The TypeORM entity manager for database operations
   * @param statement - The statement to associate the transaction with
   * @param payload - The transaction details including amount, reference, category, type, and description
   * @param metadata - Additional metadata for the transaction
   * @returns Promise containing the created transaction record
   */
  public createTransaction(
    manager: EntityManager,
    statement: PersonalAccountStatements,
    payload: {
      amount: number;
      reference: string;
      category: string;
      type: 'credit' | 'debit';
      description: string;
      // status = STATUS.PENDING,
    },
    metadata = {},
  ) {
    return manager.save(PersonalCardTransactions, {
      personalCardAccountsId: statement.accountId,
      userId: statement.userId,
      statementId: statement.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      date: new Date().toISOString(),
      amount: payload.amount + '',
      description: payload.description,
      category: payload.category,
      status: STATUS.PENDING,
      transactionReference: payload.reference,
      condition: CONDITION.UNPAID,
      statementStatus: 'open',
      type: payload.type,
      metadata: metadata,
    });
  }

  /**
   * Calculates the end date for a statement based on the start date and billing day.
   * @param newStartDate - The start date of the statement
   * @param billingDay - The day of the month when billing occurs (defaults to 28)
   * @returns Promise containing the calculated end date
   */
  async endDate(
    newStartDate: DateTime,
    billingDay: number | null = null,
  ): Promise<DateTime> {
    // Default to 28th if no billing day is provided
    billingDay = billingDay ?? 28;

    // Clone the start date to avoid mutability issues and set the billing day
    let endDate = newStartDate.set({ day: billingDay });

    // If the end date is earlier than or too close (<= 5 new_statement_generation_days_limit), move to next month
    const diffInDays = newStartDate.diff(endDate, 'days').days;
    const newStatementGenerationDaysLimit = parseInt(
      (
        await this.configurationRepository.findOneBy({
          name: 'new_statement_generation_days_limit',
        })
      ).value ?? '9',
    );

    if (
      endDate <= newStartDate ||
      diffInDays <= newStatementGenerationDaysLimit
    ) {
      endDate = endDate.plus({ months: 1 }).startOf('month');
    }

    // Set time to 00:00:00
    endDate = endDate.startOf('day');

    return endDate;
  }

  /**
   * Generates a new statement for a user's credit card account.
   * @param account - The credit card account
   * @param profile - The user's profile
   * @param manager - The TypeORM entity manager for database operations
   * @returns Promise containing the newly created statement
   */
  async generateStatement(
    account: PersonalCardAccounts,
    profile: UserProfiles,
    manager: EntityManager,
  ) {
    const billingDay = profile.billingDate;
    // $lastStatement = $statement ?? $account->statements()->latest()->first();
    const newStartDate = DateTime.now();
    const newEndDate = await this.endDate(newStartDate, billingDay);

    const statement = await manager.save(PersonalAccountStatements, {
      userId: account.userId,
      accountId: account.id,
      startDate: newStartDate.toSQLDate(),
      endDate: newEndDate.toSQLDate(),
      condition: CONDITION.UNPAID,
      createdAt: new Date(),
      updatedAt: new Date(),
      outstandingStatementId: null,
      interest: '0.0',
      openingBalance: '0.0',
      carryOverBalance: '0.0',
      aggBalance: '0.0',
      totalOutstanding: '0.0',
      feesAndCharges: null,
    });

    return statement;
  }

  // public  deferredPlan(): HasOne
  // {
  //     return $this->hasOne(PersonalDeferredPlanPayment::class, 'statement_id');
  // }

  // public  unpaidDeferredPlan(): HasOne
  // {
  //     return $this->deferredPlan()->unpaidStatus();
  // }

  // public  rewardWalletTransaction(): HasMany
  // {
  //     return $this->hasMany(RewardWalletTransaction::class, 'statement_id');
  // }

  // public  comments(): MorphMany
  // {
  //     return $this->morphMany(Comment::class, 'commentable');
  // }

  // public  setTotalOutstandingAttribute($value): void
  // {
  //     $this->attributes['total_outstanding'] = roundUpValue($value);
  // }

  // public  setPaymentsAttribute($value): void
  // {
  //     $this->attributes['payments'] = roundUpValue($value);
  // }

  // public  setAggBalanceAttribute($value): void
  // {
  //     $this->attributes['agg_balance'] = roundUpValue($value);
  // }

  // public  isUnpaid(): bool
  // {
  //     return $this->{'condition'} === static::UNPAID;
  // }
  // }
}
