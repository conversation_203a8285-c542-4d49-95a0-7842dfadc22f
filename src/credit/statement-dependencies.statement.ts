// // TStatementDependencies.ts

// import { BadRequestException } from "@nestjs/common";
// import moment, { Moment } from "moment";
// import { PersonalCardTransactions } from "src/config/entities/PersonalCardTransactions";


//  export enum Categories {
//   "TRANSFER" = "transfer",
//   "INTEREST" = "interest",
//   "REPAYMENT" = "repayment",
//   "ADMIN_REPAYMENT" = "admin_repayment",
//   "REPAYMENT_OUTSTANDING" = "repayment_outstanding",
//   "CREDIT" = "credit",
//   "BILLS" = "bills",
//   "VIRTUAL_CARD" = "virtual_card"
//  }


//  export enum PaymentVendor {
//   PAYSTACK = 'paystack',
//   KORAPAY = 'korapay',
// }
 
// // Also assume you have a Sequelize (or similar) instance available if needed
// // and that your ORM supports methods such as findAll, count, update, etc.

// export class TStatementDependencies {
//  /**
//    * Format a given date/time string to a UTC moment instance.
//    * @param dateTime - A date string, Date object, or moment instance.
//    * @returns A moment instance in UTC.
//    */
//  static formatToCorrectTime(dateTime: string | Date | Moment): Moment {
//   return moment.utc(dateTime).clone();
// }

// /**
//  * Calculate the first end date given a billing day.
//  * @param billingDay - The day of the month for billing.
//  * @returns A moment instance representing the end date.
//  */
// static firstEndDate(billingDay: number): Moment {
//   let endDate = moment().startOf('day');
//   endDate.date(billingDay);
//   // If the computed endDate is earlier than or equal to now OR
//   // the difference in days is less than the limit, move it to next month.
//   if (
//     endDate.isSameOrBefore(moment()) ||
//     endDate.diff(moment(), 'days') < Configuration.newStatementGenerationDaysLimit()
//   ) {
//     endDate.add(1, 'month');
//   }
//   endDate.subtract(1, 'second');
//   return endDate;
// }

// /**
//  * Calculate the start date based on the last statement's end date.
//  * @param lastStatementEndDate - The end date of the previous statement.
//  * @returns A moment instance representing the start date (one second after the last end date).
//  */
// static startDate(lastStatementEndDate: string | Date | Moment): Moment {
//   return moment.utc(lastStatementEndDate).clone().add(1, 'second');
// }

// /**
//  * Calculate the new statement's end date given a new start date and billing day.
//  * @param newStartDate - The new statement's start date.
//  * @param billingDay - The day of the month for billing.
//  * @returns A moment instance representing the new statement's end date.
//  */
// static endDate(newStartDate: Moment, billingDay: number): Moment {
//   let endDate = moment()
//     .startOf('day')
//     .year(newStartDate.year())
//     .month(newStartDate.month())
//     .date(billingDay);
//   if (
//     endDate.isSameOrBefore(newStartDate) ||
//     endDate.diff(newStartDate, 'days') < Configuration.newStatementGenerationDaysLimit()
//   ) {
//     endDate.add(1, 'month');
//   }
//   endDate.subtract(1, 'second');
//   return endDate;
// }

// /**
//  * Calculate the salary date for a given statement.
//  * @param statement - An object representing the statement; it is assumed to have user and profile properties.
//  * @returns A moment instance representing the salary date.
//  */
// static salaryDate(statement: any): Moment {
//   const salaryDay: number = statement.user.profile.salary_day;
//   const statementEndDate = moment(statement.end_date);
//   let salaryDateObj = moment();
//   salaryDateObj.year(statementEndDate.year());
//   salaryDateObj.month(statementEndDate.month());
//   salaryDateObj.date(salaryDay);
//   if (salaryDateObj.isBefore(statementEndDate)) {
//     salaryDateObj.add(1, 'month');
//   }
//   return salaryDateObj;
// }

// /**
//  * Get the billing start date for a statement.
//  * @param statement - An object with a start_date property.
//  * @returns A moment instance representing the billing start date in UTC.
//  */
// static billingStartDate(statement: any): Moment {
//   return moment.utc(statement.start_date);
// }

// /**
//  * Get the billing end date for a statement.
//  * @param statement - An object with an end_date property.
//  * @returns A moment instance representing the billing end date in UTC.
//  */
// static billingEndDate(statement: any): Moment {
//   return moment.utc(statement.end_date);
// }

// /**
//  * Calculate the grace end date by adding the payment day difference (in days) to the billing end date.
//  * @param billingEndDate - The billing end date.
//  * @param paymentDayDiff - The number of days to add.
//  * @returns A moment instance representing the grace end date.
//  */
// static graceEndDate(billingEndDate: Moment, paymentDayDiff: number): Moment {
//   return moment(billingEndDate).clone().add(paymentDayDiff, 'days').add(1, 'second');
// }

// /**
//  * Calculate the payment date by adding the payment day difference (in days) to the billing end date.
//  * @param billingEndDate - The billing end date.
//  * @param paymentDayDiff - The number of days to add (default is 0).
//  * @returns A moment instance representing the payment date.
//  */
// static paymentDate(billingEndDate: Moment, paymentDayDiff: number = 0): Moment {
//   return moment(billingEndDate).clone().add(paymentDayDiff, 'days').add(1, 'second');
// }

// /**
//  * Determine if the payment due date has been reached.
//  * @param paymentDate - The payment due date.
//  * @returns True if the current time is on or after the payment date.
//  */
// static paymentDueStatus(paymentDate: Moment): boolean {
//   return moment().isSameOrAfter(paymentDate);
// }

// /**
//  * Determine if the grace due date has been reached.
//  * @param graceDueDate - The grace due date.
//  * @returns True if the current time is on or after the grace due date.
//  */
// static graceDueStatus(graceDueDate: Moment): boolean {
//   return moment().isSameOrAfter(graceDueDate);
// } 

//   /**
//    * Check if a user has a billing date (and salary day) set.
//    */
//   static abortIfUserHasNoBillingDate(user: any): void {
//     const billingDate = user.profile?.billing_date;
//     const salaryDay = user.profile?.salary_day;
//     if (!billingDate || !salaryDay) {
//       throw new BadRequestException(
//         'User profile does not have billing date',
//         // 412
//       );
//     }
//   }

  
//   /**
//    * Rounds up the given value.
//    * Adjust this helper if you need to round to a specific decimal place.
//    * @param value - The value to round up.
//    * @returns The rounded-up value.
//    */
//   static   roundUpValue(value: number): number {
//     return Math.ceil(value);
//   }
  
//   /**
//    * Calculates the processing fee for Paystack.
//    * @param amount - The base amount.
//    * @returns The calculated Paystack processing fee.
//    */
//   static   calculatePaystackProcessingFee(amount: number): number {
//     // Retrieve processing fee percentage from configuration (default 1.78%)
//     const percentage =
//       (Configuration.value('processing_fee_percentage', 1.78) as number) / 100;
//     if (percentage === 0) {
//       return 0;
//     }
//     // Calculate the total fee such that amount is the net after deducting fee
//     let totalFee = amount / (1 - percentage);
  
//     // If total fee meets or exceeds threshold, add a fixed fee
//     if (totalFee >= 2500) {
//       totalFee += 100;
//     }
  
//     let paystackFee = totalFee - amount;
//     if (paystackFee > 2000) {
//       paystackFee = 2000;
//     }
  
//     return paystackFee;
//   }
  
//   /**
//    * Calculates the processing fee for Korapay.
//    * @param amount - The base amount.
//    * @returns The calculated Korapay processing fee.
//    */
//   static   calculateKorapayProcessingFee(amount: number): number {
//     return 0.02 * amount;
//   }
  
//   /**
//    * Calculates the processing fee for the given amount and vendor.
//    * @param amount - The base amount.
//    * @param vendor - The payment vendor (default is PaymentVendor.PAYSTACK).
//    * @returns The rounded-up processing fee.
//    */
//   static     calculateProcessingFee(
//     amount: number,
//     vendor: PaymentVendor = PaymentVendor.PAYSTACK
//   ): number {
//     let processingFee = 0;
  
//     switch (vendor) {
//       case PaymentVendor.PAYSTACK:
//         processingFee = TStatementDependencies.calculatePaystackProcessingFee(amount);
//         break;
//       case PaymentVendor.KORAPAY:
//         processingFee = TStatementDependencies.calculateKorapayProcessingFee(amount);
//         break;
//     }
  
//     return TStatementDependencies.roundUpValue(processingFee);
//   }
  
//   /**
//    * Throw if the given statement is null.
//    */
//   static abortIfStatementDoesntExist(statement: any): void {
//     if (!statement) {
//       throw new BadRequestException('Statement does not exist', 412);
//     }
//   }

//   /**
//    * Check that a statement’s account is balanced. If not, log and create an issue record.
//    */
//   static async accountIsBalanced(statement: any): Promise<void> {
//     // Ensure personalAccount is loaded
//     // (Assume getUnpaidTransactions is a method returning a Promise with the array of transactions.)
//     const unpaidTransactions: any[] = await statement.getUnpaidTransactions({
//       where: { status: { $ne: 'failed' }, category: { $ne: Categories.INTEREST } }
//     });
//     const sumOfUnpaid = unpaidTransactions.reduce(
//       (sum, tx) = sum + tx.amount,
//       0
//     );
//     const ab = statement.personalAccount.available_balance;
//     const ac = statement.personalAccount.available_credit;
//     const ccl = statement.personalAccount.credit_card_limit;

//     const balanced =
//       Math.floor(ab) === Math.floor(sumOfUnpaid) && ab + ac === ccl;

//     if (!balanced) {
//       const userId = statement.user_id;
//       const message = `UserID-${userId} | StatementID-${statement.id} is not adding up`;
//       const identifier = CreditCardIssue.issueIdentifierUsingStatement(
//         statement,
//         'not_adding_up'
//       );
//       const initiator = authUserId() || User.CRON_ID;
//       await CreditCardIssue.firstOrCreate(
//         { identifier },
//         { initiator_id: initiator, issue: message, status: Enum.PENDING }
//       );
//       console.info(message);
//       slackTheError(message);
//       throw new BadRequestException(
//         'Repayment imbalanced',
//         412
//       );
//     }
//   }

//   /**
//    * Abort if the account’s available balance or credit limit is negative.
//    */
//   static abortIfAccountVariablesIsNegative(statement: any): void {
//     const ab = statement.personalAccount.available_balance;
//     const ccl = statement.personalAccount.credit_card_limit;

//     if (ab < 0 || ccl < 0) {
//       const accountId = statement.personalAccount.id;
//       const userId = statement.user.id;
//       // slackTheError(
//       //   `AccountID ${accountId} and UserID ${userId} is in negative. Please look into it.`
//       // );
//       throw new BadRequestException('Account not balanced', 412);
//     }
//   }

//   /**
//    * Abort if the statement does not have at least the minimum number of successful debit transactions.
//    */
//   static async abortIfHasNoMinimumSuccessfullDebitTransactions(
//     statement: any,
//     minimumDebitTransactions: number = 1
//   ): Promise<void> {
//     // Assume debitTransactions returns a query builder with a count() method.
//     const debitCount = await statement
//       .debitTransactions({ where: { status: PCT.STATUS['5'] } })
//       .count();
//     if (debitCount < minimumDebitTransactions) {
//       throw new BadRequestException('Minimum debits not met', 412);
//     }
//   }

//   /**
//    * Get a flat list of unpaid transaction IDs for a given user’s statement.
//    */
//   static async transactionIds(statement: any): Promise<number[]> {
//     const statements = await PersonalAccountStatement.findAll({
//       where: { user_id: statement.user_id, condition: Enum.UNPAID },
//       include: ['unpaidTransactions']
//     });
//     return statements.flatMap((stmt: any) =>
//       stmt.unpaidTransactions.map((tx: any) = tx.id)
//     );
//   }

//   /**
//    * Get a flat list of default charge IDs for a statement.
//    */
//   static async defaultChargeIds(statement: any): Promise<number[]> {
//     const statements = await PersonalAccountStatement.findAll({
//       where: { user_id: statement.user_id, condition: Enum.UNPAID },
//       include: ['unpaidDefaultCharge']
//     });
//     let ids: number[] = [];
//     for (const stmt of statements) {
//       let defaultChargeIds = (
//         await stmt.getUnpaidDefaultCharge({ where: { report_statement_id: null } })
//       ).map((dc: any) = dc.id);
//       if (stmt.fees_and_charges > 0 && defaultChargeIds.length === 0) {
//         defaultChargeIds = (
//           await stmt.getUnpaidReportPersonalDefaultCharge()
//         ).map((dc: any) = dc.id);
//       }
//       ids.push(...defaultChargeIds);
//     }
//     return ids;
//   }

//   /**
//    * Get deferred plan IDs for a statement.
//    */
//   static async deferredPlanIds(statement: any): Promise<number[]> {
//     const statements = await PersonalAccountStatement.findAll({
//       where: { user_id: statement.user_id, condition: Enum.UNPAID },
//       include: ['unpaidDeferredPlan']
//     });
//     return statements.flatMap((stmt: any) =>
//       stmt.unpaidDeferredPlan.map((dp: any) = dp.id)
//     );
//   }

//   /**
//    * Get outstanding statement IDs.
//    */
//   static async outstandingStatementIds(statement: any): Promise<number[]> {
//     const statements = await PersonalAccountStatement.findAll({
//       where: { user_id: statement.user_id, condition: Enum.UNPAID },
//       attributes: ['id', 'outstanding_statement_id'],
//       include: [
//         {
//           association: 'unpaidOutstanding',
//           attributes: ['id', 'outstanding_statement_id']
//         }
//       ]
//     });
//     return statements.flatMap((stmt: any) =>
//       stmt.unpaidOutstanding.map((os: any) = os.id)
//     );
//   }

//   /**
//    * Return the cumulative aggregate balance for unpaid statements.
//    */
//   static async unpaidStatementCumulativeAggBalances(
//     statement: any
//   ): Promise<number> {
//     // Example using Sequelize functions (adjust as needed)
//     const result = await PersonalAccountStatement.findOne({
//       where: { user_id: statement.user_id, condition: Enum.UNPAID },
//       attributes: [
//         [/* your sum function for agg_balance */, 'aggSum'],
//         [/* your sum function for carry_over_balance */, 'carrySum']
//       ]
//     });
//     // Adjust property access according to your ORM
//     const total = result ? (result.get('aggSum') + result.get('carrySum')) : 0;
//     return Number(total) || 0;
//   }

//   /**
//    * Recursively sum previous statements’ aggregate balances.
//    */
//   static async sumOfPreviousAggBalances(statement: any): Promise<number> {
//     const previousStatement = (await statement.getUnpaidOutstandingStatement())[0];
//     if (previousStatement) {
//       return previousStatement.agg_balance + (await this.sumOfPreviousAggBalances(previousStatement));
//     }
//     return 0;
//   }

//   static async fromCurrentToPreviousAggBalance(
//     statement: any
//   ): Promise<number> {
//     const currentAgg = statement.agg_balance;
//     const previousAgg = await this.sumOfPreviousAggBalances(statement);
//     return currentAgg + previousAgg;
//   }

//   /**
//    * Abort if the statement is not the latest for the account.
//    */
//   static abortIfNotLatestStatement(statement: any): void {
//     const latestStatement = statement.personalAccount.latestStatement;
//     if (latestStatement.id !== statement.id) {
//       throw new BadRequestException('Statement already reported', 412);
//     }
//   }

//   /**
//    * Calculate the total outstanding amount.
//    */
//   static totalOutstanding(
//     outstanding: any | null,
//     aggBalance: number,
//     planAmount: number,
//     interest: number
//   ): number {
//     return (outstanding?.total_outstanding || 0) + aggBalance + planAmount + interest;
//   }

//   /**
//    * Return the statement if it is unpaid or partially paid; otherwise null.
//    */
//   static outstandingStatement(lastStatement: any): any | null {
//     return (lastStatement.condition === Enum.UNPAID ||
//       lastStatement.condition === Enum.PART_PAID)
//       ? lastStatement
//       : null;
//   }

//   /**
//    * Return a deferred plan query for an account between two dates.
//    */
//   static deferredPlanQuery(account: any, newStartDate: Date, newEndDate: Date): any {
//     return this.anyDeferredPlanQuery(account) ?? this.deferredPlanBetweenDatesQuery(account, newStartDate, newEndDate);
//   }

//   static deferredPlanBetweenDatesQuery(account: any, newStartDate: Date, newEndDate: Date): any {
//     return account.user.deferredPlans()
//       .betweenCreatedDates(newStartDate, newEndDate)
//       .unpaidStatus()
//       .open();
//   }

//   static anyDeferredPlanQuery(account: any): any {
//     return account.user.deferredPlans()
//       .unpaidStatus()
//       .open();
//   }

//   /**
//    * Get an unpaid default charge for an account between two dates.
//    */
//   static async unpaidDefaultCharge(account: any, newStartDate: Date, newEndDate: Date): Promise<any | null> {
//     return await account.defaultCharges()
//       .betweenCreatedDates(newStartDate, newEndDate)
//       .unpaid()
//       .latest()
//       .first();
//   }

//   /**
//    * Update an existing default charge.
//    */
//   static async updateExistingDefaultCharge(defaultCharge: any, amount: number, defaultDays: number): Promise<any> {
//     defaultCharge.amount = amount;
//     defaultCharge.days = defaultDays;
//     await defaultCharge.save();
//     return defaultCharge;
//   }

//   /**
//    * Update the statement’s default charge and then cascade updates to the next statement.
//    */
//   static async updateDefaultChargeStatement(statement: any, defaultCharge: any): Promise<void> {
//     const amount = defaultCharge.amount;
//     statement.fees_and_charges = amount;
//     statement.total_outstanding =
//       statement.opening_balance +
//       statement.carry_over_balance +
//       statement.agg_balance +
//       statement.interest +
//       statement.fees_and_charges +
//       statement.deferred_plan_amount;
//     await statement.save();
//     await this.updateNextStatement(statement);
//   }

//   static async updateNextStatement(statement: any): Promise<void> {
//     const nextStatement = (await statement.getUnpaidReportingStatement())[0];
//     if (nextStatement) {
//       nextStatement.opening_balance = statement.total_outstanding;
//       nextStatement.total_outstanding =
//         nextStatement.opening_balance +
//         nextStatement.carry_over_balance +
//         nextStatement.agg_balance +
//         nextStatement.interest +
//         nextStatement.fees_and_charges +
//         nextStatement.deferred_plan_amount;
//       await nextStatement.save();
//       await this.updateNextStatement(nextStatement);
//     }
//   }

//   /**
//    * Return a query for the aggregate balance (transactions) on an account.
//    */
//   static anyAggBalanceQuery(account: any): any {
//     return account.transactions().unpaid().successful().open();
//   }

//   static aggBalanceBetweenDateQuery(account: any, newStartDate: Date, newEndDate: Date): any {
//     return account.transactions()
//       .betweenCreatedDates(newStartDate, newEndDate)
//       .where('wallet_type', PCT.WALLET_TYPE.CREDPAL_CARD)
//       .unpaid()
//       .successful()
//       .open();
//   }

//   static carriedOverAggBalanceBetweenDateQuery(account: any, newStartDate: Date, newEndDate: Date): any {
//     return account.transactions()
//       .betweenCreatedDates(newStartDate, newEndDate)
//       .where('wallet_type', PCT.WALLET_TYPE.CREDPAL_CARD)
//       .unpaid()
//       .successful()
//       .open()
//       .carriedOver();
//   }

//   /**
//    * Calculate interest based on an account’s rate and an aggregate balance.
//    */
//     interest(account: any, aggBalance: number): number {
//     const interestRate =
//       account.user.activeUserPlan?.interest ?? account.user.uniquePlan?.interest ?? 0;
//     return aggBalance * (interestRate / 100);
//   }

//   /**
//    * Recursively determine the latest statement.
//    */
//     async figureOutLatestStatement(account: any, count: number): Promise<any | null> {
//     if ((await account.statements().count()) === 0) {
//       await this.generateFirstStatement(account.user.id);
//     }
//     let lastStatement = await account.statements().recent().first();
//     const lastEndDate = new Date(lastStatement.end_date);
//     if (lastEndDate < new Date()) {
//       await this.generateStatement(account);
//       if (count < 12) {
//         await new Promise(resolve = setTimeout(resolve, 1000)); // sleep 1 second
//         count++;
//         return await this.figureOutLatestStatement(account, count);
//       }
//     }
//     return await account.statements().recent().first();
//   }

//   /**
//    * Determine the appropriate statement for a resource (such as a transaction).
//    */
//   static async figureOutStatement(resource: any): Promise<any> {
//     const resourceDate = new Date(resource.created_at);
//     let statement = await resource.dynamicallyLoadStatements();
//     if (!statement) {
//       const userId = (await resource.dynamicallyLoadUser()).id;
//       statement = await this.generateFirstStatement(userId);
//     }
//     const startDate = new Date(statement.start_date);
//     const endDate = new Date(statement.end_date);
//     if (!(resourceDate >= startDate && resourceDate <= endDate)) {
//       statement = await this.generateLatestStatement(statement.user.id);
//     }
//     return statement;
//   }

//   /**
//    * Recursively update the report statement payment.
//    */
//   static async updateReportStatementPayment(statement: any): Promise<void> {
//     const reportStatement = await this.getNextStatement(statement);
//     if (reportStatement) {
//       await reportStatement.reload();
//       await this.updateReportStatementPayment(reportStatement);
//     }
//   }

//   /**
//    * Update the next statement (and its related transactions) with payment data.
//    */
//   static async updateNextStatementAndPayment(statement: any): Promise<void> {
//     const nextStatement = await this.getNextStatement(statement);
//     if (nextStatement) {
//       nextStatement.outstanding_statement_id = statement.id;
//       nextStatement.opening_balance = statement.total_outstanding;
//       nextStatement.total_outstanding =
//         statement.total_outstanding +
//         nextStatement.carry_over_balance +
//         nextStatement.agg_balance +
//         nextStatement.fees_and_charges +
//         nextStatement.interest +
//         nextStatement.deferred_plan_amount;
//       nextStatement.condition = Enum.UNPAID;
//       await nextStatement.save();
//       await nextStatement.reload();
//       const debitTx = await nextStatement.debitTransactions();
//       if (debitTx) {
//         await Promise.all(debitTx.map((tx: any) = tx.update({ condition: Enum.UNPAID })));
//       }
//       const clearanceTx = await nextStatement.clearanceTransactions();
//       if (clearanceTx) {
//         await Promise.all(
//           clearanceTx.map((tx: any) = tx.update({ amount: 0.0, status: Enum.REVERSED }))
//         );
//       }
//       if (nextStatement.deferredPlan) {
//         await nextStatement.deferredPlan.update({ condition: Enum.UNPAID });
//       }
//       if (nextStatement.defaultCharge) {
//         await nextStatement.defaultCharge.update({ condition: Enum.UNPAID });
//       }
//       await this.updateNextStatementAndPayment(nextStatement);
//     }
//   }

//   /**
//    * Get the “next” statement after the current one.
//    */
//   static async getNextStatement(statement: any): Promise<any | null> {
//     const collection = await PersonalAccountStatement.findAll({
//       where: {
//         user_id: statement.user_id,
//         id: { $gte: statement.id },
//         condition: { $ne: Enum.REVERSED }
//       },
//       order: [['createdAt', 'ASC']]
//     });
//     if (collection.length > 1) {
//       // Skip the current statement.
//       return collection[1];
//     }
//     return null;
//   }

//   /**
//    * Calculate the default charge.
//    */
//   static calculateDefaultCharge(aggBalance: number, defaultDays: number): number {
//     return aggBalance * defaultDays * Configuration.defaultRate();
//   }

//   /**
//    * Return the plan amount from a deferred plan.
//    */
//   static planAmount(deferredPlan: any | null): number {
//     return deferredPlan ? (deferredPlan.uniquePlan?.fee ?? deferredPlan.plan?.default_fee ?? 0) : 0;
//   }

//   /**
//    * Abort if the statement is already being charged.
//    */
//   static async abortIfStatementIsAlreadyBeingCharged(statement: any, editorId: number | null = null): Promise<void> {
//     if (editorId) {
//       const pendingRepayments = await statement
//         .creditCardPendingRepayment()
//         .where({ status: Enum.INITIATED })
//         .count();
//       const roles: string[] = (await User.findByPk(editorId))?.roles?.map((role: any) = role.slug) || [];
//       const isSuper = roles.includes('super_admin') || roles.includes('super_finance');
//       if (isSuper && pendingRepayments > 0) {
//         return;
//       }
//     }
//     if (statement.personalAccount.ongoing_charge) {
//       throw new BadRequestException('Ongoing charge in progress', 412);
//     }
//   }

//   /**
//    * Check if the user already has a statement; if so, update it with deferred plan details.
//    */
//   static async checkIfUserAlreadyHasStatement(user: any, deferredPlan: any | null): Promise<any | null> {
//     if (user.statements && user.statements.length > 0) {
//       const statement = (await user.getStatements({ order: [['createdAt', 'DESC']], limit: 1 }))[0];
//       const deferredPlanFee = deferredPlan ? (deferredPlan.uniquePlan?.fee ?? 0) : 0;
//       statement.condition = deferredPlanFee > 0 ? Enum.UNPAID : Enum.PAID;
//       statement.deferred_plan_amount = deferredPlanFee;
//       statement.total_outstanding = statement.total_outstanding + deferredPlanFee;
//       await statement.save();
//       return statement;
//     }
//     return null;
//   }

//   /**
//    * Create a deferred plan for a user if none exists at the time of the first statement.
//    */
//   static async createOneIfNoDeferredPlanAtFirstStatement(
//     deferredPlanPayment: any | null,
//     user: any
//   ): Promise<any | null> {
//     if (!deferredPlanPayment && (!user.deferredPlans || user.deferredPlans.length === 0)) {
//       const plans = await CardPlanService.accessiblePlans(user);
//       const firstPlan = plans[0];
//       return await CardPlanService.createDeferredPlanForAUserWithoutOne(user, firstPlan);
//     }
//     return deferredPlanPayment;
//   }

//   /**
//    * Abort if the user does not have an account.
//    */
//   static abortIfUserHasNoAccount(user: any): void {
//     if (!user.personalAccount) {
//       throw new BadRequestException('User has no credit card account', 412);
//     }
//   }

//   /**
//    * Check if the user’s last transaction is more than 45 days ago.
//    */
//   static isUserLastTransactionMoreThan45DaysAgo(transaction: any): boolean {
//     const transactionDate = new Date(transaction.updated_at);
//     const diffDays = Math.floor((new Date().getTime() - transactionDate.getTime()) / (1000 * 60 * 60 * 24));
//     return diffDays > 45;
//   }

//   /**
//    * Generate a file name for a statement PDF.
//    */
//   static fileName(statement: any): string {
//     const user = statement.user;
//     return `${user.name}_${user.last_name}_${statement.id}.pdf`;
//   }

//   // Stub methods that must be defined elsewhere:
//   // generateFirstStatement, generateStatement, generateLatestStatement
//   // – these are assumed to be implemented in another service or module.
// }
