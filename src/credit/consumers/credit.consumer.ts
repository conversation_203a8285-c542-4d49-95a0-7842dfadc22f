import { Injectable, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { CashEvents, Events, Exchanges } from 'src/utils';
import { CreditService } from '../credit.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
@Injectable()
export class CreditConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly creditService: CreditService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.CREDIT}.${CashEvents.REFUND_TRANSACTION}`,
      async ({ data: payload, ack }) => {
        await this.reverseTransaction(payload['reference'], payload['returningRoutingKey'], ack);
      },
    );
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${CashEvents.REVERSE_TRANSACTION}`,
      async ({ data: payload, ack }) => {
        await this.reverseTransaction(payload['reference'],payload['returningRoutingKey'], ack);
      },
    );
  }
  catch(e) {
    console.log(e);
  }

  async reverseTransaction(reference: string, returningRoutingKey: string, ack) {
    console.log(reference);

    try {
      await this.creditService.refundTransaction(reference);
      ack();
    } catch (e) {
      if (
        String(e.message).includes('not found') ||
        String(e.message).includes('already exists') ||
        String(e.message).includes('be refunded')
      ) {
        ack();
      }
      console.log(e);
    } finally {
      this.eventEmitter.emit(Events.SEND_TRANSACTION, {
        transactionReference: reference,
        returningRoutingKey,
      });
    }
  }
}
