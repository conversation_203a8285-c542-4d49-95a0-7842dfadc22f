import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { Events } from 'src/utils';
import { CreditService } from '../credit.service';

@Processor(Events.SYNC_STATEMENT)
export class SyncStatementConsumer extends WorkerHost {
  constructor(private readonly creditService: CreditService) {
    super();
  }
  private readonly logger = new Logger(SyncStatementConsumer.name);

  async process(job: Job) {
    try {
      this.logger.debug(
        `processing job for ${Events.SYNC_STATEMENT}, `,
        job.asJSON(),
      );

      this.creditService.syncStatement(job.data);
    } catch (error) {
      console.log(error);
    }
  }
}
