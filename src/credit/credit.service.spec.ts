import { Test, TestingModule } from '@nestjs/testing';
import { CreditService } from './credit.service';
import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';
import { UserPlansRepository } from './repository/user-plan.repository';
import { UserRepository } from '../user/repository/user.repository';
import { UserProfileRepository } from '../user/repository/user-profile.repository';
import { DocumentsRepository } from './repository/documents.repository';
import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
import { CONDITION, StatementService, STATUS } from './statement.service';
import { PaymentCacheService, PaymentTransactionStatus, PaymentInterface, PaymentTransactionSource, PaymentTransactionType, PaymentTransactionApprovalStatus, PaymentTransactionCategory } from '@crednet/utils';
import { RabbitmqService } from '@crednet/utils';
import { DataSource, EntityManager, UpdateResult } from 'typeorm';
import { ConfigurationRepository } from './repository/configuration.repository';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PersonalCardAccounts } from '../config/entities/PersonalCardAccounts';
import { Users } from '../config/entities/Users';
import { PersonalCardTransactions } from '../config/entities/PersonalCardTransactions';
import { PersonalAccountStatements } from '../config/entities/PersonalAccountStatements';
import { AuthData } from '@crednet/authmanager';
import { UnprocessableEntityException, ForbiddenException } from '@nestjs/common';
import { UserProfiles } from '../config/entities/UserProfiles';
import { Configurations } from '../config/entities/Configurations';
import { DateTime } from 'luxon';
import { UserPlans } from '../config/entities/UserPlans';

describe('CreditService', () => {
  let service: CreditService;
  let personalCardAccountsRepository: jest.Mocked<PersonalCardAccountsRepository>;
  let userRepository: jest.Mocked<UserRepository>;
  let transactionRepository: jest.Mocked<PersonalCardATransactionsRepository>;
  let personalAccountStatementsRepository: jest.Mocked<PersonalAccountStatementsRepository>;
  let statementService: jest.Mocked<StatementService>;
  let paymentCacheService: jest.Mocked<PaymentCacheService>;
  let configurationRepository: jest.Mocked<ConfigurationRepository>;
  let rmqService: jest.Mocked<RabbitmqService>;
  let dataSource: jest.Mocked<DataSource>;
  let eventEmitter: jest.Mocked<EventEmitter2>;
  let userPlansRepository: jest.Mocked<UserPlansRepository>;

  const mockAccount: PersonalCardAccounts = {
    id: 'account1',
    userId: 'user1',
    availableBalance: '0',
    availableCredit: '5000',
    creditCardLimit: '5000',
    status: 'active',
    ongoingCharge: false,
  } as PersonalCardAccounts;

  const mockUserProfile = {
    id: 'profile1',
    userId: 'user1',
    status: 'activated',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as UserProfiles;

  const mockUser = {
    id: 'user1',
    isBlacklisted: false,
    userProfiles: [mockUserProfile],
    createdAt: new Date(),
    updatedAt: new Date(),
  } as Users;

  const mockStatement = {
    id: 1,
    userId: 2,
    accountId: 'account1',
    condition: 'unpaid',
    // startDate: new Date().toISOString(),
    // endDate: new Date().toISOString(),
    interest: '0',
    openingBalance: '0',
    carryOverBalance: '0',
    aggBalance: '0',
    totalOutstanding: '0',
    outstandingStatementId: null,
    maintenanceFeeId: null,
    feesAndCharges: null,
    deferredPlanAmount: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    payments: null,
    discount: null,
    notified: null,
    data: null,
    createdDate: new Date(),
    walletEarned: null,
    walletRedeemed: null,
    walletBalance: null,
    walletOpeningBalance: null,
    deletedAt: null,
  } as unknown as PersonalAccountStatements;

  const mockPayment = {
    userId: '1',
    amount: 100,
    reference: 'ref1',
    // source: PaymentTransactionSource.WALLET,
    description: 'Test payment',
    returningRoutingKey: 'test.key',
    currency: 'USD',
    walletType: 'credit',
    type: PaymentTransactionType.DEBIT,
    approvalStatus: PaymentTransactionApprovalStatus.APPROVED,
    category: PaymentTransactionCategory.TOP_UP,
    meta: {},
  } as unknown as PaymentInterface;

  const mockConfig = {
    id: 'config1',
    configurationGroupId: 1,
    name: 'test_config',
    title: 'Test Config',
    value: '0.5',
    default: '0.5',
    valueType: 'string',
    field: 'test_field',
    editorId: 'test_editor',
    isActive: true,
    isSystem: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as Configurations;

  const mockAuth = {
    id: 1,
    name: 'Test User',
    last_name: 'Test',
    full_name: 'Test User',
    email: '<EMAIL>',
    phone: '**********',
    phone_no: '**********',
    country: 'US',
    currency: 'USD',
    language: 'en',
    transaction_pin: '1234',
    pnd: 0,
    token_identifier: 'test_token',
  } as AuthData;

  const mockTransaction = {
    id: 'transaction1',
    amount: '100',
    status: STATUS.PENDING,
    personalCardAccountsId: 'account1',
    userId: 'user1',
    statementId: 'statement1',
    statement: mockStatement,
    createdAt: new Date(),
    updatedAt: new Date(),
    date: new Date().toISOString(),
    description: 'Test transaction',
    category: 'test',
    type: 'debit' as const,
    transactionReference: 'ref1',
    condition: 'unpaid',
    statementStatus: 'open',
    metadata: {},
    walletId: null,
    walletType: 'credpal_card',
    currency: 'NGN',
    carriedOver: false,
    createdDate: new Date(),
    batch: null,
    lenderId: null,
    deletedAt: null,
  } as unknown as PersonalCardTransactions;

  beforeEach(async () => {
    const mockEntityManager = {
      update: jest.fn(),
      findOne: jest.fn().mockImplementation((entity, options) => {
        if (entity === UserPlans) {
          return Promise.resolve({
            id: 'plan1',
            userId: 'user1',
            originalPlanId: 'original1',
            planName: 'Basic Plan',
            interest: 5,
            fee: '100',
            repaymentPercentage: '100.0000',
            startDate: new Date(),
            endDate: new Date(),
            duration: 365,
            status: 'active',
            changedStatus: null,
            previousPlan: null,
            downgradedAt: null,
            upgradedAt: null,
            deletedAt: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            personalDeferredPlanPayments: [],
          } as UserPlans);
        }
        return Promise.resolve(null);
      }),
      findOneBy: jest.fn(),
      transaction: jest.fn().mockImplementation(cb => cb(mockEntityManager)),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditService,
        {
          provide: PersonalCardAccountsRepository,
          useValue: {
            findOne: jest.fn(),
            findOneBy: jest.fn(),
            save: jest.fn(),
            insert: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: PersonalCardATransactionsRepository,
          useValue: {
            find: jest.fn(),
            save: jest.fn(),
            sum: jest.fn(),
            existsBy: jest.fn(),
          },
        },
        {
          provide: PersonalAccountStatementsRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: StatementService,
          useValue: {
            debitTransactions: jest.fn(),
            creditTransactions: jest.fn(),
            creditTransactionsSum: jest.fn(),
            totalOutstanding: jest.fn(),
            carriedOverTransactions: jest.fn(),
            createTransaction: jest.fn(),
            generateStatement: jest.fn(),
          },
        },
        {
          provide: PaymentCacheService,
          useValue: {
            getPayment: jest.fn(),
          },
        },
        {
          provide: RabbitmqService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            transaction: jest.fn(),
            createEntityManager: jest.fn().mockReturnValue(mockEntityManager),
          },
        },
        {
          provide: ConfigurationRepository,
          useValue: {
            findOneBy: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: UserPlansRepository,
          useValue: {
            findOne: jest.fn(),
            findBy: jest.fn().mockResolvedValue([]),
            insert: jest.fn(),
          },
        },
        {
          provide: UserProfileRepository,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: DocumentsRepository,
          useValue: {
            findOne: jest.fn(),
            insert: jest.fn(),
          },
        },
        {
          provide: 'BullQueue_sync-statement',
          useValue: {
            add: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CreditService>(CreditService);
    personalCardAccountsRepository = module.get(PersonalCardAccountsRepository);
    userRepository = module.get(UserRepository);
    transactionRepository = module.get(PersonalCardATransactionsRepository);
    personalAccountStatementsRepository = module.get(PersonalAccountStatementsRepository);
    statementService = module.get(StatementService);
    paymentCacheService = module.get(PaymentCacheService);
    configurationRepository = module.get(ConfigurationRepository);
    rmqService = module.get(RabbitmqService);
    dataSource = module.get(DataSource);
    eventEmitter = module.get(EventEmitter2);
    userPlansRepository = module.get(UserPlansRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkLocalAccount', () => {
    it('should validate account successfully', async () => {
      const result = await service.checkLocalAccount(mockAccount, mockUser);

      expect(result).toBe(true);
    });

    it('should throw error if user profile is not activated', async () => {
      const inactiveUserProfile = { ...mockUserProfile, status: 'inactive' };
      const inactiveUser = { ...mockUser, userProfiles: [inactiveUserProfile] };

      await expect(service.checkLocalAccount(mockAccount, inactiveUser)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should throw error if user is blacklisted', async () => {
      const blacklistedUser = { ...mockUser, isBlacklisted: true };

      await expect(service.checkLocalAccount(mockAccount, blacklistedUser)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });
  });

  describe('checkBalances', () => {
    it('should validate balances successfully', async () => {
      await expect(service.checkBalances(mockAccount, 100)).resolves.not.toThrow();
    });

    it('should throw error if amount exceeds credit limit', async () => {
      await expect(service.checkBalances(mockAccount, 6000)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should throw error if insufficient funds', async () => {
      const mockAccountWithLowBalance = {
        ...mockAccount,
        availableCredit: '100',
      };
      await expect(service.checkBalances(mockAccountWithLowBalance, 4900)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });
  });

  describe('initiateWalletCharge', () => {
    it('should process wallet charge successfully', async () => {
      paymentCacheService.getPayment.mockResolvedValue(mockPayment);
      transactionRepository.existsBy.mockResolvedValue(false);
      personalCardAccountsRepository.findOneBy.mockResolvedValue(mockAccount);
      userRepository.findOne.mockResolvedValue(mockUser);
      personalAccountStatementsRepository.findOne.mockResolvedValue(mockStatement);
      configurationRepository.findOneBy.mockResolvedValue({
        ...mockConfig,
        value: '0.5',
      });
      const mockUserPlan = {
        id: 'plan1',
        userId: 'user1',
        originalPlanId: 'original1',
        planName: 'Basic Plan',
        interest: 5,
        fee: '100',
        repaymentPercentage: '100.0000',
        startDate: new Date(),
        endDate: new Date(),
        duration: 365,
        status: 'active',
        changedStatus: null,
        previousPlan: null,
        downgradedAt: null,
        upgradedAt: null,
        deletedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        personalDeferredPlanPayments: [],
      } as unknown as UserPlans;
      userPlansRepository.findOne.mockResolvedValue(mockUserPlan);
      statementService.createTransaction.mockResolvedValue(Promise.resolve({
        ...mockTransaction,
        status: STATUS.PENDING,
        condition: CONDITION.UNPAID,
      }));
      // statementService.generateStatement.mockResolvedValue(Promise.resolve({
      //   ...mockStatement,
      //   startDate: DateTime.now().toSQLDate(),
      //   endDate: DateTime.now().toSQLDate(),
      //   condition: CONDITION.UNPAID,
      //   userId: 'user1',
      //   accountId: 'account1',
      // }));

      const result = await service.initiateWalletCharge('ref1', mockAuth);

      expect(result).toBeDefined();
      expect(paymentCacheService.getPayment).toHaveBeenCalledWith('ref1');
      expect(rmqService.send).toHaveBeenCalled();
    });

    it('should throw error if payment not found', async () => {
      paymentCacheService.getPayment.mockResolvedValue(null);

      await expect(service.initiateWalletCharge('ref1', mockAuth)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should throw error if user is not authorized', async () => {
      paymentCacheService.getPayment.mockResolvedValue({
        ...mockPayment,
        userId: 'user2',
      });

      await expect(service.initiateWalletCharge('ref1', mockAuth)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('createCustomerWithCreditLimit', () => {
    it('should create customer with credit limit', async () => {
      personalCardAccountsRepository.findOneBy.mockResolvedValue(null);
      configurationRepository.findOneBy.mockResolvedValue(mockConfig);

      await service.createCustomerWithCreditLimit('user1', '5000');

      expect(personalCardAccountsRepository.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user1',
          creditCardLimit: '5000',
          status: 'active',
        }),
      );
    });

    it('should not create account if user already has one', async () => {
      personalCardAccountsRepository.findOneBy.mockResolvedValue(mockAccount);

      await service.createCustomerWithCreditLimit('user1', '5000');

      expect(personalCardAccountsRepository.insert).not.toHaveBeenCalled();
    });
  });

  describe('syncStatement', () => {
    it('should sync statement data', async () => {
      const statementId = 'statement1';
      const mockStatementForSync = {
        id: statementId,
        userId: 'user1',
        accountId: 'account1',
        condition: 'unpaid',
        startDate: new Date(),
        endDate: new Date(),
        interest: '0',
        openingBalance: '0',
        carryOverBalance: '0',
        aggBalance: '0',
        totalOutstanding: '0',
        outstandingStatementId: null,
        maintenanceFeeId: null,
        feesAndCharges: null,
        deferredPlanAmount: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as unknown as PersonalAccountStatements;

      personalAccountStatementsRepository.findOne.mockResolvedValue(mockStatementForSync);
      statementService.debitTransactions.mockResolvedValue([]);
      statementService.creditTransactions.mockResolvedValue([]);
      statementService.creditTransactionsSum.mockResolvedValue(0);
      statementService.totalOutstanding.mockResolvedValue({
        total: 1000,
        interest: 100,
        aggBalance: 900,
        carryOverBalance: 0,
        deferredPlanAmount: 0,
        feesAndCharges: 0,
      });
      statementService.carriedOverTransactions.mockResolvedValue(0);

     
      personalAccountStatementsRepository.update.mockResolvedValue({ 
        affectedRows: 1,
        raw: [],generatedMaps: []
      } as UpdateResult);

      await service.syncStatement({ statementId, userId: 'user1' });

        
      // expect(statementService.creditTransactionsSum).toHaveBeenCalledWith(statementId);
      expect(statementService.totalOutstanding).toHaveBeenCalledWith(statementId);
      expect(personalAccountStatementsRepository.update).toHaveBeenCalledWith(
        { id: statementId },
        {
          aggBalance: '900',
          carryOverBalance: '0',
          interest: '100',
          totalOutstanding: '1000',
        }
      );
    });
  });
});
