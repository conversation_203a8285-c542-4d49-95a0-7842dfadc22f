// // StatementService.ts

// import moment from 'moment';
// import { v4 as uuidv4 } from 'uuid';
 
// import { BadRequestException, Injectable } from '@nestjs/common';
// import { Categories, TStatementDependencies } from './statement-dependencies.statement';
// import { UserRepository } from 'src/user/repository/user.repository';
// import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
// import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
// import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';

// // --- Enums and Constants ---
// enum Enum {
//   PAID = 'PAID',
//   OPEN = 'OPEN',
//   UNPAID = 'UNPAID',
//   PENDING = 'PENDING',
//   PART_PAID = 'PART_PAID',
//   CLOSE = 'CLOSE',
// }

// export const STATUS: Record<number, string> = {
//   1: 'error',
//   2: 'failed',
//   3: 'pending',
//   4: 'reversed',
//   5: 'success',
// };

// export const STATEMENT_STATUS: Record<number, string> = {
//   1: 'open',
//   2: 'close',
// };

// export const CONDITION: Record<number, string> = {
//   1: 'paid',
//   2: 'unpaid',
//   3: 'part_paid',
// };

// export enum TYPE {
//   DEBIT= 'debit',
//   CREDIT ='credit',
// };
// // const PCT = {
// //   TYPE: ['TYPE1', 'TYPE2'], // Adjust to actual types
// //   CATEGORIES: {
// //     REPAYMENT: 'REPAYMENT',
// //     REPAYMENT_OUTSTANDING: 'REPAYMENT_OUTSTANDING',
// //   },
// //   STATUS: {
// //     '5': 'STATUS5',
// //     '2': 'STATUS2',
// //   },
// //   CONDITION: {
// //     '2': 'CONDITION2',
// //     '3': 'CONDITION3',
// //   },
// // };
  

// @Injectable()
// export class StatementService extends TStatementDependencies {
//   constructor(private readonly userRepository: UserRepository,
//     private readonly personalAccountStatementsRepository: PersonalAccountStatementsRepository,
//     private readonly personalCardATransactionsRepository: PersonalCardATransactionsRepository,
//     private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
//   ) {
//     super();
//   }
//   public    GRACE_DAYS = 0;
//   // Get user statement breakdown at login.
//     async getUserStatementBreakDownAtLogin(userId: number): Promise<any> {
//     const user = await this.userRepository.findOne( {where: {id: userId+''}, select: ['id'] });
//     if (!user) {
//       throw new BadRequestException('Users not found');
//     }
//     const statements = await this.personalAccountStatementsRepository.find({
//       order: {createdAt: 'DESC'},
//       where: {userId: user.id},
//        take: 1,
//       // attributes: ['id'],
//     });
//     let latestStatementId: string | null = statements.length > 0 ? statements[0].id : null;
//     if (!latestStatementId) {
//       const firstStatement = await this.generateFirstStatement(userId);
//       latestStatementId = firstStatement.id;
//     }
//     return await this.getBreakDown(latestStatementId);
//   }

//   // Get user payment breakdown.
//    async userPaymentBreakDown(userId: number): Promise<any> {
//     const user = await t.findByPk(userId, {
//       attributes: ['id'],
//       include: [{ association: 'profile' }],
//     });
//     if (!user) {
//       throw new BadRequestException('Users not found');
//     }
//     let statements = await user.getStatements({
//       order: [['createdAt', 'DESC']],
//       limit: 1,
//     });
//     let latestStatement: any;
//     if (!statements || statements.length === 0) {
//       latestStatement = await this.generateLatestStatement(userId);
//     } else {
//       latestStatement = statements[0];
//     }
//     return await this.getBreakDown(latestStatement.id);
//   }

//   // Get statement breakdown.
//    async getBreakDown(statementId: string): Promise<any> {
//     // const statement = await this.personalAccountStatementsRepository.findOne( {
//     //   where: {id: statementId},
//     //   relations: [
//     //     { association: 'user', include: ['activeUserPlan', 'profile'] },
//     //     { association: 'personalAccount' },
//     //   ],
//     // });
//     // if (!statement) {
//     //   throw new BadRequestException('Statement does not exist');
//     // }
//     // const isPaid = statement.condition === Enum.PAID;
//     // const ongoingCharge = statement.personalAccount.ongoing_charge;
//     // const billingEnd = StatementService.billingEndDate(statement);
//     // const paymentDayDiff =
//     //   typeof statement.user.profile.paymentDayDifference === 'function'
//     //     ? statement.user.profile.paymentDayDifference()
//     //     : 0;
//     // const paymentDueDate = StatementService.paymentDate(billingEnd);
//     // const graceDays = StatementService.GRACE_DAYS || 0;
//     // const graceEnd = StatementService.graceEndDate(billingEnd, paymentDayDiff + graceDays);
//     // const partialPercentage = statement.personalAccount.repayment_percentage;
//     // const paymentDueStatusFlag = !isPaid && StatementService.paymentDueStatus(paymentDueDate);
//     // const graceDueStatusFlag = !isPaid && StatementService.graceDueStatus(graceEnd);

//     // // Update freemium status if applicable.
//     // const creditCard = await PersonalCardAccount.findOne({ where: { user_id: statement.user.id } });
//     // if (creditCard.freemium) {
//     //   const expireDate = moment(creditCard.freemium_expire_date);
//     //   if (expireDate.isAfter(moment())) {
//     //     const discount = statement.interest;
//     //     await statement.update({ discount });
//     //   } else if (expireDate.isBefore(moment())) {
//     //     await creditCard.update({ freemium: false });
//     //   }
//     // }

//     // // Assume these instance methods perform necessary DB calls.
//     // const aggBalance = await statement.aggBalance();
//     // const interestAmount = await statement.interest();
//     // const netOutstanding = await statement.netOutstanding();
//     // const credits = await statement.totalCredit();

//     // // Update statement values.
//     // statement.agg_balance = aggBalance;
//     // statement.interest = interestAmount;
//     // statement.payments = (statement.payments || 0) + credits;
//     // statement.total_outstanding = await statement.totalOutstanding();

//     // return {
//     //   statement,
//     //   summary: {
//     //     statement_id: statement.id,
//     //     total_outstanding: parseFloat(await statement.totalOutstanding()),
//     //     agg_balance: parseFloat(aggBalance),
//     //     credits,
//     //     net_outstanding: netOutstanding,
//     //     interest: parseFloat(interestAmount),
//     //     processing_fee: StatementService.calculateProcessingFee(netOutstanding),
//     //     partial_repayment_percentage: parseFloat(partialPercentage),
//     //     ongoing_repayment: Boolean(ongoingCharge),
//     //   },
//     //   date: {
//     //     billing_date: statement.user.profile.billing_date,
//     //     start_date_day: moment(statement.start_date).utc(),
//     //     end_date_day: moment(statement.end_date).utc(),
//     //     start_date: moment(statement.start_date).utc(),
//     //     end_date: moment(statement.end_date).utc(),
//     //     payment_due_date: paymentDueDate,
//     //     grace_due_date: graceEnd,
//     //     payment_due_status: paymentDueStatusFlag,
//     //     grace_due_status: graceDueStatusFlag,
//     //     payment_date_diff: paymentDayDiff,
//     //   },
//     //   download: {
//     //     start_date_day: moment(statement.start_date).utc().format('ddd, MMM D, YYYY h:mm A'),
//     //     end_date_day: moment(statement.end_date).utc().format('ddd, MMM D, YYYY h:mm A'),
//     //     start_date: moment(statement.start_date).utc().format('LL'),
//     //     end_date: moment(statement.end_date).utc().format('LL'),
//     //     payment_due_date: paymentDueDate.format('LL'),
//     //     grace_due_date: graceEnd.format('LL'),
//     //   },
//     // };
//   }

//   // Generate the latest statement.
//    async generateLatestStatement(userId: number): Promise<any> {
//     // const transaction: Transaction = await db.transaction();
//     try {
//       const account = await this.personalCardAccountsRepository.findOne({
//         where: { user: {id: userId +''} },
//         // include: [
//         //   { association: 'statements', order: [['createdAt', 'DESC']] },
//         //   { association: 'user', attributes: ['id'] },
//         // ],
//       });
//       if (!account) {
//         throw new BadRequestException('Credit card account does not exist', 412);
//       }
//       let count = 0;
//       const latestStatement = this.figureOutLatestStatement(account, count);
//       // await transaction.commit();
//       return latestStatement;
//     } catch (e) {
//       // await transaction.rollback();
//       throw e;
//     }
//   }

//   // Generate (or update) a statement.
//    async generateStatement(account: any, statement: any = null): Promise<any> {
//     const billingDay: number = account.user.profile.billing_date;
//     const lastStatement =
//       statement || (await account.getStatements({ order: [['createdAt', 'DESC']], limit: 1 }))[0];
//     const lastStatementEndDate = moment(lastStatement.end_date);
//     const newStartDate = StatementService.startDate(lastStatementEndDate);
//     const newEndDate = StatementService.endDate(newStartDate, billingDay);

//     // "updateOrCreate" equivalent: check if exists, else create.
//     let stmt = await PersonalAccountStatement.findOne({
//       where: {
//         user_id: account.user.id,
//         account_id: account.id,
//         start_date: newStartDate.toDate(),
//         end_date: newEndDate.toDate(),
//       },
//     });
//     if (!stmt) {
//       stmt = await PersonalAccountStatement.create({
//         user_id: account.user.id,
//         account_id: account.id,
//         start_date: newStartDate.toDate(),
//         end_date: newEndDate.toDate(),
//         outstanding_statement_id: null,
//         condition: Enum.OPEN,
//         interest: 0,
//         opening_balance: 0,
//         carry_over_balance: 0,
//         agg_balance: 0,
//         total_outstanding: 0,
//         fees_and_charges: null,
//       });
//     }
//     return stmt;
//   }

//   // Open a new statement based on an old one.
//    async openNewStatement(oldStatement: any, outstandingAmount: number = 0, interestValue: number | null = null): Promise<any> {
//     let newStatement: any = null;
//     await db.transaction(async (t: Transaction) => {
//       newStatement = await this.generateStatement(oldStatement.personalAccount, oldStatement);
//       await newStatement.update({ condition: Enum.UNPAID }, { transaction: t });
//       if (outstandingAmount > 0) {
//         await PersonalCardTransaction.create({
//           personal_card_accounts_id: newStatement.personalAccount.id,
//           user_id: newStatement.user.id,
//           date: new Date(),
//           amount: outstandingAmount,
//           description: 'Partial Repayment Outstanding',
//           category: PCT.CATEGORIES.REPAYMENT_OUTSTANDING,
//           condition: Enum.UNPAID,
//           type: PCT.TYPE[1],
//           status: PCT.STATUS['5'],
//           statement_status: Enum.OPEN,
//           statement_id: newStatement.id,
//           transaction_reference: uuidv4(),
//         }, { transaction: t });
//       }
//       await newStatement.save({ transaction: t });
//     });
//     return newStatement;
//   }

//   // Generate default charge for a statement.
//    async generateDefaultCharge(statement: any): Promise<void> {
//     const transaction: Transaction = await db.transaction();
//     try {
//       const billingEnd = StatementService.billingEndDate(statement);
//       const defaultStartDate = moment(billingEnd).add(1, 'day');
//       const defaultDays = moment().diff(defaultStartDate, 'days');
//       const aggBalance: number = statement.agg_balance;
//       const defaultAmount = StatementService.calculateDefaultCharge(aggBalance, defaultDays);
//       let defaultCharge = await statement.getUnpaidOriginatedPersonalDefaultCharge();
//       if (defaultCharge) {
//         defaultCharge = StatementService.updateExistingDefaultCharge(defaultCharge, defaultAmount, defaultDays);
//       } else {
//         const unitAmount = defaultAmount / defaultDays;
//         defaultCharge = await statement.createOriginatedPersonalDefaultCharge({
//           account_id: statement.personalAccount.id,
//           unit_amount: unitAmount,
//           amount: defaultAmount,
//           days: defaultDays,
//           condition: Enum.UNPAID,
//           start_date: defaultStartDate.toDate(),
//         }, { transaction });
//       }
//       StatementService.updateDefaultChargeStatement(statement, defaultCharge);
//       await transaction.commit();
//     } catch (e) {
//       await transaction.rollback();
//       throw e;
//     }
//   }

//   // Add a deferred plan to its statement.
//    async addDeferredPlanToItsStatement(deferredPlan: any): Promise<any> {
//     const statement = await StatementService.figureOutStatement(deferredPlan);
//     await deferredPlan.update({
//       statement_status: Enum.CLOSE,
//       statement_id: statement.id,
//     });
//     return await this.setDeferredPlanAmountInStatement(statement, deferredPlan);
//   }

//   // Set the deferred plan amount in the statement.
//    async setDeferredPlanAmountInStatement(statement: any, deferredPlan: any): Promise<any> {
//     statement.deferred_plan_amount = deferredPlan.amount;
//     statement.deferred_plan_id = deferredPlan.id;
//     statement.total_outstanding =
//       statement.deferred_plan_amount +
//       statement.opening_balance +
//       statement.fees_and_charges +
//       statement.carry_over_balance +
//       statement.interest +
//       statement.agg_balance;
//     await statement.save();
//     return statement;
//   }

//   // Add a transaction to its statement.
//    async addTransactionToItsStatement(transaction: any): Promise<any> {
//     const amount: number = transaction.amount;
//     let statement = await StatementService.figureOutStatement(transaction);
//     if (transaction.statement_id) {
//       return statement;
//     }
//     await transaction.update({
//       statement_status: Enum.CLOSE,
//       statement_id: statement.id,
//     });
//     return await this.updateAggBalanceInStatement(statement, amount);
//   }

//   // Update aggregate balance in the statement.
//    async updateAggBalanceInStatement(statement: any, amount: number): Promise<any> {
//     const newAggBalance = statement.agg_balance + amount;
//     const totalAggBalance = newAggBalance + statement.carry_over_balance;
//     const interestAmount = this.interest(statement.personalAccount, totalAggBalance);
//     statement.interest = interestAmount;
//     statement.agg_balance = newAggBalance;
//     statement.total_outstanding =
//       newAggBalance +
//       interestAmount +
//       statement.opening_balance +
//       statement.carry_over_balance +
//       statement.fees_and_charges +
//       (statement.deferred_plan_amount || 0);
//     statement.condition = statement.total_outstanding > 0 ? Enum.UNPAID : Enum.PAID;
//     await statement.save();
//     return statement;
//   }

//   // Generate the first statement for a user.
//   //  async generateFirstStatement(userId: number): Promise<any> {
//   //   const account = await this.personalCardAccountsRepository.findOne({ where: { user: {id: userId+''} }, 
//   //   relations: {user: true} });
//   //   if (!account) {
//   //     throw new BadRequestException('No account found for user');
//   //   }
//   //   const billingDate: number = account.user.profile.billing_date;
//   //   const firstStartDate = new Date();
//   //   const firstEnd = StatementService.firstEndDate(billingDate);
//   //   const aggBalanceQuery = StatementService.anyAggBalanceQuery(account);
//   //   const aggBalance = aggBalanceQuery.sum('amount');
//   //   const interestAmount = this.interest(account, aggBalance);
//   //   const deferredPlanQry = this.deferredPlanQuery(account, firstStartDate, firstEnd.toDate());
//   //   let deferredPlan = await deferredPlanQry.latest();
//   //   deferredPlan = StatementService.createOneIfNoDeferredPlanAtFirstStatement(deferredPlan, account.user);
//   //   const deferredPlanFee = deferredPlan.uniquePlan ? deferredPlan.uniquePlan.fee || 0 : 0;
//   //   const totalOut = StatementService.totalOutstanding(null, aggBalance, deferredPlanFee, interestAmount);
//   //   let statement = await this.personalAccountStatementsRepository.findOne({
//   //     where: {
//   //       userId: userId,
//   //       accountId: account.id,
//   //       startDate: firstStartDate,
//   //       endDate: firstEnd.toDate(),
//   //       totalOutstanding: totalOut,
//   //     },
//   //   });
//   //   if (!statement) {
//   //     statement = await this.personalAccountStatementsRepository.insert({
//   //       user_id: userId,
//   //       account_id: account.id,
//   //       start_date: firstStartDate,
//   //       end_date: firstEnd.toDate(),
//   //       total_outstanding: totalOut,
//   //       condition: totalOut > 0 ? Enum.UNPAID : Enum.PAID,
//   //       opening_balance: 0,
//   //       carry_over_balance: 0,
//   //       interest: interestAmount,
//   //       agg_balance: aggBalance,
//   //       fees_and_charges: 0,
//   //       deferred_plan_amount: deferredPlanFee,
//   //     });
//   //   }
//   //   if (deferredPlan) {
//   //     await deferredPlan.update({
//   //       statement_id: statement.id,
//   //       statement_status: Enum.CLOSE,
//   //     });
//   //   }
//   //   await aggBalanceQuery.update({ statement_status: Enum.CLOSE, statement_id: statement.id });
//   //   return statement;
//   // }

//   // Download the statement.
//   //  async download(id: number): Promise<string> {
//   //   const breakDown = await this.getBreakDown(id);
//   //   // Remove unwanted relations (e.g., 'document' and 'profile')
//   //   // breakDown.statement = breakDown.statement.toJSON();
//   //   // breakDown.statement.user = breakDown.statement.user;
//   //   return await this.convertStatement(breakDown);
//   // }

//   // Convert the statement (e.g., generate a document).
//   //  async convertStatement(breakDown: any): Promise<string> {
//   //   const statement = breakDown.statement;
//   //   let document = await StatementDocument.document(statement.id);
//   //   if (statement.condition === Enum.PAID && document) {
//   //     return document.url;
//   //   }
//   //   const response = await this.connectToConverter(breakDown);
//   //   if (!response.status) {
//   //     throw new BadRequestException('Converter failed', 417);
//   //   }
//   //   const fileUrl = response.path;
//   //   const fileName = response.name;
//   //   await this.storeStatementDocument(statement, fileUrl, fileName, document);
//   //   return fileUrl;
//   // }

//   // // Connect to the converter service via WebSocket.
//   //  async connectToConverter(data: any): Promise<any> {
//   //   data.zxzionxz = 'for tracking end of data';
//   //   // const converterUrl = config.api.converter_url;
//   //   // const converterPort = config.api.converter_port;
//   //   // const socket = new SocketClient(converterUrl, converterPort);
//   //   // await socket.sendMessage(data);
//   //   // const response = await socket.receiveMessage();
//   //   // return JSON.parse(response);
//   // }

//   // Store (or update) the statement document.
//   //  async storeStatementDocument(statement: any, fileUrl: string, fileName: string, document: any): Promise<void> {
//   //   if (statement.condition === Enum.UNPAID && !document) {
//   //     return;
//   //   }
//   //   if (statement.condition === Enum.UNPAID && document) {
//   //     await document.delete();
//   //     return;
//   //   }
//   //   await StatementDocument.updateOrCreate(
//   //     { statement_id: statement.id },
//   //     {
//   //       user_id: statement.user.id,
//   //       file_name: fileName,
//   //       url: fileUrl,
//   //     }
//   //   );
//   // }

//   // Create a new transaction.
//    async createTransaction(
//     statement: any,
//     amount: number,
//     category: string,
//     type: TYPE,
//     description: string = '',
//     reference: string | null = null,
//     status: string = Enum.PENDING,
//     metadata: any = {}
//   ): Promise<any> {
//     if (! [TYPE.CREDIT.toString(), TYPE.DEBIT.toString()].includes(type)) {
//       throw new BadRequestException(
//         `Invalid transaction type. Accepted options are: ${Object.values(TYPE).join(', ')}`
//       );
//     }
//     if (!Object.values(Categories).map(v=> v.toString()).includes(category)) {
//       throw new BadRequestException(
//         `Invalid transaction category. Accepted options are: ${Object.values(Categories).join(', ')}`
//       );
//     }
//     reference = reference || uuidv4();
//     const transaction = await this.personalCardATransactionsRepository.insert({
//       personalCardAccountsId: statement.personalAccount.id,
//       userId: statement.user.id,
//       statementId: statement.id,
//       date: new Date().toISOString(),
//       amount: amount+'',
//       description,
//       category,
//       status,
//       transactionReference: reference,
//       condition: Enum.UNPAID,
//       statementStatus: Enum.OPEN,
//       type,
//       metadata,
//     });
//     return transaction;
//   }

//   // Create a repayment transaction.
//    async createStatementRepaymentTransaction(
//     statement: any,
//     amount: number,
//     partPaid: boolean = false,
//     description: string = 'Credit Card Repayment',
//     metadata: any = {},
//     category: string = Categories.REPAYMENT
//   ): Promise<any> {
//     const transaction = await this.createTransaction(
//       statement,
//       amount,
//       category,
//       TYPE.CREDIT,
//       description,
//       null,
//       undefined,
//       metadata
//     );
//     await transaction.update({ condition: partPaid ? CONDITION['3'] : CONDITION['2'] });
//     return transaction;
//   }

//   // Clear (settle) the statement.
//    async clearStatement(statement: any, amountToRestorePCABy: number, partPaid: boolean = false): Promise<boolean> {
//     // await db.transaction(async (t: Transaction) => {
//     //   const unpaidTransactions = await statement.getUnpaidTransactions();
//     //   if (unpaidTransactions && unpaidTransactions.length > 0) {
//     //     await Promise.all(
//     //       unpaidTransactions.map((tx: any) =>
//     //         tx.update({ condition: partPaid ? Enum.PART_PAID : Enum.PAID }, { transaction: t })
//     //       )
//     //     );
//     //   }
//     //   await statement.update({ condition: partPaid ? Enum.PART_PAID : Enum.PAID }, { transaction: t });
//     //   await statement.update({ statement_status: Enum.CLOSE }, { transaction: t });
//     //   const transactions = await statement.getTransactions();
//     //   if (transactions && transactions.length > 0) {
//     //     await Promise.all(
//     //       transactions.map((tx: any) =>
//     //         tx.update({ statement_status: Enum.CLOSE }, { transaction: t })
//     //       )
//     //     );
//     //   }
//     //   await statement.personalAccount.update({ ongoing_charge: false, status: 'active' }, { transaction: t });
//     //   await this.appropriatePersonalCardAccount(statement.personalAccount, amountToRestorePCABy);
//     // });
//     // return true;
//   }

//   // Update personal card account balances.
//    async appropriatePersonalCardAccount(account: any, amount: number): Promise<void> {
//     if (amount <= 0) {
//       await account.update({
//         available_credit: account.credit_card_limit,
//         available_balance: 0,
//       });
//     } else {
//       await account.update({
//         available_credit: account.available_credit + amount,
//         available_balance: account.available_balance - amount,
//       });
//     }
//   }

//   // Update (ratify) a repayment transaction.
//    async ratifyRepaymentTransaction(
//     repaymentTransaction: any,
//     statementId: number | null = null,
//     metadata: any = null,
//     status: boolean = true
//   ): Promise<boolean> {
//     await repaymentTransaction.update({
//       status: status ?  STATUS['5'] : STATUS['2'],
//       statement_id: statementId || repaymentTransaction.statement_id,
//       metadata: metadata || repaymentTransaction.metadata,
//     });
//     return true;
//   }

//    async updateRepaymentTransactionSuccess(
//     repaymentTransaction: any,
//     statementId: number | null = null,
//     metadata: any = null
//   ): Promise<boolean> {
//     return await this.ratifyRepaymentTransaction(repaymentTransaction, statementId, metadata, true);
//   }

//    async updateRepaymentTransactionFailed(
//     repaymentTransaction: any,
//     statementId: number | null = null,
//     metadata: any = null
//   ): Promise<boolean> {
//     return await this.ratifyRepaymentTransaction(repaymentTransaction, statementId, metadata, false);
//   }
// }
