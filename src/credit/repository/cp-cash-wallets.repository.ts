import { Injectable } from "@nestjs/common";
import { CpcashWallets } from "src/config/entities/CpcashWallets";
import { TypeOrmRepository } from "src/config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class CpcashWalletsRepository extends TypeOrmRepository<CpcashWallets> {
  constructor(private readonly dataSource: DataSource) {
    super(CpcashWallets, dataSource.createEntityManager());
  }
}
