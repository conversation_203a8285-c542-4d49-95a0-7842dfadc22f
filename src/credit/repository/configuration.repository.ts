import { Injectable } from "@nestjs/common";
import { Configurations } from "../../config/entities/Configurations";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class ConfigurationRepository extends TypeOrmRepository<Configurations> {
  constructor(private readonly dataSource: DataSource) {
    super(Configurations, dataSource.createEntityManager());
  }
}
