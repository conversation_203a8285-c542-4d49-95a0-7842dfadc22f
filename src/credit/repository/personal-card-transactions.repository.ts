import { Injectable } from "@nestjs/common";
import { PersonalCardTransactions } from "../../config/entities/PersonalCardTransactions";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class PersonalCardATransactionsRepository extends TypeOrmRepository<PersonalCardTransactions> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalCardTransactions, dataSource.createEntityManager());
  }
}
