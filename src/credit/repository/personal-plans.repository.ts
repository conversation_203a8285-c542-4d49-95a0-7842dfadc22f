import { Injectable } from "@nestjs/common";
import { PersonalCardPlans } from "src/config/entities/PersonalCardPlans";
import { UserPlans } from "src/config/entities/UserPlans";
import { TypeOrmRepository } from "src/config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class PersonalPlansRepository extends TypeOrmRepository<PersonalCardPlans> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalCardPlans, dataSource.createEntityManager());
  }
}
