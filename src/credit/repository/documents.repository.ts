import { Injectable } from "@nestjs/common";
import { Documents } from "../../config/entities/Documents";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class DocumentsRepository extends TypeOrmRepository<Documents> {
  constructor(private readonly dataSource: DataSource) {
    super(Documents, dataSource.createEntityManager());
  }
}
