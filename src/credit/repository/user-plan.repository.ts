import { Injectable } from "@nestjs/common";
import { UserPlans } from "../../config/entities/UserPlans";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class UserPlansRepository extends TypeOrmRepository<UserPlans> {
  constructor(private readonly dataSource: DataSource) {
    super(UserPlans, dataSource.createEntityManager());
  }
}
