import { Injectable } from "@nestjs/common";
import { LoanbotVerifications } from "src/config/entities/LoanbotVerifications";
import { TypeOrmRepository } from "src/config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class LoanbotVerificationsRepository extends TypeOrmRepository<LoanbotVerifications> {
  constructor(private readonly dataSource: DataSource) {
    super(LoanbotVerifications, dataSource.createEntityManager());
  }
}
