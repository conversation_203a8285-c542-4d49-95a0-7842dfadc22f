import { Injectable } from "@nestjs/common";
import { PersonalCardAccounts } from "../../config/entities/PersonalCardAccounts";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class PersonalCardAccountsRepository extends TypeOrmRepository<PersonalCardAccounts> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalCardAccounts, dataSource.createEntityManager());
  }
}
