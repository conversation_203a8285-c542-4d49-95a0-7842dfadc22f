import { Injectable } from "@nestjs/common";
import { PersonalAccountStatements } from "../../config/entities/PersonalAccountStatements";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class PersonalAccountStatementsRepository extends TypeOrmRepository<PersonalAccountStatements> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalAccountStatements, dataSource.createEntityManager());
  }
}
