import { Test, TestingModule } from '@nestjs/testing';
import { StatementService } from './statement.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PersonalCardAccounts } from '../config/entities/PersonalCardAccounts';
import { UserProfiles } from '../config/entities/UserProfiles';
import { PersonalAccountStatements } from '../config/entities/PersonalAccountStatements';
import { PersonalCardTransactions } from '../config/entities/PersonalCardTransactions';
import { ConfigurationRepository } from './repository/configuration.repository';
import { UserPlansRepository } from './repository/user-plan.repository';
import { PersonalAccountStatementsRepository } from './repository/personal-account-statements.repository';
import { PersonalCardATransactionsRepository } from './repository/personal-card-transactions.repository';
import { PersonalCardAccountsRepository } from './repository/personal-card-account.repository';
import { EntityManager, In } from 'typeorm';

describe('StatementService', () => {
  let service: StatementService;
  let configurationRepository: jest.Mocked<ConfigurationRepository>;

  const mockPersonalCardAccounts = {
    id: '1',
    userId: '1',
    accountNo: '**********',
    availableBalance: '1000.00',
    creditCardLimit: '5000.00',
    availableCredit: '4000.00',
    status: 'active',
    repaymentPercentage: '100.0000',
    pinStatus: 'set',
    freemium: false,
    ongoingCharge: false,
    creditCardLimitIncreasedAt: null,
    deletedAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    provider: 'default',
    type: 'virtual',
    cycleStatus: 'active',
    canCalculateDefault: true,
    freemiumExpireDate: null
  } as PersonalCardAccounts;

  const mockUserProfiles = {
    id: '1',
    userId: '1',
    dateOfBirth: '1990-01-01',
    jobTitle: 'Software Engineer',
    gender: 'male',
    maritalStatus: 'single',
    bvn: '***********',
    address: '123 Main St',
    state: 'Lagos',
    lga: 'Ikeja',
    education: 'Bachelor',
    employer: 'Tech Corp',
    empType: 'full-time',
    salary: '100000',
    status: 'active',
    billingDate: 15,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null
  } as UserProfiles;

  const mockStatement: Partial<PersonalAccountStatements> = {
    id: '1',
    userId: '1',
    accountId: '1',
    startDate: new Date(),
    endDate: new Date(),
    totalOutstanding: '1000.00',
    openingBalance: '0.00',
    carryOverBalance: '0.00',
    interest: '0.00',
    aggBalance: '1000.00',
    feesAndCharges: '0.00',
    payments: '0.00',
    createdAt: new Date(),
    updatedAt: new Date(),
    condition: 'active'
  };

  const mockTransaction: Partial<PersonalCardTransactions> = {
    id: '1',
    userId: '1',
    personalCardAccountsId: '1',
    date: '2024-03-20',
    amount: '1000.00',
    description: 'Test transaction',
    category: 'purchase',
    type: 'debit',
    status: 'completed',
    statementStatus: 'open',
    carriedOver: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockUserPlansRepository = {
    findOne: jest.fn().mockResolvedValue({
      id: '1',
      userId: '1',
      interest: 10,
      status: 'active'
    }),
  };

  const mockPersonalCardAccountsRepository = {
    findOne: jest.fn().mockResolvedValue(mockPersonalCardAccounts),
  };

  const mockPersonalAccountStatementsRepository = {
    findOne: jest.fn().mockResolvedValue(mockStatement),
    save: jest.fn().mockResolvedValue(mockStatement),
  };

  const mockPersonalCardATransactionsRepository = {
    find: jest.fn().mockResolvedValue([mockTransaction]),
    findOne: jest.fn().mockResolvedValue(mockTransaction),
    sum: jest.fn().mockResolvedValue(1000),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      getRawOne: jest.fn().mockResolvedValue({ sum: 1000 }),
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StatementService,
        {
          provide: UserPlansRepository,
          useValue: mockUserPlansRepository,
        },
        {
          provide: PersonalAccountStatementsRepository,
          useValue: mockPersonalAccountStatementsRepository,
        },
        {
          provide: PersonalCardATransactionsRepository,
          useValue: mockPersonalCardATransactionsRepository,
        },
        {
          provide: PersonalCardAccountsRepository,
          useValue: mockPersonalCardAccountsRepository,
        },
        {
          provide: ConfigurationRepository,
          useValue: {
            findOneBy: jest.fn().mockResolvedValue({
              value: '9',
              name: 'new_statement_generation_days_limit'
            }),
          },
        },
      ],
    }).compile();

    service = module.get<StatementService>(StatementService);
    configurationRepository = module.get(ConfigurationRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('debitTransactions', () => {
    it('should return debit transactions for a statement', async () => {
      mockPersonalCardATransactionsRepository.find.mockResolvedValueOnce([mockTransaction]);
      
      const result = await service.debitTransactions('1');
      expect(result).toEqual([mockTransaction]);
      expect(mockPersonalCardATransactionsRepository.find).toHaveBeenCalledWith({
        where: {
          statement: { id: '1' },
          type: 'debit',
          status: In(['success', 'pending']),
        },
      });
    });
  });

  describe('creditTransactions', () => {
    it('should return credit transactions for a statement', async () => {
      const mockCreditTransaction = { ...mockTransaction, type: 'credit' };
      mockPersonalCardATransactionsRepository.find.mockResolvedValueOnce([mockCreditTransaction]);
      
      const result = await service.creditTransactions('1');
      expect(result).toEqual([mockCreditTransaction]);
      expect(mockPersonalCardATransactionsRepository.find).toHaveBeenCalledWith({
        where: {
          statement: { id: '1' },
          type: 'credit',
          status: 'success',
        },
      });
    });
  });

  describe('creditTransactionsSum', () => {
    it('should return sum of credit transactions', async () => {
      const mockSum = 1000;
      mockPersonalCardATransactionsRepository.sum.mockResolvedValueOnce(mockSum);

      const result = await service.creditTransactionsSum('1');
      expect(result).toBe(mockSum);
      expect(mockPersonalCardATransactionsRepository.sum).toHaveBeenCalledWith('amount', {
        statement: { id: '1' },
        type: 'credit',
        status: 'success',
      });
    });
  });

  describe('totalOutstanding', () => {
    it('should return total outstanding amount', async () => {
      mockPersonalAccountStatementsRepository.findOne.mockResolvedValueOnce({
        ...mockStatement,
        userId: '1'
      });

      mockPersonalCardATransactionsRepository.sum
        .mockResolvedValueOnce(1000) // aggBalance
        .mockResolvedValueOnce(1000); // carriedOverTransactions

      mockUserPlansRepository.findOne.mockResolvedValueOnce({
        interest: 10,
        status: 'active'
      });

      const result = await service.totalOutstanding('1');
      expect(result).toEqual({
        total: 1200, // 1000 + (20% of 1000)
        interest: 200,
        aggBalance: 1000,
        carryOverBalance: 0,
        deferredPlanAmount: 0,
        feesAndCharges: 0
      });
    });
  });

  describe('generateStatement', () => {
    it('should generate a new statement', async () => {
      const mockStatementForSync = {
        ...mockStatement,
        totalOutstanding: '2000.00',
        openingBalance: '1000.00'
      };

      const mockEntityManager = {
        save: jest.fn().mockResolvedValue(mockStatementForSync),
        createQueryRunner: jest.fn().mockReturnValue({
          connect: jest.fn(),
          startTransaction: jest.fn(),
          commitTransaction: jest.fn(),
          rollbackTransaction: jest.fn(),
          release: jest.fn(),
        }),
      } as unknown as EntityManager;

      mockPersonalAccountStatementsRepository.save.mockResolvedValueOnce(mockStatementForSync);

      const result = await service.generateStatement(
        mockPersonalCardAccounts,
        mockUserProfiles,
        mockEntityManager
      );

      expect(result).toEqual(mockStatementForSync);
    });
  });
}); 