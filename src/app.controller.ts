import { Controller, Get, Param, Query } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/health')
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('/query-mandate/:reference')
  verifyMandate(@Param('reference') reference: string): string {
    return this.appService.verifyMandate(reference);
  }
}
