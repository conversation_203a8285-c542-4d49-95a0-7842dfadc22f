import { Injectable, Logger } from '@nestjs/common';
import { Analytics } from '@customerio/cdp-analytics-node';
import config from 'src/config';
import { CustomerIoPayload, formatPhoneNumber } from 'src/utils';
@Injectable()
export class CustomerIoService {
  private logger: Logger = new Logger(CustomerIoService.name);
  private readonly analytics: Analytics;
  constructor() {
    this.analytics = new Analytics({
      writeKey: config.customerIoApiKey,
      maxEventsInBatch: 1,
    });

    //  this.analytics.identify({
    //      userId: '26',
    //     traits: {
    //            email: '<EMAIL>',
    //            firstName: 'Wisdom',
    //            lastName: 'Ekeh',
    //            phone: formatPhoneNumber('08169489972'),
    //          },
    //          timestamp: new Date().toUTCString()
    // }, (err, res) => {
    //   this.logger.log(`customer inserted: ${res.toJSON()}, ${err}`);
    //   console.log(res.toJSON(), err);
    // });
  }
  async insertCustomer(payload: CustomerIoPayload) {
    try {
      const res = await this.analytics.identify(payload, (err, res) => {
        this.logger.log(`customer inserted: ${res.toJSON()}, ${err}`);
      });
      this.logger.log(`customer inserted: ${res}`);
      return res;
    } catch (error) {
      this.logger.log(`could not insert customer: ${error.message}`);
    }
  }
}
