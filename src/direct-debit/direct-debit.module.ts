import { Module } from '@nestjs/common';
import { DirectDebitService } from './direct-debit.service';
import { DirectDebitController } from './direct-debit.controller';
import { DirectDebitTransactionsRepository } from './repository/direct-debit-transactions.repositrory';
import { DirectDebitRepository } from './repository/direct-debit.repository';
import { PaystackModule } from '@app/paystack';
import { RabbitmqModule } from '@crednet/utils';
import { Exchanges, PaystackEvents } from 'src/utils';
import config from 'src/config';
import { DirectDebitConsumer } from './direct-debit.consumer';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';
import { CardRepository } from 'src/user/repository/card.repository';

@Module({
  imports: [PaystackModule, 
     RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'credit-direct-debit.queue',
      prefetchCount: 10,
      showLog: true,
      global: false,
      consumeDeadLetterQueue:false,
      deadLetterQueueInterval: 100000,
      producer: {
        name: Exchanges.CREDIT,
        durable: true,
      },
      subscriptions: [
        `${Exchanges.WEBHOOK}.paystack.${PaystackEvents.DIRECT_DEBIT_ACTIVE}`,
        `${Exchanges.WEBHOOK}.paystack.${PaystackEvents.DIRECT_DEBIT_CREATED}`,
      ],
    }),
  ],
  controllers: [DirectDebitController],
  providers: [
    DirectDebitService,
    DirectDebitTransactionsRepository,
    DirectDebitRepository,
    DirectDebitConsumer, 
    UserProfileRepository,
    PersonalCardAccountsRepository,
    CardRepository
  ],
})
export class DirectDebitModule {}
