import { Controller, Get, Post, Param, UseGuards } from '@nestjs/common';
import { DirectDebitService } from './direct-debit.service';
import { AuthData, GetAuthData, JwtAuthGuard } from '@crednet/authmanager';
import { <PERSON>piBearerAuth } from '@nestjs/swagger';

@Controller('direct-debit')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class DirectDebitController {
  constructor(private readonly directDebitService: DirectDebitService) {}

  @Get('/verify/:reference')
  verifyMandate(@Param('reference') reference: string) {
    return this.directDebitService.verifyMandate(reference);
  }

  @Post('/initiate')
  initiateMandate(@GetAuthData() auth: AuthData) {
    return this.directDebitService.initiateMandate(auth);
  }

  @Get('/mandates')
  mandates(@GetAuthData() auth: AuthData): Promise<any> {
    return this.directDebitService.mandates(auth);
  }

  @Get('/mandate-transactions')
  mandatetransactions(@GetAuthData() auth: AuthData): Promise<any> {
    return this.directDebitService.mandateTransactions(auth);
  }
}
