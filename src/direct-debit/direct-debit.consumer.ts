import { Injectable, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { CashEvents, Exchanges, PaystackEvents } from 'src/utils'; 
import { DirectDebitService } from './direct-debit.service';

@Injectable()
export class DirectDebitConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly directDebitService: DirectDebitService,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.paystack.${PaystackEvents.DIRECT_DEBIT_ACTIVE}`,
      async ({ data, ack }) => {
        if (!data) return ack();
        this.handleMandate(data);
        ack();
      },
    );

     this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.paystack.${PaystackEvents.DIRECT_DEBIT_CREATED}`,
      async ({ data, ack }) => {
        if (!data) return ack();
        this.handleMandate(data);
        ack();
      },
    );
  }

  async handleMandate(data) {
    console.log('data', data);
    if (data) {
      const email = data?.data?.customer?.email;
      const { reference } = data;
      await this.directDebitService.verifyMandate(reference);
    }
  }
}
