import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { DirectDebits } from "src/config/entities/DirectDebits";

@Injectable()
export class DirectDebitRepository extends TypeOrmRepository<DirectDebits> {
  constructor(private readonly dataSource: DataSource) {
    super(DirectDebits, dataSource.createEntityManager());
  }
}
