import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { DirectDebitTransactions } from "src/config/entities/DirectDebitTransactions";

@Injectable()
export class DirectDebitTransactionsRepository extends TypeOrmRepository<DirectDebitTransactions> {
  constructor(private readonly dataSource: DataSource) {
    super(DirectDebitTransactions, dataSource.createEntityManager());
  }
}
