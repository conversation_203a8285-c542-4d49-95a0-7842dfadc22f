import { Injectable } from '@nestjs/common';
import { CreateDirectDebitDto } from './dto/create-direct-debit.dto';
import { UpdateDirectDebitDto } from './dto/update-direct-debit.dto';
import { DirectDebitRepository } from './repository/direct-debit.repository';
import { PaystackService } from '@app/paystack';
import { DirectDebitTransactionsRepository } from './repository/direct-debit-transactions.repositrory';
import { randomUUID } from 'crypto';
import { AuthData } from '@crednet/authmanager';
import config from 'src/config';
import { OnEvent } from '@nestjs/event-emitter';
import { Events } from 'src/utils';
import { UserProfileRepository } from 'src/user/repository/user-profile.repository';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';
import { CardRepository } from 'src/user/repository/card.repository';

@Injectable()
export class DirectDebitService {
  constructor(
    private readonly directDebitRepository: DirectDebitRepository,
    private readonly profileRepository: UserProfileRepository,
    private readonly directDebitTransactionRepository: DirectDebitTransactionsRepository,
    private readonly paystackService: PaystackService,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly cardRepository: CardRepository,
  ) {
    // this.verifyMandate('st0jb9x57gxmwlx');
    setTimeout(() => {
      // this.updateAccountMandateStatus({ userId: '26' });
    }, 4000);
  }

  mandates(auth: AuthData) {
    return this.directDebitRepository.findBy({ userId: auth.id + '' });
  }

  mandateTransactions(auth: AuthData) {
    return this.directDebitTransactionRepository.findBy({
      userId: auth.id + '',
      type: 'authorization',
    });
  }

  async initiateMandate(auth: AuthData) {
    console.log(config.paystack.directdebitCallbackUrl);
    const response = await this.paystackService.initiateDirectDebit({
      email: auth.email,
      callbackUrl: `${config.paystack.directdebitCallbackUrl}/query-mandate/${auth.id}`,
    });

    await this.directDebitTransactionRepository.insert({
      userId: auth.id + '',
      id: randomUUID(),
      // email: auth.email,
      reference: response.reference,
      createdAt: new Date(),
      updatedAt: new Date(),
      amount: '0',
      type: 'authorization',
      status: 'pending',
    });
    return response;
  }

  async verifyMandate(reference: string) {
    try {
      const response =
        await this.paystackService.verifyAuthorization(reference);

      if (response.status === true && response.data?.reusable === true) {
        if (response.data?.active == true) {
          await this.directDebitTransactionRepository.update(
            { reference },
            {
              status: 'success',
              updatedAt: new Date(),
            },
          );
        }

        const transaction =
          await this.directDebitTransactionRepository.findOneBy({
            reference,
          });

        const mandate = await this.directDebitRepository.findBy({
          signature: response.data?.signature,
          userId: transaction.userId,
        });

        if (!mandate.length) {
          await this.directDebitRepository.insert({
            userId: transaction.userId,
            authorizationCode: response.data?.authorization_code,
            createdAt: new Date(),
            updatedAt: new Date(),
            email: response.data?.customer?.email,
            last4: response.data?.last4,
            signature: response.data?.signature,
            endDate: new Date(
              response.data?.exp_year,
              response.data?.exp_month,
              1,
            ),
            startDate: new Date(),
            bank: response.data?.bank,
            id: randomUUID(),
            status: response.data?.active == true ? 'active' : 'initiated',
          });

          await this.updateAccountMandateStatus({ userId: transaction.userId });
        }

        await this.profileRepository.update(
          { userId: transaction.userId },
          {
            cardActivationPaid: true,
          },
        );
      }
    } catch (error) {
      console.log(error);
      if (
        String(error.message).includes(
          'does not exist or does not belong to integration',
        )
      ) {
        await this.directDebitTransactionRepository.update(
          { reference },
          {
            status: 'failed',
            updatedAt: new Date(),
          },
        );
      }
    }
  }

  async checkMandate(userId: string) {}

  @OnEvent(Events.VERIFY_MANDATE)
  async verifyMandateEvent(payload: { userId: string; reference: string }) {
    if (payload.reference) {
      this.verifyMandate(payload.reference);
    }

    if (payload.userId) {
      const mandate = await this.directDebitTransactionRepository.findBy({
        userId: payload.userId,
      });
      if (mandate.length > 0) {
        for (const m of mandate) {
          await this.verifyMandate(m.reference);
        }
      }
    }
  }

  @OnEvent(Events.CHECK_MANDATE_STATUS)
  async checkMandateStatus(payload: { userId: string }) {
    const userId = payload.userId;
    const mandate = await this.directDebitRepository.findBy({
      status: 'initiated',
      userId,
    });
    console.log(mandate.length);
    if (mandate.length > 0) {
      for (const m of mandate) {
        const customer = await this.paystackService.getCustomer(m.email);
        var mandateAuthorizations = customer.data.authorizations?.filter(
          (a) => a.channel == 'direct_debit',
        );

        if (
          mandateAuthorizations.filter(
            (f) => f.authorization_code == m.authorizationCode,
          ).length
        ) {
          await this.directDebitRepository.update(
            { id: m.id },
            {
              status: 'active',
            },
          );
        } else {
          await this.paystackService.retryAuthorizationCharge(
            customer.data.id + '',
          );
        }
      }
    }
    return mandate;
  }

  @OnEvent(Events.UPDATE_ACCOUNT_MANDATE_STATUS)
  async updateAccountMandateStatus(payload: { userId: string }) {
    console.log(Events.UPDATE_ACCOUNT_MANDATE_STATUS, payload);
    const userId = payload.userId;
    const mandate = await this.directDebitRepository.findBy({ userId });
    const cards = await this.cardRepository.findBy({ userId });
    console.log(mandate.length, cards.length);
    const now = new Date();

    const activeMandates = mandate.filter((m) =>
      ['initiated', 'active'].includes(m.status),
    );
    const activeCards = cards; //.filter((m) => ['active'].includes(m.status));

    const hasActiveMandate =
      activeMandates.length ||
      activeCards.filter(
        (m) =>
          parseInt(m.expYear) > now.getFullYear() ||
          (parseInt(m.expYear) == now.getFullYear() &&
            parseInt(m.expMonth) >= now.getMonth() + 1),
      ).length;
    console.log(Events.UPDATE_ACCOUNT_MANDATE_STATUS,hasActiveMandate, activeMandates?.length,activeCards?.length,
     hasActiveMandate? 'active':
          (activeMandates.length || activeCards.length) && !hasActiveMandate
            ?'expired'
              : 'inactive',  );

    await this.personalCardAccountsRepository.update(
      { userId },
      {
        mandateStatus:hasActiveMandate? 'active':
          (activeMandates.length || activeCards.length) && !hasActiveMandate
            ?'expired'
              : 'inactive',
        updatedAt: new Date(),      
      },
    );
    return mandate;
  }
}
