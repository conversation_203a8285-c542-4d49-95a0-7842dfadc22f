import { DataSource, EntityManager } from 'typeorm';
import { InternalServerErrorException } from '@nestjs/common';

export function useDBTransaction<T>(
  dataSource: DataSource,
  callback: (manager: EntityManager) => Promise<T>,
): Promise<T> {
  const manager = dataSource.createEntityManager();
  if (!manager) {
    throw new InternalServerErrorException('No transaction manager found');
  }
  console.log('using db transaction');
  return manager.transaction(callback);
}
