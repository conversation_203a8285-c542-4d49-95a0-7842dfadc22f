import config from '.';
import { DataSource, DataSourceOptions } from 'typeorm';
import { UserProfiles } from './entities/UserProfiles';
import { Users } from './entities/Users';
import { Webhooks } from './entities/Webhooks';
 

require('dotenv').config();

export const typeOrmConfig: DataSourceOptions = {
  type: 'mysql',
  url: config.db.url,
  timezone: "Z",
  // autoLoadEntities:true,
  // migrations: ['dist/db/migrations/*.js'],
  ssl: false,
  entities: ['**/entities/*.js'],
  subscribers: [],
  logging: false,
  synchronize:  false 
};

export const dataSource = new DataSource(typeOrmConfig);
