import * as path from 'node:path';
import * as dotenv from 'dotenv';

dotenv.config({
  path: path.resolve(process.cwd(), './.env'),
});

export default {
  port: Number.parseInt(process.env.PORT, 10),
  env: process.env.ENV,
  nodeEnv: process.env.NODE_ENV,
  baseUrl: process.env.BASE_URL || `http://127.0.0.1:${process.env.PORT}`,
  isCronEnabled: process.env.IS_CRON_ENABLED === 'true',
  jwt: {
    publicKey: process.env.PUBLIC_KEY,
    issuer: process.env.ISSUER || 'crednet/auth', // time in seconds
  },
  db: {
    url: process.env.DATABASE_URL,
  },
  baxi: {
    baseUrl: process.env.BAXI_BASE_URL,
    apiKey: process.env.BAXI_API_KEY,
  },
  flutterwave: {
    baseUrl: 'https://api.flutterwave.com',
    apiKey: process.env.FLUTTERWAVE_SECRET_KEY,
  },
  notification: {
    signatureKey: process.env.CP_CASH_HEADER_KEY,

    baseUrl: process.env.NOTIFICATION_URL,
  },
  verification: {
    uri: process.env.VERIFICATION_BASE_URL,
  },
  loanbot: {
    uri: process.env.LOANBOT_BASE_URL,
    apiKey: process.env.LOANBOT_API_KEY,
  },
  redis: {
    url: process.env.REDIS_URL,
    host: process.env.REDIS_HOST,
    user: process.env.REDIS_USER,
    password: process.env.REDIS_PASS,
    port: Number.parseInt(process.env.REDIS_PORT, 10),
  },
  rabbitMq: {
    brockers: process.env.RABBIT_MQ_BROCKERS?.split(','),
  },
  FT_API_KEY: process.env.FT_API_KEY,
  paystack: {
    baseUrl: process.env.PAYSTACK_BASE_URL ?? 'https://api.paystack.co',
    secretKey: process.env.PAYSTACK_SECRET_KEY,
    directdebitCallbackUrl: process.env.PAYSTACK_DIRECTDEBIT_CALLBACK_URL,
  },
  korapay: {
    baseUrl: process.env.KORAPAY_BASE_URL,
    secretKey: process.env.KORAPAY_SECRET_KEY,
  },
  whatsapp: {
    apiKey: process.env.WHATSAPP_API_KEY,
    instanceId: process.env.WHATSAPP_INSTANCE_ID || '56072',
    phoneNumber: process.env.WHATSAPP_ID || '629647906896875',
  },
  telegram: {
    apiKey: process.env.TELEGRAM_API_KEY,
  },
  fiveLinx: {
    user: process.env.FIVELINX_USER,
    password: process.env.FIVELINX_PASS,
    sender: process.env.FIVELINX_SENDER,
    baseUrl: process.env.FIVELINX_BASE_URL,
  },
  crednetCachePrefix: process.env.CREDNET_CACHE_PREFIX ?? 'crednet_cache_',
  mbs: {
    secret: process.env.MBS_CLIENT_SECRET,
    uri: process.env.MBS_URI,
    clientId: process.env.MBS_CLIENT_ID,
    corporateEmail: process.env.MBS_CORPORATE_EMAIL || '<EMAIL>',
  },
  emisri: {
    url: process.env.EMISRI_BASE_URL,
    apiKey: process.env.EMISRI_TOKEN,
    username: process.env.EMISRI_USERNAME,
    password: process.env.EMISRI_PASSWORD,
    sender: process.env.EMISRI_SENDER,
  },
  smsProvider: process.env.SMS_PROVIDER ?? 'emisri',
  customerIoApiKey: process.env.CUSTOMER_IO_API_KEY,
};
