// import { Module } from '@nestjs/common';
// import { ConfigService } from './config.service';
// import { ConfigController } from './config.controller';
// import { ConfigurationRepository } from 'src/wallet/repository/configuration.repository';

// @Module({
//   controllers: [ConfigController],
//   providers: [ConfigService, ConfigurationRepository],
//   exports: [ConfigService, ConfigurationRepository],
// })
// export class ConfigModule {}
