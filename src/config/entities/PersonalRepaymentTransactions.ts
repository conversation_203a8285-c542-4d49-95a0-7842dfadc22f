import {
  Column,
  Entity,
  Index,
  Join<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { Users } from "./Users";

@Index("personal_repayment_transactions_account_id_foreign", ["accountId"], {})
@Index("personal_repayment_transactions_user_id_foreign", ["userId"], {})
@Entity("personal_repayment_transactions")
export class PersonalRepaymentTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "account_id", unsigned: true })
  accountId: string;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("datetime", { name: "period_from" })
  periodFrom: Date;

  @Column("datetime", { name: "period_to" })
  periodTo: Date;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) =>
      personalCardAccounts.personalRepaymentTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @ManyToOne(() => Users, (users) => users.personalRepaymentTransactions, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
