import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("vouchers")
export class Vouchers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "code", length: 255 })
  code: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("decimal", { name: "amount", precision: 8, scale: 2 })
  amount: string;

  @Column("bigint", { name: "referrer_id", nullable: true, unsigned: true })
  referrerId: string | null;

  @Column("datetime", { name: "used_at", nullable: true })
  usedAt: Date | null;

  @Column("bigint", { name: "merchant_id", nullable: true, unsigned: true })
  merchantId: string | null;

  @Column("tinyint", {
    name: "is_payment_processed",
    width: 1,
    default: () => "'0'",
  })
  isPaymentProcessed: boolean;

  @Column("datetime", { name: "processed_at", nullable: true })
  processedAt: Date | null;
}
