import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyPlans } from "./CompanyPlans";

@Index("company_unique_plans_company_id_foreign", ["companyId"], {})
@Index("company_unique_plans_company_plan_id_foreign", ["companyPlanId"], {})
@Entity("company_unique_plans")
export class CompanyUniquePlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "company_plan_id", nullable: true, unsigned: true })
  companyPlanId: string | null;

  @Column("varchar", { name: "plan_name", length: 255 })
  planName: string;

  @Column("enum", {
    name: "duration_label",
    enum: ["monthly", "quarterly", "biannually", "yearly"],
  })
  durationLabel: "monthly" | "quarterly" | "biannually" | "yearly";

  @Column("varchar", { name: "ref_no", nullable: true, length: 255 })
  refNo: string | null;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("date", { name: "expiry_date" })
  expiryDate: string;

  @Column("varchar", { name: "interest_rate", nullable: true, length: 255 })
  interestRate: string | null;

  @Column("decimal", {
    name: "subscription_fee",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  subscriptionFee: string | null;

  @Column("decimal", {
    name: "card_issuance_fee",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  cardIssuanceFee: string | null;

  @Column("int", { name: "maintenance_fee_per_card", nullable: true })
  maintenanceFeePerCard: number | null;

  @Column("int", { name: "free_cards", nullable: true })
  freeCards: number | null;

  @Column("int", { name: "maximum_cards", nullable: true })
  maximumCards: number | null;

  @Column("decimal", {
    name: "business_credit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  businessCredit: string | null;

  @Column("int", { name: "receipt_storage", nullable: true })
  receiptStorage: number | null;

  @Column("int", { name: "receipt_storage_count", default: () => "'0'" })
  receiptStorageCount: number;

  @Column("int", { name: "free_card_count", default: () => "'0'" })
  freeCardCount: number;

  @Column("int", { name: "maximum_card_count", default: () => "'0'" })
  maximumCardCount: number;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyUniquePlans, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyPlans,
    (companyPlans) => companyPlans.companyUniquePlans,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_plan_id", referencedColumnName: "id" }])
  companyPlan: CompanyPlans;
}
