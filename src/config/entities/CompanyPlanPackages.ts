import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { PackagesPlans } from "./PackagesPlans";

@Entity("company_plan_packages")
export class CompanyPlanPackages {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(() => PackagesPlans, (packagesPlans) => packagesPlans.package)
  packagesPlans: PackagesPlans[];
}
