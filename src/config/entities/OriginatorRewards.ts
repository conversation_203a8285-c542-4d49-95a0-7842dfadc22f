import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Rewards } from "./Rewards";

@Index("originator_rewards_reward_id_foreign", ["rewardId"], {})
@Entity("originator_rewards")
export class OriginatorRewards {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "reward_id", nullable: true, length: 36 })
  rewardId: string | null;

  @Column("double", {
    name: "commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  commission: number | null;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("varchar", { name: "batch", nullable: true, length: 255 })
  batch: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "request_withdrawal_at", nullable: true })
  requestWithdrawalAt: Date | null;

  @Column("timestamp", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Rewards, (rewards) => rewards.originatorRewards, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "reward_id", referencedColumnName: "id" }])
  reward: Rewards;
}
