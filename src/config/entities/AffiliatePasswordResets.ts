import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Index('affiliate_password_resets_email_index', ['email'], {})
@Index('affiliate_password_resets_token_unique', ['token'], { unique: true })
@Entity('affiliate_password_resets')
export class AffiliatePasswordResets {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;
  @Column('varchar', { name: 'email', length: 255 })
  email: string;

  @Column('varchar', { name: 'token', length: 255 })
  token: string;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;
}
