import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Users } from "./Users";

@Index("comments_comment_by_foreign", ["commentBy"], {})
@Index(
  "comments_commentable_type_commentable_id_index",
  ["commentableType", "commentableId"],
  {}
)
@Index("comments_user_id_foreign", ["userId"], {})
@Entity("comments")
export class Comments {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("bigint", { name: "comment_by", unsigned: true })
  commentBy: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "commentable_type", length: 255 })
  commentableType: string;

  @Column("bigint", { name: "commentable_id", unsigned: true })
  commentableId: string;

  @Column("text", { name: "body" })
  body: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.comments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "comment_by", referencedColumnName: "id" }])
  commentBy2: Users;

  @ManyToOne(() => Users, (users) => users.comments2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
