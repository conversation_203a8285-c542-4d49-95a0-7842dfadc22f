import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("banks")
export class Banks {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "bank_name", length: 255 })
  bankName: string;

  @Column("varchar", { name: "bank_code", length: 255 })
  bankCode: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
