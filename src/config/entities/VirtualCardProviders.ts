import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("virtual_card_providers")
export class VirtualCardProviders {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "title", length: 255 })
  title: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "currency_unit", length: 255 })
  currencyUnit: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("int", { name: "editor_id" })
  editorId: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
