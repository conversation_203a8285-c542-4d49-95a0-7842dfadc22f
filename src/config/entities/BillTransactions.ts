import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("bill_transactions_is_resolved_index", ["isResolved"], {})
@Index("bill_transactions_payment_method_index", ["paymentMethod"], {})
@Index("bill_transactions_service_provider_index", ["serviceProvider"], {})
@Index("bill_transactions_status_index", ["status"], {})
@Index("bill_transactions_type_index", ["type"], {})
@Index("bill_transactions_user_id_foreign", ["userId"], {})
@Index("bill_transactions_user_type_index", ["userType"], {})
@Index("bill_transactions_wallet_id_index", ["walletId"], {})
@Entity("bill_transactions")
export class BillTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("int", { name: "account_id", nullable: true })
  accountId: number | null;

  @Column("char", { name: "wallet_id", nullable: true, length: 36 })
  walletId: string | null;

  @Column("varchar", { name: "user_type", length: 255 })
  userType: string;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("varchar", { name: "service_provider", length: 255 })
  serviceProvider: string;

  @Column("varchar", { name: "request_ref", nullable: true, length: 255 })
  requestRef: string | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'complete'",
  })
  status: string;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("varchar", { name: "recipient_number", nullable: true, length: 255 })
  recipientNumber: string | null;

  @Column("varchar", { name: "action", length: 255, default: () => "'skip'" })
  action: string;

  @Column("tinyint", { name: "is_resolved", nullable: true, width: 1 })
  isResolved: boolean | null;

  @Column("int", { name: "count", default: () => "'0'" })
  count: number;

  @Column("longtext", { name: "url", nullable: true })
  url: string | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("longtext", { name: "description", nullable: true })
  description: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.billTransactions, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
