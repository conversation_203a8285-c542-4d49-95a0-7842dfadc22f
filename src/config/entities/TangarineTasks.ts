import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Loans } from "./Loans";
import { Users } from "./Users";

@Index("tangarine_tasks_loan_id_foreign", ["loanId"], {})
@Index("tangarine_tasks_user_id_foreign", ["userId"], {})
@Entity("tangarine_tasks")
export class TangarineTasks {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "success", "error"],
    default: () => "'pending'",
  })
  status: "pending" | "success" | "error";

  @Column("json", { name: "response" })
  response: object;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Loans, (loans) => loans.tangarineTasks, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(() => Users, (users) => users.tangarineTasks, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
