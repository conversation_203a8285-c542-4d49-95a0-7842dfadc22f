import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { ExpenseRequests } from "./ExpenseRequests";
import { Users } from "./Users";

@Index("expense_approvers_company_id_foreign", ["companyId"], {})
@Index("expense_approvers_expense_request_id_foreign", ["expenseRequestId"], {})
@Index("expense_approvers_user_id_foreign", ["userId"], {})
@Entity("expense_approvers")
export class ExpenseApprovers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "expense_request_id", unsigned: true })
  expenseRequestId: string;

  @Column("varchar", { name: "role_slug", length: 255 })
  roleSlug: string;

  @Column("enum", {
    name: "status",
    enum: ["approved", "declined", "pending"],
    default: () => "'pending'",
  })
  status: "approved" | "declined" | "pending";

  @Column("tinyint", {
    name: "write_permission",
    width: 1,
    default: () => "'0'",
  })
  writePermission: boolean;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.expenseApprovers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => ExpenseRequests,
    (expenseRequests) => expenseRequests.expenseApprovers,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_request_id", referencedColumnName: "id" }])
  expenseRequest: ExpenseRequests;

  @ManyToOne(() => Users, (users) => users.expenseApprovers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
