import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  <PERSON>ToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("virtual_accounts_provider_index", ["provider"], {})
@Index("virtual_accounts_user_id_foreign", ["userId"], {})
@Index("virtual_accounts_virtual_account_id_index", ["virtualAccountId"], {})
@Index(
  "virtual_accounts_virtual_account_number_index",
  ["virtualAccountNumber"],
  {}
)
@Index("virtual_accounts_wallet_id_index", ["walletId"], {})
@Entity("virtual_accounts")
export class VirtualAccounts {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "wallet_id", length: 255 })
  walletId: string;

  @Column("varchar", { name: "virtual_account_id", length: 255 })
  virtualAccountId: string;

  @Column("varchar", {
    name: "virtual_account_number",
    nullable: true,
    length: 255,
  })
  virtualAccountNumber: string | null;

  @Column("varchar", { name: "provider", length: 255 })
  provider: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.virtualAccounts, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
