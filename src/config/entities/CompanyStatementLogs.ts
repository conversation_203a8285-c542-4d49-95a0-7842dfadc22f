import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";

@Index("company_statement_logs_company_id_foreign", ["companyId"], {})
@Index(
  "company_statement_logs_company_statement_id_foreign",
  ["companyStatementId"],
  {}
)
@Entity("company_statement_logs")
export class CompanyStatementLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", {
    name: "company_statement_id",
    nullable: true,
    unsigned: true,
  })
  companyStatementId: string | null;

  @Column("decimal", { name: "amount_paid", precision: 15, scale: 2 })
  amountPaid: string;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyStatementLogs, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyStatementLogs,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_statement_id", referencedColumnName: "id" }])
  companyStatement: CompanyStatements;
}
