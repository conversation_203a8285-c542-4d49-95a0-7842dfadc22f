import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { BankTransfers } from "./BankTransfers";
import { Beneficiaries } from "./Beneficiaries";
import { CompanyRequests } from "./CompanyRequests";
import { CompanyAccountStatements } from "./CompanyAccountStatements";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyCardFeePayments } from "./CompanyCardFeePayments";
import { CompanyCreditCardLimitRequest } from "./CompanyCreditCardLimitRequest";
import { CompanyCreditCardRepayments } from "./CompanyCreditCardRepayments";
import { CompanyCreditTransactions } from "./CompanyCreditTransactions";
import { CompanyCustomPlanRequests } from "./CompanyCustomPlanRequests";
import { CompanyDebitCardRequest } from "./CompanyDebitCardRequest";
import { CompanyDebitTransactions } from "./CompanyDebitTransactions";
import { CompanyDefaultCharges } from "./CompanyDefaultCharges";
import { CompanyDocuments } from "./CompanyDocuments";
import { CompanyExpenseCategories } from "./CompanyExpenseCategories";
import { CompanyExpenseSubCategories } from "./CompanyExpenseSubCategories";
import { CompanyPlanUpgradeRequests } from "./CompanyPlanUpgradeRequests";
import { CompanyProfiles } from "./CompanyProfiles";
import { CompanyRepaymentCards } from "./CompanyRepaymentCards";
import { CompanyStatementLogs } from "./CompanyStatementLogs";
import { CompanyStatementPayments } from "./CompanyStatementPayments";
import { CompanyStatements } from "./CompanyStatements";
import { CompanyTransfers } from "./CompanyTransfers";
import { CompanyUniquePlans } from "./CompanyUniquePlans";
import { CompanyWallets } from "./CompanyWallets";
import { ExpenseApprovers } from "./ExpenseApprovers";
import { ExpenseGroups } from "./ExpenseGroups";
import { ExpensePolicies } from "./ExpensePolicies";
import { ExpenseRequests } from "./ExpenseRequests";
import { Groups } from "./Groups";
import { Payments } from "./Payments";
import { Settlements } from "./Settlements";
import { Units } from "./Units";
import { Users } from "./Users";

@Index("companies_company_request_id_foreign", ["companyRequestId"], {})
@Index("companies_name_unique", ["name"], { unique: true })
@Entity("companies")
export class Companies {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", unique: true, length: 255 })
  name: string;

  @Column("varchar", { name: "email_domain", nullable: true, length: 255 })
  emailDomain: string | null;

  @Column("int", { name: "tier" })
  tier: number;

  @Column("double", {
    name: "interest_rate",
    precision: 22,
    default: () => "'0'",
  })
  interestRate: number;

  @Column("varchar", { name: "agent_code", nullable: true, length: 255 })
  agentCode: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("bigint", {
    name: "company_request_id",
    nullable: true,
    unsigned: true,
  })
  companyRequestId: string | null;

  @OneToMany(() => BankTransfers, (bankTransfers) => bankTransfers.company)
  bankTransfers: BankTransfers[];

  @OneToMany(() => Beneficiaries, (beneficiaries) => beneficiaries.company)
  beneficiaries: Beneficiaries[];

  @ManyToOne(
    () => CompanyRequests,
    (companyRequests) => companyRequests.companies,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_request_id", referencedColumnName: "id" }])
  companyRequest: CompanyRequests;

  @OneToMany(
    () => CompanyAccountStatements,
    (companyAccountStatements) => companyAccountStatements.company
  )
  companyAccountStatements: CompanyAccountStatements[];

  @OneToMany(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.company
  )
  companyAccounts: CompanyAccounts[];

  @OneToMany(
    () => CompanyCardFeePayments,
    (companyCardFeePayments) => companyCardFeePayments.company
  )
  companyCardFeePayments: CompanyCardFeePayments[];

  @OneToMany(
    () => CompanyCreditCardLimitRequest,
    (companyCreditCardLimitRequest) => companyCreditCardLimitRequest.company
  )
  companyCreditCardLimitRequests: CompanyCreditCardLimitRequest[];

  @OneToMany(
    () => CompanyCreditCardRepayments,
    (companyCreditCardRepayments) => companyCreditCardRepayments.company
  )
  companyCreditCardRepayments: CompanyCreditCardRepayments[];

  @OneToMany(
    () => CompanyCreditTransactions,
    (companyCreditTransactions) => companyCreditTransactions.company
  )
  companyCreditTransactions: CompanyCreditTransactions[];

  @OneToMany(
    () => CompanyCustomPlanRequests,
    (companyCustomPlanRequests) => companyCustomPlanRequests.company
  )
  companyCustomPlanRequests: CompanyCustomPlanRequests[];

  @OneToMany(
    () => CompanyDebitCardRequest,
    (companyDebitCardRequest) => companyDebitCardRequest.company
  )
  companyDebitCardRequests: CompanyDebitCardRequest[];

  @OneToMany(
    () => CompanyDebitTransactions,
    (companyDebitTransactions) => companyDebitTransactions.company
  )
  companyDebitTransactions: CompanyDebitTransactions[];

  @OneToMany(
    () => CompanyDefaultCharges,
    (companyDefaultCharges) => companyDefaultCharges.company
  )
  companyDefaultCharges: CompanyDefaultCharges[];

  @OneToMany(
    () => CompanyDocuments,
    (companyDocuments) => companyDocuments.company
  )
  companyDocuments: CompanyDocuments[];

  @OneToMany(
    () => CompanyExpenseCategories,
    (companyExpenseCategories) => companyExpenseCategories.company
  )
  companyExpenseCategories: CompanyExpenseCategories[];

  @OneToMany(
    () => CompanyExpenseSubCategories,
    (companyExpenseSubCategories) => companyExpenseSubCategories.company
  )
  companyExpenseSubCategories: CompanyExpenseSubCategories[];

  @OneToMany(
    () => CompanyPlanUpgradeRequests,
    (companyPlanUpgradeRequests) => companyPlanUpgradeRequests.company
  )
  companyPlanUpgradeRequests: CompanyPlanUpgradeRequests[];

  @OneToMany(
    () => CompanyProfiles,
    (companyProfiles) => companyProfiles.company
  )
  companyProfiles: CompanyProfiles[];

  @OneToMany(
    () => CompanyRepaymentCards,
    (companyRepaymentCards) => companyRepaymentCards.company
  )
  companyRepaymentCards: CompanyRepaymentCards[];

  @OneToMany(
    () => CompanyStatementLogs,
    (companyStatementLogs) => companyStatementLogs.company
  )
  companyStatementLogs: CompanyStatementLogs[];

  @OneToMany(
    () => CompanyStatementPayments,
    (companyStatementPayments) => companyStatementPayments.company
  )
  companyStatementPayments: CompanyStatementPayments[];

  @OneToMany(
    () => CompanyStatements,
    (companyStatements) => companyStatements.company
  )
  companyStatements: CompanyStatements[];

  @OneToMany(
    () => CompanyTransfers,
    (companyTransfers) => companyTransfers.company
  )
  companyTransfers: CompanyTransfers[];

  @OneToMany(
    () => CompanyUniquePlans,
    (companyUniquePlans) => companyUniquePlans.company
  )
  companyUniquePlans: CompanyUniquePlans[];

  @OneToMany(() => CompanyWallets, (companyWallets) => companyWallets.company)
  companyWallets: CompanyWallets[];

  @OneToMany(
    () => ExpenseApprovers,
    (expenseApprovers) => expenseApprovers.company
  )
  expenseApprovers: ExpenseApprovers[];

  @OneToMany(() => ExpenseGroups, (expenseGroups) => expenseGroups.company)
  expenseGroups: ExpenseGroups[];

  @OneToMany(
    () => ExpensePolicies,
    (expensePolicies) => expensePolicies.company
  )
  expensePolicies: ExpensePolicies[];

  @OneToMany(
    () => ExpenseRequests,
    (expenseRequests) => expenseRequests.company
  )
  expenseRequests: ExpenseRequests[];

  @OneToMany(() => Groups, (groups) => groups.company)
  groups: Groups[];

  @OneToMany(() => Payments, (payments) => payments.company)
  payments: Payments[];

  @OneToMany(() => Settlements, (settlements) => settlements.company)
  settlements: Settlements[];

  @OneToMany(() => Units, (units) => units.company)
  units: Units[];

  @OneToMany(() => Users, (users) => users.company)
  users: Users[];
}
