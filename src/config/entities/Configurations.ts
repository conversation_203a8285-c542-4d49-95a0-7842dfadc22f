import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("configurations")
export class Configurations {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("int", {
    name: "configuration_group_id",
    nullable: true,
    unsigned: true,
  })
  configurationGroupId: number | null;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "title", length: 255 })
  title: string;

  @Column("varchar", { name: "default", length: 255 })
  default: string;

  @Column("longtext", { name: "value" })
  value: string;

  @Column("varchar", { name: "value_type", length: 255 })
  valueType: string;

  @Column("text", { name: "field", nullable: true })
  field: string | null;

  @Column("bigint", { name: "editor_id" })
  editorId: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
