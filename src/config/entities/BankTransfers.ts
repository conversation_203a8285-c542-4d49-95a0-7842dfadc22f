import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { Users } from "./Users";

@Index("bank_transfers_company_id_foreign", ["companyId"], {})
@Index(
  "bank_transfers_payable_type_payable_id_index",
  ["payableType", "payableId"],
  {}
)
@Index("bank_transfers_user_id_foreign", ["userId"], {})
@Entity("bank_transfers")
export class BankTransfers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "reference", length: 255 })
  reference: string;

  @Column("varchar", { name: "payable_type", length: 255 })
  payableType: string;

  @Column("bigint", { name: "payable_id", unsigned: true })
  payableId: string;

  @Column("double", { name: "amount", precision: 22 })
  amount: number;

  @Column("enum", {
    name: "status",
    enum: ["pending", "confirmed", "disputed"],
    default: () => "'pending'",
  })
  status: "pending" | "confirmed" | "disputed";

  @Column("timestamp", { name: "confirmed_at", nullable: true })
  confirmedAt: Date | null;

  @Column("timestamp", { name: "disputed_at", nullable: true })
  disputedAt: Date | null;

  @Column("bigint", { name: "confirmed_by", nullable: true, unsigned: true })
  confirmedBy: string | null;

  @Column("bigint", { name: "disputed_by", nullable: true, unsigned: true })
  disputedBy: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.bankTransfers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(() => Users, (users) => users.bankTransfers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
