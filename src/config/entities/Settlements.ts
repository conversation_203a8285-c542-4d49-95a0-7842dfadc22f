import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("settlements_company_id_foreign", ["companyId"], {})
@Entity("settlements")
export class Settlements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("double", { name: "amount", precision: 22, default: () => "'0'" })
  amount: number;

  @Column("double", {
    name: "amount_paid",
    precision: 22,
    default: () => "'0'",
  })
  amountPaid: number;

  @Column("date", { name: "due_date" })
  dueDate: string;

  @Column("enum", {
    name: "status",
    enum: ["not_due", "due", "transferred", "paid"],
    default: () => "'not_due'",
  })
  status: "not_due" | "due" | "transferred" | "paid";

  @Column("datetime", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("tinyint", { name: "defaulted", width: 1, default: () => "'0'" })
  defaulted: boolean;

  @Column("datetime", { name: "disputed_at", nullable: true })
  disputedAt: Date | null;

  @Column("bigint", { name: "paid_by", nullable: true, unsigned: true })
  paidBy: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.settlements, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
