import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("account_statements_credit_expenses")
export class AccountStatementsCreditExpenses {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
