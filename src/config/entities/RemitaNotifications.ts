import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("remita_notifications")
export class RemitaNotifications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("varchar", { name: "request_id", length: 255 })
  requestId: string;

  @Column("longtext", { name: "mandate_id", nullable: true })
  mandateId: string | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
