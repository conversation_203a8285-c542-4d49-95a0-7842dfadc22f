import { Column, <PERSON>ti<PERSON>, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "virtual_card_transactions_payment_reference_unique",
  ["paymentReference"],
  { unique: true }
)
@Index(
  "virtual_card_transactions_personal_card_transaction_id_index",
  ["paymentReference"],
  {}
)
@Index("virtual_card_transactions_provider_index", ["provider"], {})
@Index("virtual_card_transactions_reference_index", ["vcProviderReference"], {})
@Index("virtual_card_transactions_status_index", ["status"], {})
@Index(
  "virtual_card_transactions_transaction_type_index",
  ["transactionType"],
  {}
)
@Index("virtual_card_transactions_user_id_index", ["userId"], {})
@Entity("virtual_card_transactions")
export class VirtualCardTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "virtual_card_id", length: 50 })
  virtualCardId: string;

  @Column("decimal", { name: "amount", precision: 12, scale: 2 })
  amount: string;

  @Column("decimal", {
    name: "balance",
    nullable: true,
    precision: 12,
    scale: 2,
    default: () => "'0.00'",
  })
  balance: string | null;

  @Column("varchar", {
    name: "vc_provider_reference",
    nullable: true,
    comment: "Can also be transaction ID from provider",
    length: 100,
  })
  vcProviderReference: string | null;

  @Column("varchar", {
    name: "transaction_type",
    nullable: true,
    comment: "Can be either debit / credit",
    length: 15,
  })
  transactionType: string | null;

  @Column("bigint", {
    name: "payment_reference",
    nullable: true,
    unique: true,
    unsigned: true,
  })
  paymentReference: string | null;

  @Column("tinytext", { name: "narration", nullable: true })
  narration: string | null;

  @Column("varchar", { name: "currency", nullable: true, length: 15 })
  currency: string | null;

  @Column("varchar", { name: "provider", nullable: true, length: 30 })
  provider: string | null;

  @Column("varchar", { name: "status", length: 40 })
  status: string;

  @Column("text", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;
}
