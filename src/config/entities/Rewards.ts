import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { OriginatorRewards } from "./OriginatorRewards";
import { AffiliateCodes } from "./AffiliateCodes";
import { Affiliates } from "./Affiliates";
import { Conversions } from "./Conversions";

@Index("rewards_affiliate_code_id_foreign", ["affiliateCodeId"], {})
@Index("rewards_affiliate_id_foreign", ["affiliateId"], {})
@Index("rewards_batch_index", ["batch"], {})
@Index("rewards_conversion_id_foreign", ["conversionId"], {})
@Index("rewards_paid_at_index", ["paidAt"], {})
@Index("rewards_product_index", ["product"], {})
@Index(
  "rewards_rewardable_type_rewardable_id_index",
  ["rewardableType", "rewardableId"],
  {}
)
@Index("rewards_status_index", ["status"], {})
@Entity("rewards")
export class Rewards {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", nullable: true, length: 36 })
  affiliateId: string | null;

  @Column("varchar", { name: "rewardable_type", length: 255 })
  rewardableType: string;

  @Column("bigint", { name: "rewardable_id", unsigned: true })
  rewardableId: string;

  @Column("char", { name: "conversion_id", nullable: true, length: 36 })
  conversionId: string | null;

  @Column("varchar", { name: "product", nullable: true, length: 255 })
  product: string | null;

  @Column("varchar", { name: "tier", nullable: true, length: 255 })
  tier: string | null;

  @Column("double", {
    name: "commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  commission: number | null;

  @Column("varchar", {
    name: "registration_state",
    nullable: true,
    length: 255,
  })
  registrationState: string | null;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("varchar", { name: "batch", nullable: true, length: 255 })
  batch: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("char", { name: "affiliate_code_id", nullable: true, length: 36 })
  affiliateCodeId: string | null;

  @Column("timestamp", { name: "request_withdrawal_at", nullable: true })
  requestWithdrawalAt: Date | null;

  @OneToMany(
    () => OriginatorRewards,
    (originatorRewards) => originatorRewards.reward
  )
  originatorRewards: OriginatorRewards[];

  @ManyToOne(() => AffiliateCodes, (affiliateCodes) => affiliateCodes.rewards, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_code_id", referencedColumnName: "id" }])
  affiliateCode: AffiliateCodes;

  @ManyToOne(() => Affiliates, (affiliates) => affiliates.rewards, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;

  @ManyToOne(() => Conversions, (conversions) => conversions.rewards, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "conversion_id", referencedColumnName: "id" }])
  conversion: Conversions;
}
