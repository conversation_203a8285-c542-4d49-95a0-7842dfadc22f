import { Column, Entity } from "typeorm";

@Entity("bvns")
export class Bvns {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "bvn", length: 255 })
  bvn: string;

  @Column("longtext", { name: "data" })
  data: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
