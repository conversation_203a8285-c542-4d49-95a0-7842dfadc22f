import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("cpcash_wallets_user_id_index", ["userId"], {})
@Index("cpcash_wallets_virtual_account_id_index", ["virtualAccountId"], {})
@Index("cpcash_wallets_wallet_id_index", ["walletId"], {})
@Entity("cpcash_wallets")
export class CpcashWallets {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "user_id", nullable: true, length: 255 })
  userId: string | null;

  @Column("varchar", { name: "wallet_id", nullable: true, length: 255 })
  walletId: string | null;

  @Column("varchar", {
    name: "virtual_account_id",
    nullable: true,
    length: 255,
  })
  virtualAccountId: string | null;

  @Column("tinyint", {
    name: "is_email_synced_to_wallets",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  isEmailSyncedToWallets: boolean | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
