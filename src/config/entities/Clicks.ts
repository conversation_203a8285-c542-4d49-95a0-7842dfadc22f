import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { AffiliateCodes } from "./AffiliateCodes";
import { Affiliates } from "./Affiliates";
import { Conversions } from "./Conversions";

@Index("clicks_affiliate_code_id_foreign", ["affiliateCodeId"], {})
@Index("clicks_affiliate_id_foreign", ["affiliateId"], {})
@Entity("clicks")
export class Clicks {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", nullable: true, length: 36 })
  affiliateId: string | null;

  @Column("tinyint", { name: "unique", width: 1 })
  unique: boolean;

  @Column("varchar", { name: "medium", length: 255 })
  medium: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("char", { name: "affiliate_code_id", nullable: true, length: 36 })
  affiliateCodeId: string | null;

  @ManyToOne(() => AffiliateCodes, (affiliateCodes) => affiliateCodes.clicks, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_code_id", referencedColumnName: "id" }])
  affiliateCode: AffiliateCodes;

  @ManyToOne(() => Affiliates, (affiliates) => affiliates.clicks, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;

  @OneToMany(() => Conversions, (conversions) => conversions.click)
  conversions: Conversions[];
}
