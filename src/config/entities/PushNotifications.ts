import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("push_notifications")
export class PushNotifications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "title", length: 255 })
  title: string;

  @Column("mediumtext", { name: "message" })
  message: string;

  @Column("varchar", { name: "image", nullable: true, length: 255 })
  image: string | null;

  @Column("varchar", { name: "cta", nullable: true, length: 255 })
  cta: string | null;

  @Column("bigint", { name: "category_id", nullable: true, unsigned: true })
  categoryId: string | null;

  @Column("datetime", { name: "scheduled_date", nullable: true })
  scheduledDate: Date | null;

  @Column("tinyint", {
    name: "is_sent",
    nullable: true,
    width: 1,
    default: () => "'1'",
  })
  isSent: boolean | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
