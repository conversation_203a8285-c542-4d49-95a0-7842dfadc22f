import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyPlans } from "./CompanyPlans";
import { Users } from "./Users";

@Index("company_custom_plan_requests_company_id_foreign", ["companyId"], {})
@Index(
  "company_custom_plan_requests_company_plan_id_foreign",
  ["companyPlanId"],
  {}
)
@Index("company_custom_plan_requests_user_id_foreign", ["userId"], {})
@Entity("company_custom_plan_requests")
export class CompanyCustomPlanRequests {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "company_plan_id", unsigned: true })
  companyPlanId: string;

  @Column("enum", {
    name: "duration_label",
    enum: ["monthly_fee", "quarterly_fee", "biannually_fee", "yearly_fee"],
  })
  durationLabel:
    | "monthly_fee"
    | "quarterly_fee"
    | "biannually_fee"
    | "yearly_fee";

  @Column("varchar", { name: "ref_no", nullable: true, length: 255 })
  refNo: string | null;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("enum", { name: "status", enum: ["pending", "approved", "declined"] })
  status: "pending" | "approved" | "declined";

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyCustomPlanRequests,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyPlans,
    (companyPlans) => companyPlans.companyCustomPlanRequests,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_plan_id", referencedColumnName: "id" }])
  companyPlan: CompanyPlans;

  @ManyToOne(() => Users, (users) => users.companyCustomPlanRequests, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
