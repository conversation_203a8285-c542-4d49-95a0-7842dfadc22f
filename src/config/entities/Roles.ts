import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinT<PERSON>,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ExpensePolicyRole } from "./ExpensePolicyRole";
import { Permissions } from "./Permissions";
import { Users } from "./Users";

@Entity("roles")
export class Roles {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => ExpensePolicyRole,
    (expensePolicyRole) => expensePolicyRole.role
  )
  expensePolicyRoles: ExpensePolicyRole[];

  @ManyToMany(() => Permissions, (permissions) => permissions.roles)
  permissions: Permissions[];

  @ManyToMany(() => Users, (users) => users.roles)
  @JoinTable({
    name: "users_roles",
    joinColumns: [{ name: "role_id", referencedColumnName: "id" }],
    inverseJoinColumns: [{ name: "user_id", referencedColumnName: "id" }],
    schema: "uat_live",
  })
  users: Users[];
}
