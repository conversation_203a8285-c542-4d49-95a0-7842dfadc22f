import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("feature_configurations")
export class FeatureConfigurations {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "title", length: 255 })
  title: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("tinyint", { name: "default", width: 1, default: () => "'1'" })
  default: boolean;

  @Column("tinyint", { name: "value", width: 1, default: () => "'1'" })
  value: boolean;

  @Column("json", { name: "data", nullable: true })
  data: object | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
