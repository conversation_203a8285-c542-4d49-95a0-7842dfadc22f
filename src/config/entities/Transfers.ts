import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("transfers_account_id_index", ["accountId"], {})
@Index("transfers_is_resolved_index", ["isResolved"], {})
@Index("transfers_provider_index", ["provider"], {})
@Index("transfers_reference_index", ["reference"], {})
@Index("transfers_status_index", ["status"], {})
@Index("transfers_transaction_id_index", ["transactionId"], {})
@Index("transfers_user_id_foreign", ["userId"], {})
@Index("transfers_user_type_index", ["userType"], {})
@Index("transfers_wallet_type_index", ["walletType"], {})
@Entity("transfers")
export class Transfers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("char", { name: "wallet_id", nullable: true, length: 36 })
  walletId: string | null;

  @Column("varchar", {
    name: "wallet_type",
    length: 255,
    default: () => "'credpal_card'",
  })
  walletType: string;

  @Column("bigint", { name: "transaction_id", nullable: true })
  transactionId: string | null;

  @Column("varchar", { name: "user_type", length: 255 })
  userType: string;

  @Column("varchar", { name: "reference", length: 255 })
  reference: string;

  @Column("varchar", { name: "provider", nullable: true, length: 255 })
  provider: string | null;

  @Column("varchar", { name: "transfer_code", nullable: true, length: 255 })
  transferCode: string | null;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("tinyint", { name: "is_resolved", nullable: true, width: 1 })
  isResolved: boolean | null;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("int", { name: "update_count", default: () => "'0'" })
  updateCount: number;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.transfers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
