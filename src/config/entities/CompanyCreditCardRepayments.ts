import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";

@Index("company_credit_card_repayments_company_id_foreign", ["companyId"], {})
@Index(
  "company_credit_card_repayments_company_statement_id_foreign",
  ["companyStatementId"],
  {}
)
@Entity("company_credit_card_repayments")
export class CompanyCreditCardRepayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", {
    name: "company_statement_id",
    nullable: true,
    unsigned: true,
  })
  companyStatementId: string | null;

  @Column("varchar", { name: "payment_method", length: 255 })
  paymentMethod: string;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("enum", {
    name: "status",
    enum: ["pending", "decline", "approve"],
    default: () => "'pending'",
  })
  status: "pending" | "decline" | "approve";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyCreditCardRepayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyCreditCardRepayments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_statement_id", referencedColumnName: "id" }])
  companyStatement: CompanyStatements;
}
