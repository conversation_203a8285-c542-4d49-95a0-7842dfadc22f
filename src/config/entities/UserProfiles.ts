import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Lgas } from "./Lgas";
import { States } from "./States";
import { Users } from "./Users";

@Index("user_profiles_billing_date_index", ["billingDate"], {})
@Index("user_profiles_bolt_credit_available_index", ["boltCreditAvailable"], {})
@Index("user_profiles_bolt_credit_limit_index", ["boltCreditLimit"], {})
@Index("user_profiles_bolt_credit_used_index", ["boltCreditUsed"], {})
@Index("user_profiles_bvn_index", ["bvn"], {})
@Index("user_profiles_date_of_birth_index", ["dateOfBirth"], {})
@Index("user_profiles_emp_type_index", ["empType"], {})
@Index("user_profiles_employment_status_index", ["employmentStatus"], {})
@Index("user_profiles_lga_id_foreign", ["lgaId"], {})
@Index("user_profiles_nin_index", ["nin"], {})
@Index("user_profiles_nin_unique", ["nin"], { unique: true })
@Index("user_profiles_referral_code_index", ["referralCode"], {})
@Index("user_profiles_referral_link_index", ["referralLink"], {})
@Index("user_profiles_salary_day_index", ["salaryDay"], {})
@Index("user_profiles_salary_index", ["salary"], {})
@Index("user_profiles_state_id_foreign", ["stateId"], {})
@Index("user_profiles_status_index", ["status"], {})
@Index("user_profiles_user_id_foreign", ["userId"], {})
@Index("user_profiles_verify_method_index", ["verifyMethod"], {})
@Entity("user_profiles")
export class UserProfiles {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "date_of_birth", nullable: true, length: 255 })
  dateOfBirth: string | null;

  @Column("varchar", { name: "job_title", nullable: true, length: 255 })
  jobTitle: string | null;

  @Column("enum", {
    name: "gender",
    nullable: true,
    enum: ["male", "female", "other"],
  })
  gender: "male" | "female" | "other" | null;

  @Column("enum", {
    name: "marital_status",
    nullable: true,
    enum: ["single", "engaged", "married", "divorced", "widowed"],
  })
  maritalStatus:
    | "single"
    | "engaged"
    | "married"
    | "divorced"
    | "widowed"
    | null;

  @Column("varchar", { name: "bvn", nullable: true, length: 255 })
  bvn: string | null;

  @Column("longtext", { name: "additional_phone", nullable: true })
  additionalPhone: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 255 })
  address: string | null;

  @Column("json", { name: "location", nullable: true })
  location: object | null;

  @Column("varchar", { name: "state", nullable: true, length: 255 })
  state: string | null;

  @Column("varchar", { name: "lga", nullable: true, length: 255 })
  lga: string | null;

  @Column("varchar", { name: "education", nullable: true, length: 255 })
  education: string | null;

  @Column("varchar", { name: "employer", nullable: true, length: 255 })
  employer: string | null;

  @Column("varchar", { name: "company_industry", nullable: true, length: 255 })
  companyIndustry: string | null;

  @Column("varchar", { name: "company_address", nullable: true, length: 255 })
  companyAddress: string | null;

  @Column("varchar", { name: "emp_type", nullable: true, length: 255 })
  empType: string | null;

  @Column("varchar", { name: "salary", nullable: true, length: 255 })
  salary: string | null;

  @Column("decimal", {
    name: "detected_salary",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  detectedSalary: string | null;

  @Column("decimal", {
    name: "debt_to_income_ratio",
    nullable: true,
    precision: 5,
    scale: 2,
  })
  debtToIncomeRatio: string | null;

  @Column("varchar", { name: "position", nullable: true, length: 255 })
  position: string | null;

  @Column("varchar", {
    name: "current_loan_facility",
    nullable: true,
    length: 255,
  })
  currentLoanFacility: string | null;

  @Column("varchar", { name: "account_no", nullable: true, length: 255 })
  accountNo: string | null;

  @Column("varchar", { name: "bank_name", nullable: true, length: 255 })
  bankName: string | null;

  @Column("double", {
    name: "standard_loan_limit",
    precision: 22,
    default: () => "'0'",
  })
  standardLoanLimit: number;

  @Column("double", {
    name: "standard_loan_interest_rate",
    nullable: true,
    precision: 22,
  })
  standardLoanInterestRate: number | null;

  @Column("double", {
    name: "credit_limit",
    precision: 22,
    default: () => "'0'",
  })
  creditLimit: number;

  @Column("decimal", {
    name: "bolt_credit_limit",
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  boltCreditLimit: string | null;

  @Column("decimal", {
    name: "bolt_credit_used",
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  boltCreditUsed: string | null;

  @Column("decimal", {
    name: "bolt_credit_available",
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  boltCreditAvailable: string | null;

  @Column("int", { name: "salary_day", nullable: true })
  salaryDay: number | null;

  @Column("int", { name: "billing_date", nullable: true })
  billingDate: number | null;

  @Column("int", { name: "payment_date", nullable: true })
  paymentDate: number | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("varchar", { name: "id_type", nullable: true, length: 255 })
  idType: string | null;

  @Column("varchar", { name: "id_number", nullable: true, length: 255 })
  idNumber: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("varchar", { name: "id_issued_date", nullable: true, length: 255 })
  idIssuedDate: string | null;

  @Column("varchar", { name: "id_expiry_date", nullable: true, length: 255 })
  idExpiryDate: string | null;

  @Column("varchar", { name: "risk_profile_id", nullable: true, length: 255 })
  riskProfileId: string | null;

  @Column("varchar", { name: "referral_code", nullable: true, length: 255 })
  referralCode: string | null;

  @Column("varchar", { name: "referral_link", nullable: true, length: 255 })
  referralLink: string | null;

  @Column("varchar", { name: "bvn_first_name", nullable: true, length: 255 })
  bvnFirstName: string | null;

  @Column("varchar", { name: "bvn_middle_name", nullable: true, length: 255 })
  bvnMiddleName: string | null;

  @Column("varchar", { name: "bvn_last_name", nullable: true, length: 255 })
  bvnLastName: string | null;

  @Column("varchar", { name: "nin_first_name", nullable: true, length: 255 })
  ninFirstName: string | null;

  @Column("varchar", { name: "nin_middle_name", nullable: true, length: 255 })
  ninMiddleName: string | null;

  @Column("varchar", { name: "nin_last_name", nullable: true, length: 255 })
  ninLastName: string | null;

  @Column("varchar", { name: "nin_phone_no", nullable: true, length: 255 })
  ninPhoneNo: string | null;

  @Column("varchar", { name: "nin_date_of_birth", nullable: true, length: 255 })
  ninDateOfBirth: string | null;

  @Column("varchar", { name: "verify_method", nullable: true, length: 255 })
  verifyMethod: string | null;

  @Column("varchar", { name: "trial_bank_code", nullable: true, length: 255 })
  trialBankCode: string | null;

  @Column("varchar", { name: "trial_account_no", nullable: true, length: 255 })
  trialAccountNo: string | null;

  @Column("varchar", {
    name: "trial_bank_account_name",
    nullable: true,
    length: 255,
  })
  trialBankAccountName: string | null;

  @Column("varchar", { name: "employment_status", nullable: true, length: 100 })
  employmentStatus: string | null;

  @Column("text", { name: "company_description", nullable: true })
  companyDescription: string | null;

  @Column("varchar", {
    name: "company_start_month",
    nullable: true,
    length: 255,
  })
  companyStartMonth: string | null;

  @Column("varchar", {
    name: "company_start_year",
    nullable: true,
    length: 255,
  })
  companyStartYear: string | null;

  @Column("varchar", {
    name: "company_registration_number",
    nullable: true,
    length: 255,
  })
  companyRegistrationNumber: string | null;

  @Column("varchar", {
    name: "previous_formal_job",
    nullable: true,
    length: 255,
  })
  previousFormalJob: string | null;

  @Column("varchar", { name: "company_website", nullable: true, length: 255 })
  companyWebsite: string | null;

  @Column("varchar", {
    name: "company_twitter_url",
    nullable: true,
    length: 255,
  })
  companyTwitterUrl: string | null;

  @Column("varchar", {
    name: "company_instagram_url",
    nullable: true,
    length: 255,
  })
  companyInstagramUrl: string | null;

  @Column("varchar", {
    name: "company_monthly_income",
    nullable: true,
    length: 255,
  })
  companyMonthlyIncome: string | null;

  @Column("varchar", {
    name: "company_monthly_profit",
    nullable: true,
    length: 255,
  })
  companyMonthlyProfit: string | null;

  @Column("varchar", { name: "company_size", nullable: true, length: 255 })
  companySize: string | null;

  @Column("varchar", { name: "bank_account_name", nullable: true, length: 255 })
  bankAccountName: string | null;

  @Column("tinyint", { name: "pays_salary", nullable: true, width: 1 })
  paysSalary: boolean | null;

  @Column("varchar", { name: "nin", nullable: true, unique: true, length: 255 })
  nin: string | null;

  @Column("varchar", { name: "link_to_contact", nullable: true, length: 255 })
  linkToContact: string | null;

  @Column("varchar", { name: "trial_work_email", nullable: true, length: 255 })
  trialWorkEmail: string | null;

  @Column("date", { name: "next_loan_request_at", nullable: true })
  nextLoanRequestAt: string | null;

  @Column("char", { name: "state_id", nullable: true, length: 36 })
  stateId: string | null;

  @Column("char", { name: "lga_id", nullable: true, length: 36 })
  lgaId: string | null;

  @Column("varchar", { name: "next_of_kin_name", nullable: true, length: 255 })
  nextOfKinName: string | null;

  @Column("varchar", { name: "next_of_kin_email", nullable: true, length: 255 })
  nextOfKinEmail: string | null;

  @Column("varchar", {
    name: "next_of_kin_phone_no",
    nullable: true,
    length: 255,
  })
  nextOfKinPhoneNo: string | null;

  @Column("varchar", {
    name: "next_of_kin_address",
    nullable: true,
    length: 255,
  })
  nextOfKinAddress: string | null;

  @Column("varchar", {
    name: "next_of_kin_relationship",
    nullable: true,
    length: 255,
  })
  nextOfKinRelationship: string | null;

  @Column("varchar", { name: "onboarded_for", nullable: true, length: 255 })
  onboardedFor: string | null;

  @Column("varchar", { name: "educational_level", nullable: true, length: 100 })
  educationalLevel: string | null;

  @Column("varchar", { name: "employment_role", nullable: true, length: 255 })
  employmentRole: string | null;

  @Column("tinyint", { name: "card_activation_paid", nullable: true, width: 1 })
  cardActivationPaid: boolean | null;

  @Column("json", { name: "preferences", nullable: true })
  preferences: object | null;

  @Column("tinyint", {
    name: "has_good_credit_history",
    nullable: true,
    width: 1,
  })
  hasGoodCreditHistory: boolean | null;

  @Column("varchar", {
    name: "secured_investment_status",
    nullable: true,
    length: 64,
  })
  securedInvestmentStatus: string | null;

  @ManyToOne(() => Lgas, (lgas) => lgas.userProfiles, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lga_id", referencedColumnName: "id" }])
  lga_2: Lgas;

  @ManyToOne(() => States, (states) => states.userProfiles, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "state_id", referencedColumnName: "id" }])
  state_2: States;

  @ManyToOne(() => Users, (users) => users.userProfiles, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
