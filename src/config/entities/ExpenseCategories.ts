import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { ExpenseSubCategories } from "./ExpenseSubCategories";

@Entity("expense_categories")
export class ExpenseCategories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => ExpenseSubCategories,
    (expenseSubCategories) => expenseSubCategories.expenseCategory
  )
  expenseSubCategories: ExpenseSubCategories[];
}
