import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { Users } from "./Users";

@Index("credit_card_activities_account_id_foreign", ["accountId"], {})
@Index("credit_card_activities_editor_id_foreign", ["editorId"], {})
@Entity("credit_card_activities")
export class CreditCardActivities {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "account_id", unsigned: true })
  accountId: string;

  @Column("bigint", { name: "editor_id", unsigned: true })
  editorId: string;

  @Column("varchar", { name: "action", length: 255 })
  action: string;

  @Column("varchar", { name: "reason", length: 255 })
  reason: string;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.creditCardActivities,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @ManyToOne(() => Users, (users) => users.creditCardActivities, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "editor_id", referencedColumnName: "id" }])
  editor: Users;
}
