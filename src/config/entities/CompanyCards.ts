import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccountStatements } from "./CompanyAccountStatements";
import { CompanyAccounts } from "./CompanyAccounts";
import { Users } from "./Users";
import { Groups } from "./Groups";
import { CompanyCreditTransactions } from "./CompanyCreditTransactions";
import { CompanyDebitTransactions } from "./CompanyDebitTransactions";

@Index("company_cards_company_account_id_foreign", ["companyAccountId"], {})
@Index("company_cards_employee_id_foreign", ["employeeId"], {})
@Index("company_cards_group_id_foreign", ["groupId"], {})
@Index("company_cards_manager_id_foreign", ["managerId"], {})
@Entity("company_cards")
export class CompanyCards {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_account_id", unsigned: true })
  companyAccountId: string;

  @Column("varchar", { name: "cms_card_id", length: 255 })
  cmsCardId: string;

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive"],
    default: () => "'inactive'",
  })
  status: "active" | "inactive";

  @Column("datetime", { name: "activation_date", nullable: true })
  activationDate: Date | null;

  @Column("varchar", { name: "pin_status", nullable: true, length: 255 })
  pinStatus: string | null;

  @Column("bigint", { name: "group_id", nullable: true, unsigned: true })
  groupId: string | null;

  @Column("bigint", { name: "employee_id", nullable: true, unsigned: true })
  employeeId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true, unsigned: true })
  managerId: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyAccountStatements,
    (companyAccountStatements) => companyAccountStatements.companyCard
  )
  companyAccountStatements: CompanyAccountStatements[];

  @ManyToOne(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.companyCards,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_account_id", referencedColumnName: "id" }])
  companyAccount: CompanyAccounts;

  @ManyToOne(() => Users, (users) => users.companyCards, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "employee_id", referencedColumnName: "id" }])
  employee: Users;

  @ManyToOne(() => Groups, (groups) => groups.companyCards, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "group_id", referencedColumnName: "id" }])
  group: Groups;

  @ManyToOne(() => Users, (users) => users.companyCards2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "manager_id", referencedColumnName: "id" }])
  manager: Users;

  @OneToMany(
    () => CompanyCreditTransactions,
    (companyCreditTransactions) => companyCreditTransactions.companyCard
  )
  companyCreditTransactions: CompanyCreditTransactions[];

  @OneToMany(
    () => CompanyDebitTransactions,
    (companyDebitTransactions) => companyDebitTransactions.companyCard
  )
  companyDebitTransactions: CompanyDebitTransactions[];
}
