import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("promos_provider_index", ["provider"], {})
@Index("promos_type_index", ["type"], {})
@Entity("promos")
export class Promos {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "provider", length: 100 })
  provider: string;

  @Column("varchar", { name: "identifier", length: 255 })
  identifier: string;

  @Column("varchar", { name: "type", length: 100 })
  type: string;

  @Column("varchar", { name: "status", length: 16 })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
