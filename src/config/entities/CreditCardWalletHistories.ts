import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { PersonalCardAccounts } from "./PersonalCardAccounts";

@Index("credit_card_wallet_histories_log_status_index", ["logStatus"], {})
@Index(
  "credit_card_wallet_histories_transaction_type_index",
  ["transactionType"],
  {}
)
@Index("credit_card_wallet_histories_user_id_foreign", ["userId"], {})
@Index("credit_card_wallet_histories_wallet_id_foreign", ["walletId"], {})
@Index("credit_card_wallet_histories_wallet_type_index", ["walletType"], {})
@Entity("credit_card_wallet_histories")
export class CreditCardWalletHistories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "wallet_id", unsigned: true })
  walletId: string;

  @Column("varchar", { name: "log_status", length: 255 })
  logStatus: string;

  @Column("varchar", { name: "wallet_type", length: 255 })
  walletType: string;

  @Column("varchar", { name: "transaction_type", length: 255 })
  transactionType: string;

  @Column("decimal", { name: "prev_card_limit", precision: 15, scale: 2 })
  prevCardLimit: string;

  @Column("decimal", { name: "prev_available_credit", precision: 15, scale: 2 })
  prevAvailableCredit: string;

  @Column("decimal", {
    name: "prev_available_balance",
    precision: 15,
    scale: 2,
  })
  prevAvailableBalance: string;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("decimal", {
    name: "next_card_limit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  nextCardLimit: string | null;

  @Column("decimal", {
    name: "next_available_credit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  nextAvailableCredit: string | null;

  @Column("decimal", {
    name: "next_available_balance",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  nextAvailableBalance: string | null;

  @Column("longtext", { name: "meta_data" })
  metaData: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.creditCardWalletHistories, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.creditCardWalletHistories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "wallet_id", referencedColumnName: "id" }])
  wallet: PersonalCardAccounts;
}
