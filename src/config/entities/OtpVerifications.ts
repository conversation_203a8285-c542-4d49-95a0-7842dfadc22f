import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("otp_verifications_purpose_index", ["purpose"], {})
@Index("otp_verifications_recipient_index", ["recipient"], {})
@Index("otp_verifications_status_index", ["status"], {})
@Index("otp_verifications_user_id_foreign", ["userId"], {})
@Entity("otp_verifications")
export class OtpVerifications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "purpose", nullable: true, length: 255 })
  purpose: string | null;

  @Column("varchar", { name: "verifies", length: 255 })
  verifies: string;

  @Column("varchar", { name: "route", length: 255 })
  route: string;

  @Column("datetime", { name: "verified_at", nullable: true })
  verifiedAt: Date | null;

  @Column("int", { name: "mail", nullable: true, default: () => "'0'" })
  mail: number | null;

  @Column("int", { name: "sms", nullable: true, default: () => "'0'" })
  sms: number | null;

  @Column("varchar", { name: "number", length: 255 })
  number: string;

  @Column("datetime", { name: "expires_at" })
  expiresAt: Date;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "recipient", nullable: true, length: 255 })
  recipient: string | null;

  @ManyToOne(() => Users, (users) => users.otpVerifications, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
