import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Clicks } from "./Clicks";
import { Rewards } from "./Rewards";

@Index("conversions_click_id_foreign", ["clickId"], {})
@Index(
  "conversions_conversionable_type_conversionable_id_index",
  ["conversionableType", "conversionableId"],
  {}
)
@Entity("conversions")
export class Conversions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "click_id", nullable: true, length: 36 })
  clickId: string | null;

  @Column("varchar", { name: "conversionable_type", length: 255 })
  conversionableType: string;

  @Column("bigint", { name: "conversionable_id", unsigned: true })
  conversionableId: string;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "confirmed_at", nullable: true })
  confirmedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Clicks, (clicks) => clicks.conversions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "click_id", referencedColumnName: "id" }])
  click: Clicks;

  @OneToMany(() => Rewards, (rewards) => rewards.conversion)
  rewards: Rewards[];
}
