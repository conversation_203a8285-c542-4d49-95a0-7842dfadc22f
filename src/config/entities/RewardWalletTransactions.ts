import { Column, <PERSON><PERSON><PERSON>, <PERSON>, Join<PERSON><PERSON>umn, <PERSON>ToOne } from "typeorm";
import { Cashbacks } from "./Cashbacks";
import { RewardWallets } from "./RewardWallets";
import { PersonalAccountStatements } from "./PersonalAccountStatements";

@Index("reward_wallet_transactions_cashback_id_foreign", ["cashbackId"], {})
@Index("reward_wallet_transactions_reference_unique", ["reference"], {
  unique: true,
})
@Index(
  "reward_wallet_transactions_reward_wallet_id_foreign",
  ["rewardWalletId"],
  {}
)
@Index("reward_wallet_transactions_source_index", ["source"], {})
@Index("reward_wallet_transactions_statement_id_foreign", ["statementId"], {})
@Index("reward_wallet_transactions_status_index", ["status"], {})
@Index("reward_wallet_transactions_type_index", ["type"], {})
@Entity("reward_wallet_transactions")
export class RewardWalletTransactions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "reward_wallet_id", nullable: true, length: 36 })
  rewardWalletId: string | null;

  @Column("double", { name: "amount", nullable: true, precision: 30, scale: 2 })
  amount: number | null;

  @Column("double", {
    name: "prevBalance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  prevBalance: number | null;

  @Column("double", {
    name: "currentBalance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  currentBalance: number | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("date", { name: "transaction_date", nullable: true })
  transactionDate: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @Column("varchar", { name: "source", nullable: true, length: 255 })
  source: string | null;

  @Column("char", { name: "cashback_id", nullable: true, length: 36 })
  cashbackId: string | null;

  @ManyToOne(
    () => Cashbacks,
    (cashbacks) => cashbacks.rewardWalletTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "cashback_id", referencedColumnName: "id" }])
  cashback: Cashbacks;

  @ManyToOne(
    () => RewardWallets,
    (rewardWallets) => rewardWallets.rewardWalletTransactions,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "reward_wallet_id", referencedColumnName: "id" }])
  rewardWallet: RewardWallets;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.rewardWalletTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;
}
