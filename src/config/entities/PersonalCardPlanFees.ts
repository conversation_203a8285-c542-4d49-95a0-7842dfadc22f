import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("personal_card_plan_fees")
export class PersonalCardPlanFees {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "personal_card_plans_id", unsigned: true })
  personalCardPlansId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("double", { name: "interest", precision: 22, default: () => "'0'" })
  interest: number;

  @Column("double", {
    name: "monthly_fees",
    precision: 22,
    default: () => "'0'",
  })
  monthlyFees: number;

  @Column("double", {
    name: "quartely_fees",
    precision: 22,
    default: () => "'0'",
  })
  quartelyFees: number;

  @Column("double", {
    name: "yearly_fees",
    precision: 22,
    default: () => "'0'",
  })
  yearlyFees: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
