import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { Communications } from "./Communications";

@Index("communication_templates_created_by_foreign", ["createdBy"], {})
@Index("communication_templates_subject_index", ["subject"], {})
@Index("communication_templates_updated_by_foreign", ["updatedBy"], {})
@Entity("communication_templates")
export class CommunicationTemplates {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "created_by", unsigned: true })
  createdBy: string;

  @Column("bigint", { name: "updated_by", unsigned: true })
  updatedBy: string;

  @Column("varchar", { name: "subject", length: 255 })
  subject: string;

  @Column("text", { name: "body" })
  body: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.communicationTemplates, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "created_by", referencedColumnName: "id" }])
  createdBy2: Users;

  @ManyToOne(() => Users, (users) => users.communicationTemplates2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "updated_by", referencedColumnName: "id" }])
  updatedBy2: Users;

  @OneToMany(() => Communications, (communications) => communications.template)
  communications: Communications[];
}
