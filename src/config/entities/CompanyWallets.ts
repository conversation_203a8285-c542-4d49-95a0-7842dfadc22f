import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyCardFeePayments } from "./CompanyCardFeePayments";
import { CompanyStatements } from "./CompanyStatements";
import { CompanyWalletTransactions } from "./CompanyWalletTransactions";
import { Companies } from "./Companies";
import { CreditAssignments } from "./CreditAssignments";
import { DebitAssignments } from "./DebitAssignments";

@Index("company_wallets_company_id_foreign", ["companyId"], {})
@Entity("company_wallets")
export class CompanyWallets {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("decimal", {
    name: "debit",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  debit: string;

  @Column("decimal", {
    name: "credit",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  credit: string;

  @Column("decimal", {
    name: "credit_limit",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  creditLimit: string;

  @Column("decimal", {
    name: "debit_limit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  debitLimit: string | null;

  @Column("enum", { name: "status", enum: ["active", "inactive"] })
  status: "active" | "inactive";

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.companyWallet
  )
  companyAccounts: CompanyAccounts[];

  @OneToMany(
    () => CompanyCardFeePayments,
    (companyCardFeePayments) => companyCardFeePayments.companyWallet
  )
  companyCardFeePayments: CompanyCardFeePayments[];

  @OneToMany(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyWallet
  )
  companyStatements: CompanyStatements[];

  @OneToMany(
    () => CompanyWalletTransactions,
    (companyWalletTransactions) => companyWalletTransactions.companyWallet
  )
  companyWalletTransactions: CompanyWalletTransactions[];

  @ManyToOne(() => Companies, (companies) => companies.companyWallets, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @OneToMany(
    () => CreditAssignments,
    (creditAssignments) => creditAssignments.companyWallet
  )
  creditAssignments: CreditAssignments[];

  @OneToMany(
    () => DebitAssignments,
    (debitAssignments) => debitAssignments.companyWallet
  )
  debitAssignments: DebitAssignments[];
}
