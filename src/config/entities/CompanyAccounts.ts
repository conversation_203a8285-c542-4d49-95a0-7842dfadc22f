import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccountStatements } from "./CompanyAccountStatements";
import { Companies } from "./Companies";
import { CompanyWallets } from "./CompanyWallets";
import { Groups } from "./Groups";
import { Users } from "./Users";
import { CompanyCards } from "./CompanyCards";
import { CompanyCreditTransactions } from "./CompanyCreditTransactions";
import { CompanyDebitTransactions } from "./CompanyDebitTransactions";
import { CreditAssignments } from "./CreditAssignments";
import { DebitAssignments } from "./DebitAssignments";
import { ExpenseRequests } from "./ExpenseRequests";

@Index("company_accounts_company_id_foreign", ["companyId"], {})
@Index("company_accounts_company_wallet_id_foreign", ["companyWalletId"], {})
@Index("company_accounts_group_id_foreign", ["groupId"], {})
@Index("company_accounts_manager_id_foreign", ["managerId"], {})
@Index("company_accounts_user_id_foreign", ["userId"], {})
@Entity("company_accounts")
export class CompanyAccounts {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "manager_id", nullable: true, unsigned: true })
  managerId: string | null;

  @Column("bigint", { name: "company_wallet_id", unsigned: true })
  companyWalletId: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("varchar", { name: "account_number", nullable: true, length: 255 })
  accountNumber: string | null;

  @Column("bigint", { name: "group_id", nullable: true, unsigned: true })
  groupId: string | null;

  @Column("enum", { name: "type", enum: ["debit", "credit"] })
  type: "debit" | "credit";

  @Column("decimal", { name: "limit", nullable: true, precision: 15, scale: 2 })
  limit: string | null;

  @Column("decimal", {
    name: "available_credit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  availableCredit: string | null;

  @Column("decimal", {
    name: "balance",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  balance: string | null;

  @Column("enum", { name: "balance_flag", enum: ["negative", "positive"] })
  balanceFlag: "negative" | "positive";

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive"],
    default: () => "'inactive'",
  })
  status: "active" | "inactive";

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyAccountStatements,
    (companyAccountStatements) => companyAccountStatements.companyAccount
  )
  companyAccountStatements: CompanyAccountStatements[];

  @ManyToOne(() => Companies, (companies) => companies.companyAccounts, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyWallets,
    (companyWallets) => companyWallets.companyAccounts,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_wallet_id", referencedColumnName: "id" }])
  companyWallet: CompanyWallets;

  @ManyToOne(() => Groups, (groups) => groups.companyAccounts, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "group_id", referencedColumnName: "id" }])
  group: Groups;

  @ManyToOne(() => Users, (users) => users.companyAccounts, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "manager_id", referencedColumnName: "id" }])
  manager: Users;

  @ManyToOne(() => Users, (users) => users.companyAccounts2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;

  @OneToMany(() => CompanyCards, (companyCards) => companyCards.companyAccount)
  companyCards: CompanyCards[];

  @OneToMany(
    () => CompanyCreditTransactions,
    (companyCreditTransactions) => companyCreditTransactions.companyAccount
  )
  companyCreditTransactions: CompanyCreditTransactions[];

  @OneToMany(
    () => CompanyDebitTransactions,
    (companyDebitTransactions) => companyDebitTransactions.companyAccount
  )
  companyDebitTransactions: CompanyDebitTransactions[];

  @OneToMany(
    () => CreditAssignments,
    (creditAssignments) => creditAssignments.companyAccount
  )
  creditAssignments: CreditAssignments[];

  @OneToMany(
    () => DebitAssignments,
    (debitAssignments) => debitAssignments.companyAccount
  )
  debitAssignments: DebitAssignments[];

  @OneToMany(
    () => ExpenseRequests,
    (expenseRequests) => expenseRequests.companyAccount
  )
  expenseRequests: ExpenseRequests[];
}
