import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { PersonalCreditCardRepayments } from "./PersonalCreditCardRepayments";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { CreditCardRepaymentReceipts } from "./CreditCardRepaymentReceipts";

@Index("credit_card_pending_repayments_admin_id_foreign", ["adminId"], {})
@Index(
  "credit_card_pending_repayments_repayment_id_foreign",
  ["repaymentId"],
  {}
)
@Index(
  "credit_card_pending_repayments_statement_id_foreign",
  ["statementId"],
  {}
)
@Index("credit_card_pending_repayments_status_index", ["status"], {})
@Index("credit_card_pending_repayments_user_id_foreign", ["userId"], {})
@Entity("credit_card_pending_repayments")
export class CreditCardPendingRepayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "admin_id", unsigned: true })
  adminId: string;

  @Column("bigint", { name: "statement_id", unsigned: true })
  statementId: string;

  @Column("bigint", { name: "repayment_id", nullable: true, unsigned: true })
  repaymentId: string | null;

  @Column("decimal", {
    name: "paid_amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  paidAmount: string;

  @Column("decimal", {
    name: "amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: string;

  @Column("decimal", {
    name: "total_outstanding",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  totalOutstanding: string;

  @Column("decimal", {
    name: "clearance_percentage",
    precision: 8,
    scale: 4,
    default: () => "'0.0000'",
  })
  clearancePercentage: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("json", { name: "meta", nullable: true })
  meta: object | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.creditCardPendingRepayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "admin_id", referencedColumnName: "id" }])
  admin: Users;

  @ManyToOne(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) =>
      personalCreditCardRepayments.creditCardPendingRepayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: PersonalCreditCardRepayments;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.creditCardPendingRepayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.creditCardPendingRepayments2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;

  @OneToMany(
    () => CreditCardRepaymentReceipts,
    (creditCardRepaymentReceipts) =>
      creditCardRepaymentReceipts.pendingRepayment
  )
  creditCardRepaymentReceipts: CreditCardRepaymentReceipts[];
}
