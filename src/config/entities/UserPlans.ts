import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalDeferredPlanPayments } from "./PersonalDeferredPlanPayments";
import { PersonalCardPlans } from "./PersonalCardPlans";
import { Users } from "./Users";

@Index("user_plans_original_plan_id_foreign", ["originalPlanId"], {})
@Index("user_plans_user_id_foreign", ["userId"], {})
@Entity("user_plans")
export class UserPlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", {
    name: "original_plan_id",
    nullable: true,
    unsigned: true,
  })
  originalPlanId: string | null;

  @Column("varchar", { name: "plan_name", nullable: true, length: 255 })
  planName: string | null;

  @Column("double", {
    name: "interest",
    nullable: true,
    precision: 8,
    scale: 2,
  })
  interest: number | null;

  @Column("decimal", { name: "fee", nullable: true, precision: 8, scale: 2 })
  fee: string | null;

  @Column("decimal", {
    name: "repayment_percentage",
    precision: 15,
    scale: 4,
    default: () => "'100.0000'",
  })
  repaymentPercentage: string;

  @Column("datetime", { name: "start_date", nullable: true })
  startDate: Date | null;

  @Column("datetime", { name: "end_date", nullable: true })
  endDate: Date | null;

  @Column("int", { name: "duration", default: () => "'365'" })
  duration: number;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("varchar", { name: "changed_status", nullable: true, length: 255 })
  changedStatus: string | null;

  @Column("varchar", { name: "previous_plan", nullable: true, length: 255 })
  previousPlan: string | null;

  @Column("datetime", { name: "downgraded_at", nullable: true })
  downgradedAt: Date | null;

  @Column("datetime", { name: "upgraded_at", nullable: true })
  upgradedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => PersonalDeferredPlanPayments,
    (personalDeferredPlanPayments) => personalDeferredPlanPayments.uniquePlan
  )
  personalDeferredPlanPayments: PersonalDeferredPlanPayments[];

  @ManyToOne(
    () => PersonalCardPlans,
    (personalCardPlans) => personalCardPlans.userPlans,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "original_plan_id", referencedColumnName: "id" }])
  originalPlan: PersonalCardPlans;

  @ManyToOne(() => Users, (users) => users.userPlans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
