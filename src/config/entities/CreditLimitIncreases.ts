import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("credit_limit_increases")
export class CreditLimitIncreases {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "reviewed_by", nullable: true, unsigned: true })
  reviewedBy: string | null;

  @Column("varchar", { name: "desired_limit", length: 255 })
  desiredLimit: string;

  @Column("varchar", { name: "salary", length: 255 })
  salary: string;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "reviewed_at", nullable: true })
  reviewedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
