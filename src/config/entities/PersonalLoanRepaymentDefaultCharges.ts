import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Loans } from "./Loans";
import { Repayments } from "./Repayments";
import { Users } from "./Users";

@Index(
  "personal_loan_repayment_default_charges_loan_id_foreign",
  ["loanId"],
  {}
)
@Index(
  "personal_loan_repayment_default_charges_repayment_id_foreign",
  ["repaymentId"],
  {}
)
@Index(
  "personal_loan_repayment_default_charges_user_id_foreign",
  ["userId"],
  {}
)
@Entity("personal_loan_repayment_default_charges")
export class PersonalLoanRepaymentDefaultCharges {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "repayment_id", unsigned: true })
  repaymentId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("double", {
    name: "amount",
    precision: 11,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: number;

  @Column("double", {
    name: "daily_amount",
    precision: 11,
    scale: 2,
    default: () => "'0.00'",
  })
  dailyAmount: number;

  @Column("double", {
    name: "repayment_amount",
    precision: 11,
    scale: 2,
    default: () => "'0.00'",
  })
  repaymentAmount: number;

  @Column("double", {
    name: "percentage",
    precision: 11,
    scale: 6,
    default: () => "'0.000000'",
  })
  percentage: number;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Loans,
    (loans) => loans.personalLoanRepaymentDefaultCharges,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(
    () => Repayments,
    (repayments) => repayments.personalLoanRepaymentDefaultCharges,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: Repayments;

  @ManyToOne(
    () => Users,
    (users) => users.personalLoanRepaymentDefaultCharges,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
