import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { ExpensePolicies } from "./ExpensePolicies";
import { Roles } from "./Roles";

@Index("expense_policy_role_expense_policy_id_foreign", ["expensePolicyId"], {})
@Index("expense_policy_role_role_id_foreign", ["roleId"], {})
@Entity("expense_policy_role")
export class ExpensePolicyRole {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;
  
  @Column("bigint", { name: "expense_policy_id", unsigned: true })
  expensePolicyId: string;

  @Column("bigint", { name: "role_id", unsigned: true })
  roleId: string;

  @ManyToOne(
    () => ExpensePolicies,
    (expensePolicies) => expensePolicies.expensePolicyRoles,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_policy_id", referencedColumnName: "id" }])
  expensePolicy: ExpensePolicies;

  @ManyToOne(() => Roles, (roles) => roles.expensePolicyRoles, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "role_id", referencedColumnName: "id" }])
  role: Roles;
}
