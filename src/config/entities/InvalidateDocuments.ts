import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("invalidate_documents_method_index", ["method"], {})
@Index("invalidate_documents_user_id_foreign", ["userId"], {})
@Entity("invalidate_documents")
export class InvalidateDocuments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "doc", nullable: true, length: 255 })
  doc: string | null;

  @Column("varchar", { name: "doc_type", nullable: true, length: 255 })
  docType: string | null;

  @Column("varchar", { name: "url", nullable: true, length: 255 })
  url: string | null;

  @Column("varchar", { name: "method", nullable: true, length: 255 })
  method: string | null;

  @Column("timestamp", { name: "invalidated_at", nullable: true })
  invalidatedAt: Date | null;

  @Column("timestamp", { name: "uploaded_at", nullable: true })
  uploadedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.invalidateDocuments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
