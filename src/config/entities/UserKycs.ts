import { Column, <PERSON>ti<PERSON>, <PERSON>, PrimaryGeneratedColumn } from "typeorm";

@Index("user_profiles_bvn_index", ["bvn"], {})
@Index("user_profiles_bvn_unique", ["bvn"], { unique: true })
@Index("user_profiles_driver_license_index", ["dlicense"], {})
@Index("user_profiles_drivers_license_unique", ["dlicense"], { unique: true })
@Index("user_profiles_nin_unique", ["nin"], { unique: true })
@Index("user_profiles_partner_index", ["partner"], {})
@Index("user_profiles_passport_index", ["passport"], {})
@Index("user_profiles_passport_unique", ["passport"], { unique: true })
@Index("user_profiles_reference_index", ["reference"], {})
@Index("user_profiles_status_index", ["status"], {})
@Index("user_profiles_user_id_foreign", ["userId"], {})
@Index("user_profiles_user_id_unique", ["userId"], { unique: true })
@Index("user_profiles_vnin_index", ["vnin"], {})
@Index("user_profiles_vnin_unique", ["vnin"], { unique: true })
@Index("user_profiles_voters_card_index", ["vcard"], {})
@Index("user_profiles_voters_card_unique", ["vcard"], { unique: true })
@Index("users_bvn_verified_at_index", ["bvnVerifiedAt"], {})
@Index("users_driver_license_verified_at_index", ["dlicenseVerifiedAt"], {})
@Index("users_nin_verified_at_index", ["ninVerifiedAt"], {})
@Index("users_passport_verified_at_index", ["passportVerifiedAt"], {})
@Index("users_vnin_verified_at_index", ["vninVerifiedAt"], {})
@Index("users_voters_card_verified_at_index", ["vcardVerifiedAt"], {})
@Entity("user_kycs")
export class UserKycs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unique: true, unsigned: true })
  userId: string;

  @Column("text", { name: "additional_phone", nullable: true })
  additionalPhone: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 255 })
  address: string | null;

  @Column("json", { name: "location", nullable: true })
  location: object | null;

  @Column("varchar", { name: "state", nullable: true, length: 255 })
  state: string | null;

  @Column("varchar", { name: "lga", nullable: true, length: 255 })
  lga: string | null;

  @Column("varchar", { name: "bvn", nullable: true, unique: true, length: 255 })
  bvn: string | null;

  @Column("varchar", { name: "bvn_first_name", nullable: true, length: 255 })
  bvnFirstName: string | null;

  @Column("varchar", { name: "bvn_middle_name", nullable: true, length: 255 })
  bvnMiddleName: string | null;

  @Column("varchar", { name: "bvn_last_name", nullable: true, length: 255 })
  bvnLastName: string | null;

  @Column("varchar", { name: "bvn_phone_no", nullable: true, length: 255 })
  bvnPhoneNo: string | null;

  @Column("varchar", { name: "bvn_date_of_birth", nullable: true, length: 255 })
  bvnDateOfBirth: string | null;

  @Column("datetime", { name: "bvn_verified_at", nullable: true })
  bvnVerifiedAt: Date | null;

  @Column("varchar", {
    name: "passport",
    nullable: true,
    unique: true,
    length: 255,
  })
  passport: string | null;

  @Column("varchar", {
    name: "passport_first_name",
    nullable: true,
    length: 255,
  })
  passportFirstName: string | null;

  @Column("varchar", {
    name: "passport_middle_name",
    nullable: true,
    length: 255,
  })
  passportMiddleName: string | null;

  @Column("varchar", {
    name: "passport_last_name",
    nullable: true,
    length: 255,
  })
  passportLastName: string | null;

  @Column("varchar", {
    name: "passport_date_of_birth",
    nullable: true,
    length: 255,
  })
  passportDateOfBirth: string | null;

  @Column("datetime", { name: "passport_verified_at", nullable: true })
  passportVerifiedAt: Date | null;

  @Column("varchar", {
    name: "dlicense",
    nullable: true,
    unique: true,
    length: 255,
  })
  dlicense: string | null;

  @Column("varchar", {
    name: "dlicense_first_name",
    nullable: true,
    length: 255,
  })
  dlicenseFirstName: string | null;

  @Column("varchar", {
    name: "dlicense_middle_name",
    nullable: true,
    length: 255,
  })
  dlicenseMiddleName: string | null;

  @Column("varchar", {
    name: "dlicense_last_name",
    nullable: true,
    length: 255,
  })
  dlicenseLastName: string | null;

  @Column("varchar", {
    name: "dlicense_date_of_birth",
    nullable: true,
    length: 255,
  })
  dlicenseDateOfBirth: string | null;

  @Column("datetime", { name: "dlicense_verified_at", nullable: true })
  dlicenseVerifiedAt: Date | null;

  @Column("varchar", { name: "nin", nullable: true, unique: true, length: 255 })
  nin: string | null;

  @Column("varchar", { name: "nin_first_name", nullable: true, length: 255 })
  ninFirstName: string | null;

  @Column("varchar", { name: "nin_middle_name", nullable: true, length: 255 })
  ninMiddleName: string | null;

  @Column("varchar", { name: "nin_last_name", nullable: true, length: 255 })
  ninLastName: string | null;

  @Column("varchar", { name: "nin_phone_no", nullable: true, length: 255 })
  ninPhoneNo: string | null;

  @Column("varchar", { name: "nin_date_of_birth", nullable: true, length: 255 })
  ninDateOfBirth: string | null;

  @Column("datetime", { name: "nin_verified_at", nullable: true })
  ninVerifiedAt: Date | null;

  @Column("varchar", {
    name: "vnin",
    nullable: true,
    unique: true,
    length: 255,
  })
  vnin: string | null;

  @Column("datetime", { name: "vnin_verified_at", nullable: true })
  vninVerifiedAt: Date | null;

  @Column("varchar", {
    name: "vcard",
    nullable: true,
    unique: true,
    length: 255,
  })
  vcard: string | null;

  @Column("varchar", { name: "vcard_first_name", nullable: true, length: 255 })
  vcardFirstName: string | null;

  @Column("varchar", { name: "vcard_last_name", nullable: true, length: 255 })
  vcardLastName: string | null;

  @Column("varchar", {
    name: "vcard_date_of_birth",
    nullable: true,
    length: 255,
  })
  vcardDateOfBirth: string | null;

  @Column("datetime", { name: "vcard_verified_at", nullable: true })
  vcardVerifiedAt: Date | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("tinyint", { name: "verified", nullable: true, width: 1 })
  verified: boolean | null;

  @Column("varchar", { name: "verify_method", nullable: true, length: 255 })
  verifyMethod: string | null;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("varchar", { name: "partner", nullable: true, length: 255 })
  partner: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
