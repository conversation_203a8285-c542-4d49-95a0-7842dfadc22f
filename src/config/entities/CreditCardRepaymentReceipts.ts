import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CreditCardPendingRepayments } from "./CreditCardPendingRepayments";
import { PersonalCreditCardRepayments } from "./PersonalCreditCardRepayments";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { Users } from "./Users";

@Index(
  "credit_card_repayment_receipts_pending_repayment_id_foreign",
  ["pendingRepaymentId"],
  {}
)
@Index(
  "credit_card_repayment_receipts_repayment_id_foreign",
  ["repaymentId"],
  {}
)
@Index(
  "credit_card_repayment_receipts_statement_id_foreign",
  ["statementId"],
  {}
)
@Index("credit_card_repayment_receipts_status_index", ["status"], {})
@Index("credit_card_repayment_receipts_user_id_foreign", ["userId"], {})
@Entity("credit_card_repayment_receipts")
export class CreditCardRepaymentReceipts {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @Column("bigint", {
    name: "pending_repayment_id",
    nullable: true,
    unsigned: true,
  })
  pendingRepaymentId: string | null;

  @Column("bigint", { name: "repayment_id", nullable: true, unsigned: true })
  repaymentId: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'requested'",
  })
  status: string;

  @Column("varchar", { name: "url", length: 255 })
  url: string;

  @Column("json", { name: "data", nullable: true })
  data: object | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => CreditCardPendingRepayments,
    (creditCardPendingRepayments) =>
      creditCardPendingRepayments.creditCardRepaymentReceipts,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "pending_repayment_id", referencedColumnName: "id" }])
  pendingRepayment: CreditCardPendingRepayments;

  @ManyToOne(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) =>
      personalCreditCardRepayments.creditCardRepaymentReceipts,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: PersonalCreditCardRepayments;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.creditCardRepaymentReceipts,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.creditCardRepaymentReceipts, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
