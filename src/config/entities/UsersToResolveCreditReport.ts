import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("users_to_resolve_credit_report")
export class UsersToResolveCreditReport {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id" })
  userId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "completed", "failed"],
    default: () => "'pending'",
  })
  status: "pending" | "completed" | "failed";

  @Column("varchar", { name: "failure_reason", nullable: true, length: 255 })
  failureReason: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
