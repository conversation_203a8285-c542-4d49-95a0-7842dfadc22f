import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { PersonalDefaultCharges } from "./PersonalDefaultCharges";
import { PersonalDeferredPlanPayments } from "./PersonalDeferredPlanPayments";
import { PersonalCardMaintenanceFees } from "./PersonalCardMaintenanceFees";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { Users } from "./Users";

@Index("personal_cleared_statements_account_id_foreign", ["accountId"], {})
@Index("personal_cleared_statements_created_at_index", ["createdAt"], {})
@Index("personal_cleared_statements_created_date_index", ["createdDate"], {})
@Index(
  "personal_cleared_statements_default_charge_id_foreign",
  ["defaultChargeId"],
  {}
)
@Index(
  "personal_cleared_statements_deferred_plan_id_foreign",
  ["deferredPlanId"],
  {}
)
@Index("personal_cleared_statements_end_date_index", ["endDate"], {})
@Index(
  "personal_cleared_statements_maintenance_fee_id_foreign",
  ["maintenanceFeeId"],
  {}
)
@Index("personal_cleared_statements_start_date_index", ["startDate"], {})
@Index("personal_cleared_statements_statement_id_foreign", ["statementId"], {})
@Index("personal_cleared_statements_user_id_foreign", ["userId"], {})
@Entity("personal_cleared_statements")
export class PersonalClearedStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @Column("bigint", {
    name: "maintenance_fee_id",
    nullable: true,
    unsigned: true,
  })
  maintenanceFeeId: string | null;

  @Column("bigint", {
    name: "default_charge_id",
    nullable: true,
    unsigned: true,
  })
  defaultChargeId: string | null;

  @Column("bigint", {
    name: "deferred_plan_id",
    nullable: true,
    unsigned: true,
  })
  deferredPlanId: string | null;

  @Column("decimal", { name: "total", precision: 15, scale: 2 })
  total: string;

  @Column("date", { name: "start_date", nullable: true })
  startDate: string | null;

  @Column("date", { name: "end_date", nullable: true })
  endDate: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalClearedStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @ManyToOne(
    () => PersonalDefaultCharges,
    (personalDefaultCharges) =>
      personalDefaultCharges.personalClearedStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "default_charge_id", referencedColumnName: "id" }])
  defaultCharge: PersonalDefaultCharges;

  @ManyToOne(
    () => PersonalDeferredPlanPayments,
    (personalDeferredPlanPayments) =>
      personalDeferredPlanPayments.personalClearedStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "deferred_plan_id", referencedColumnName: "id" }])
  deferredPlan: PersonalDeferredPlanPayments;

  @ManyToOne(
    () => PersonalCardMaintenanceFees,
    (personalCardMaintenanceFees) =>
      personalCardMaintenanceFees.personalClearedStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "maintenance_fee_id", referencedColumnName: "id" }])
  maintenanceFee: PersonalCardMaintenanceFees;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalClearedStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.personalClearedStatements, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
