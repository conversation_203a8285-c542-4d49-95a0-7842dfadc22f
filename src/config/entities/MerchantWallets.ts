import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("merchant_wallets")
export class MerchantWallets {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "merchant_id", unsigned: true })
  merchantId: string;

  @Column("varchar", { name: "wallet_id", length: 255 })
  walletId: string;

  @Column("varchar", {
    name: "virtual_account_id",
    nullable: true,
    length: 255,
  })
  virtualAccountId: string | null;

  @Column("tinyint", {
    name: "is_email_synced_to_wallets",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  isEmailSyncedToWallets: boolean | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
