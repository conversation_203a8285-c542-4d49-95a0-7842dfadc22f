import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index(
  "paystack_transfer_recipients_ptrable_type_ptrable_id_index",
  ["ptrableType", "ptrableId"],
  {}
)
@Index("paystack_transfer_recipients_user_id_foreign", ["userId"], {})
@Entity("paystack_transfer_recipients")
export class PaystackTransferRecipients {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("varchar", { name: "recipient_code", length: 255 })
  recipientCode: string;

  @Column("varchar", { name: "account_number", length: 255 })
  accountNumber: string;

  @Column("varchar", { name: "type", length: 255, default: () => "'nuban'" })
  type: string;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @Column("varchar", { name: "bank_code", nullable: true, length: 255 })
  bankCode: string | null;

  @Column("varchar", { name: "currency", length: 255, default: () => "'NGN'" })
  currency: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "ptrable_type", nullable: true, length: 255 })
  ptrableType: string | null;

  @Column("char", { name: "ptrable_id", nullable: true, length: 36 })
  ptrableId: string | null;

  @ManyToOne(() => Users, (users) => users.paystackTransferRecipients, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
