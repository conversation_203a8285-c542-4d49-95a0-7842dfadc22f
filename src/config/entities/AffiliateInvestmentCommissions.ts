import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "affiliate_investment_commissions_affiliate_id_index",
  ["affiliateId"],
  {}
)
@Index("affiliate_investment_commissions_user_id_index", ["userId"], {})
@Entity("affiliate_investment_commissions")
export class AffiliateInvestmentCommissions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("char", { name: "affiliate_id", length: 36 })
  affiliateId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "investment_id", length: 255 })
  investmentId: string;

  @Column("decimal", {
    name: "investment_amount",
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  investmentAmount: string;

  @Column("int", { name: "investment_tenure", unsigned: true })
  investmentTenure: number;

  @Column("decimal", {
    name: "commission_amount",
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  commissionAmount: string;

  @Column("decimal", {
    name: "commission_rate",
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  commissionRate: string;

  @Column("timestamp", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
