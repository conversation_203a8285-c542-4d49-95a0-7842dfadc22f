import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("loanbot_verifications_created_at_index", ["createdAt"], {})
@Index("loanbot_verifications_loanbot_id_index", ["loanbotId"], {})
@Index("loanbot_verifications_started_at_index", ["startedAt"], {})
@Index("loanbot_verifications_status_index", ["status"], {})
@Index("loanbot_verifications_user_id_foreign", ["userId"], {})
@Entity("loanbot_verifications")
export class LoanbotVerifications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "loanbot_id", nullable: true, length: 255 })
  loanbotId: string | null;

  @Column("varchar", { name: "remark", nullable: true, length: 255 })
  remark: string | null;

  @Column("double", { name: "amount", nullable: true, precision: 22 })
  amount: number | null;

  @Column("double", {
    name: "loan_limit",
    nullable: true,
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  loanLimit: number | null;

  @Column("json", { name: "credit_summary", nullable: true })
  creditSummary: object | null;

  @Column("json", { name: "credit_score_data", nullable: true })
  creditScoreData: object | null;

  @Column("json", { name: "performance_summary", nullable: true })
  performanceSummary: object | null;

  @Column("varchar", { name: "status", nullable: true, length: 255 })
  status: string | null;

  @Column("varchar", {
    name: "loanbot_identifier",
    nullable: true,
    length: 255,
  })
  loanbotIdentifier: string | null;

  @Column("int", { name: "retries", nullable: true, default: () => "'0'" })
  retries: number | null;

  @Column("tinyint", { name: "has_used_payment", nullable: true, width: 1 })
  hasUsedPayment: boolean | null;

  @Column("tinyint", {
    name: "has_made_first_time_payment",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  hasMadeFirstTimePayment: boolean | null;

  @Column("datetime", {
    name: "started_at",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  startedAt: Date | null;

  @Column("timestamp", { name: "credit_report_last_checked", nullable: true })
  creditReportLastChecked: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.loanbotVerifications, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
