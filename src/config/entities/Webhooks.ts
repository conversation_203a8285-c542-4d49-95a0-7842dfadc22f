import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("webhooks")
export class Webhooks {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "ip", nullable: true, length: 255 })
  ip: string | null;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @Column("json", { name: "request", nullable: true })
  request: object | null;

  @Column("json", { name: "request_data", nullable: true })
  requestData: object | null;

  @Column("json", { name: "response", nullable: true })
  response: object | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
