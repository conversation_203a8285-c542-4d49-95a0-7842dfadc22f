import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { ActivityUser } from "./ActivityUser";

@Entity("activities")
export class Activities {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("json", { name: "data" })
  data: object;

  @Column("int", { name: "level" })
  level: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(() => ActivityUser, (activityUser) => activityUser.activity)
  activityUsers: ActivityUser[];
}
