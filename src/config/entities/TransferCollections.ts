import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("transfer_collections_provider_index", ["provider"], {})
@Index("transfer_collections_reference_unique", ["reference"], { unique: true })
@Index("transfer_collections_status_index", ["status"], {})
@Index("transfer_collections_type_index", ["type"], {})
@Index("transfer_collections_user_id_foreign", ["userId"], {})
@Entity("transfer_collections")
export class TransferCollections {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("varchar", { name: "provider", length: 255 })
  provider: string;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("timestamp", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.transferCollections, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
