import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Merchants } from "./Merchants";

@Index("merchant_business_information_merchant_id_foreign", ["merchantId"], {})
@Entity("merchant_business_information")
export class MerchantBusinessInformation {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", length: 36 })
  merchantId: string;

  @Column("varchar", { name: "business_name", nullable: true, length: 255 })
  businessName: string | null;

  @Column("text", { name: "business_description", nullable: true })
  businessDescription: string | null;

  @Column("text", { name: "business_certificate", nullable: true })
  businessCertificate: string | null;

  @Column("varchar", { name: "general_email", nullable: true, length: 255 })
  generalEmail: string | null;

  @Column("varchar", { name: "support_email", nullable: true, length: 255 })
  supportEmail: string | null;

  @Column("varchar", { name: "dispute_email", nullable: true, length: 255 })
  disputeEmail: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => Merchants,
    (merchants) => merchants.merchantBusinessInformations,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
