import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { MerchantOrders } from "./MerchantOrders";
import { Lenders } from "./Lenders";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { Users } from "./Users";

@Index("personal_card_transactions_category_index", ["category"], {})
@Index("personal_card_transactions_condition_index", ["condition"], {})
@Index("personal_card_transactions_created_date_index", ["createdDate"], {})
@Index("personal_card_transactions_currency_index", ["currency"], {})
@Index("personal_card_transactions_lender_id_foreign", ["lenderId"], {})
@Index(
  "personal_card_transactions_personal_card_accounts_id_foreign",
  ["personalCardAccountsId"],
  {}
)
@Index("personal_card_transactions_statement_id_foreign", ["statementId"], {})
@Index("personal_card_transactions_status_index", ["status"], {})
@Index("personal_card_transactions_type_index", ["type"], {})
@Index("personal_card_transactions_user_id_foreign", ["userId"], {})
@Index("personal_card_transactions_wallet_type_index", ["walletType"], {})
@Entity("personal_card_transactions")
export class PersonalCardTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", {
    name: "personal_card_accounts_id",
    nullable: true,
    unsigned: true,
  })
  personalCardAccountsId: string | null;

  @Column("char", { name: "wallet_id", nullable: true, length: 36 })
  walletId: string | null;

  @Column("varchar", {
    name: "wallet_type",
    length: 255,
    default: () => "'credpal_card'",
  })
  walletType: string;

  @Column("varchar", { name: "date", length: 255 })
  date: string;

  @Column("varchar", { name: "amount", length: 255 })
  amount: string;

  @Column("varchar", { name: "description", length: 255 })
  description: string;

  @Column("varchar", { name: "category", length: 255 })
  category: string;

  @Column("varchar", {
    name: "transaction_reference",
    nullable: true,
    length: 255,
  })
  transactionReference: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("enum", { name: "type", nullable: true, enum: ["debit", "credit"] })
  type: "debit" | "credit" | null;

  @Column("varchar", {
    name: "currency",
    nullable: true,
    length: 255,
    default: () => "'ngn'",
  })
  currency: string | null;

  @Column("varchar", { name: "condition", nullable: true, length: 255 })
  condition: string | null;

  @Column("varchar", { name: "status", nullable: true, length: 250 })
  status: string | null;

  @Column("varchar", {
    name: "statement_status",
    length: 255,
    default: () => "'open'",
  })
  statementStatus: string;

  @Column("tinyint", { name: "carried_over", width: 1, default: () => "'0'" })
  carriedOver: boolean;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @Column("double", { name: "batch", nullable: true, precision: 22 })
  batch: number | null;

  @Column("bigint", { name: "lender_id", nullable: true, unsigned: true })
  lenderId: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("json", { name: "metadata", nullable: true })
  metadata: object | null;

  @OneToMany(
    () => MerchantOrders,
    (merchantOrders) => merchantOrders.transaction
  )
  merchantOrders: MerchantOrders[];

  @ManyToOne(() => Lenders, (lenders) => lenders.personalCardTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lender_id", referencedColumnName: "id" }])
  lender: Lenders;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalCardTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_card_accounts_id", referencedColumnName: "id" },
  ])
  personalCardAccounts: PersonalCardAccounts;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalCardTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.personalCardTransactions, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
