import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Activities } from "./Activities";

@Index("activity_user_activity_id_foreign", ["activityId"], {})
@Entity("activity_user")
export class ActivityUser {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "activity_id", unsigned: true })
  activityId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("enum", {
    name: "position",
    enum: ["creator", "target"],
    default: () => "'creator'",
  })
  position: "creator" | "target";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Activities, (activities) => activities.activityUsers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "activity_id", referencedColumnName: "id" }])
  activity: Activities;
}
