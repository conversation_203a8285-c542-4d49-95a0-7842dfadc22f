import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { TripTravellers } from "./TripTravellers";
import { Users } from "./Users";

@Index("trips_account_id_index", ["accountId"], {})
@Index("trips_amount_index", ["amount"], {})
@Index("trips_arrival_date_index", ["arrivalDate"], {})
@Index("trips_city_index", ["city"], {})
@Index("trips_contact_email_index", ["contactEmail"], {})
@Index("trips_country_code_index", ["countryCode"], {})
@Index("trips_departure_date_index", ["departureDate"], {})
@Index("trips_status_index", ["status"], {})
@Index("trips_transaction_reference_index", ["transactionReference"], {})
@Index("trips_type_index", ["type"], {})
@Index("trips_user_id_foreign", ["userId"], {})
@Index("trips_wallet_id_index", ["walletId"], {})
@Index("trips_wallet_type_index", ["walletType"], {})
@Entity("trips")
export class Trips {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("varchar", { name: "wallet_id", nullable: true, length: 255 })
  walletId: string | null;

  @Column("varchar", { name: "wallet_type", length: 255 })
  walletType: string;

  @Column("varchar", { name: "user_type", length: 255 })
  userType: string;

  @Column("double", {
    name: "amount",
    precision: 30,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: number;

  @Column("varchar", { name: "mark_up_percentage", length: 255 })
  markUpPercentage: string;

  @Column("varchar", { name: "transaction_reference", length: 255 })
  transactionReference: string;

  @Column("varchar", { name: "session_id", length: 255 })
  sessionId: string;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("varchar", { name: "recipient_number", length: 255 })
  recipientNumber: string;

  @Column("varchar", { name: "address", length: 255 })
  address: string;

  @Column("varchar", { name: "city", length: 255 })
  city: string;

  @Column("varchar", { name: "country_code", length: 255 })
  countryCode: string;

  @Column("varchar", { name: "contact_mobile_no", length: 255 })
  contactMobileNo: string;

  @Column("varchar", { name: "contact_email", length: 255 })
  contactEmail: string;

  @Column("json", { name: "request_data" })
  requestData: object;

  @Column("varchar", { name: "reference_number", nullable: true, length: 255 })
  referenceNumber: string | null;

  @Column("varchar", {
    name: "booking_reference_id",
    nullable: true,
    length: 255,
  })
  bookingReferenceId: string | null;

  @Column("varchar", {
    name: "booking_reference_type",
    nullable: true,
    length: 255,
  })
  bookingReferenceType: string | null;

  @Column("varchar", { name: "ticket_time_limit", nullable: true, length: 255 })
  ticketTimeLimit: string | null;

  @Column("json", { name: "response_data", nullable: true })
  responseData: object | null;

  @Column("varchar", {
    name: "operating_airline_code",
    nullable: true,
    length: 255,
  })
  operatingAirlineCode: string | null;

  @Column("varchar", {
    name: "operating_airline_name",
    nullable: true,
    length: 255,
  })
  operatingAirlineName: string | null;

  @Column("varchar", { name: "flight_class", nullable: true, length: 255 })
  flightClass: string | null;

  @Column("varchar", { name: "flight_number", nullable: true, length: 255 })
  flightNumber: string | null;

  @Column("varchar", { name: "flight_route_type", nullable: true, length: 255 })
  flightRouteType: string | null;

  @Column("varchar", { name: "aircraft", nullable: true, length: 255 })
  aircraft: string | null;

  @Column("varchar", {
    name: "departure_airport_code",
    nullable: true,
    length: 255,
  })
  departureAirportCode: string | null;

  @Column("varchar", {
    name: "departure_airport_name",
    nullable: true,
    length: 255,
  })
  departureAirportName: string | null;

  @Column("varchar", {
    name: "arrival_airport_code",
    nullable: true,
    length: 255,
  })
  arrivalAirportCode: string | null;

  @Column("varchar", {
    name: "arrival_airport_name",
    nullable: true,
    length: 255,
  })
  arrivalAirportName: string | null;

  @Column("varchar", { name: "departure_date", nullable: true, length: 255 })
  departureDate: string | null;

  @Column("varchar", { name: "arrival_date", nullable: true, length: 255 })
  arrivalDate: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(() => TripTravellers, (tripTravellers) => tripTravellers.trip)
  tripTravellers: TripTravellers[];

  @ManyToOne(() => Users, (users) => users.trips, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
