import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyExpenseSubCategories } from "./CompanyExpenseSubCategories";

@Index("company_expense_categories_company_id_foreign", ["companyId"], {})
@Entity("company_expense_categories")
export class CompanyExpenseCategories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyExpenseCategories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @OneToMany(
    () => CompanyExpenseSubCategories,
    (companyExpenseSubCategories) => companyExpenseSubCategories.companyCategory
  )
  companyExpenseSubCategories: CompanyExpenseSubCategories[];
}
