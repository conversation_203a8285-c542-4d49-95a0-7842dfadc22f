import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Users } from "./Users";

@Index("user_referrals_referred_id_foreign", ["referredId"], {})
@Index("user_referrals_user_id_foreign", ["userId"], {})
@Entity("user_referrals")
export class UserReferrals {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "referred_id", unsigned: true })
  referredId: string;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "confirmed_at", nullable: true })
  confirmedAt: Date | null;

  @Column("timestamp", { name: "redeemed_at", nullable: true })
  redeemedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("double", {
    name: "user_commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  userCommission: number | null;

  @Column("double", {
    name: "referred_commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  referredCommission: number | null;

  @Column("varchar", { name: "utm_campaign", nullable: true, length: 255 })
  utmCampaign: string | null;

  @Column("varchar", { name: "utm_source", nullable: true, length: 255 })
  utmSource: string | null;

  @Column("varchar", { name: "utm_medium", nullable: true, length: 255 })
  utmMedium: string | null;

  @ManyToOne(() => Users, (users) => users.userReferrals, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "referred_id", referencedColumnName: "id" }])
  referred: Users;

  @ManyToOne(() => Users, (users) => users.userReferrals2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
