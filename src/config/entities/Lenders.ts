import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { LenderBatches } from "./LenderBatches";
import { LenderCreditCardLoans } from "./LenderCreditCardLoans";
import { LenderCreditCardRepayments } from "./LenderCreditCardRepayments";
import { LenderRepayments } from "./LenderRepayments";
import { Loans } from "./Loans";
import { PersonalCardTransactions } from "./PersonalCardTransactions";

@Entity("lenders")
export class Lenders {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(() => LenderBatches, (lenderBatches) => lenderBatches.lender)
  lenderBatches: LenderBatches[];

  @OneToMany(
    () => LenderCreditCardLoans,
    (lenderCreditCardLoans) => lenderCreditCardLoans.lender
  )
  lenderCreditCardLoans: LenderCreditCardLoans[];

  @OneToMany(
    () => LenderCreditCardRepayments,
    (lenderCreditCardRepayments) => lenderCreditCardRepayments.lender
  )
  lenderCreditCardRepayments: LenderCreditCardRepayments[];

  @OneToMany(
    () => LenderRepayments,
    (lenderRepayments) => lenderRepayments.lender
  )
  lenderRepayments: LenderRepayments[];

  @OneToMany(() => Loans, (loans) => loans.lender)
  loans: Loans[];

  @OneToMany(
    () => PersonalCardTransactions,
    (personalCardTransactions) => personalCardTransactions.lender
  )
  personalCardTransactions: PersonalCardTransactions[];
}
