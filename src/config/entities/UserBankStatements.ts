import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { LoanbotBlankStatements } from "./LoanbotBlankStatements";
import { LoanbotStatementAnalyses } from "./LoanbotStatementAnalyses";
import { Users } from "./Users";

@Index("user_bank_statements_status_index", ["status"], {})
@Index("user_bank_statements_user_id_foreign", ["userId"], {})
@Index("user_bank_statements_verified_at_index", ["verifiedAt"], {})
@Index("user_bank_statements_verified_with_index", ["verifiedWith"], {})
@Entity("user_bank_statements")
export class UserBankStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("varchar", { name: "staff_name", length: 255 })
  staffName: string;

  @Column("varchar", { name: "account_no", nullable: true, length: 255 })
  accountNo: string | null;

  @Column("varchar", { name: "bank_name", nullable: true, length: 255 })
  bankName: string | null;

  @Column("varchar", { name: "phone", nullable: true, length: 255 })
  phone: string | null;

  @Column("varchar", { name: "code", nullable: true, length: 255 })
  code: string | null;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("enum", {
    name: "verified_with",
    enum: ["mbs", "mono", "okra", "ussd", "mobile"],
  })
  verifiedWith: "mbs" | "mono" | "okra" | "ussd" | "mobile";

  @Column("varchar", { name: "verified_by", nullable: true, length: 255 })
  verifiedBy: string | null;

  @Column("timestamp", { name: "verified_at", nullable: true })
  verifiedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => LoanbotBlankStatements,
    (loanbotBlankStatements) => loanbotBlankStatements.userBankStatement
  )
  loanbotBlankStatements: LoanbotBlankStatements[];

  @OneToMany(
    () => LoanbotStatementAnalyses,
    (loanbotStatementAnalyses) => loanbotStatementAnalyses.userBankStatement
  )
  loanbotStatementAnalyses: LoanbotStatementAnalyses[];

  @ManyToOne(() => Users, (users) => users.userBankStatements, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
