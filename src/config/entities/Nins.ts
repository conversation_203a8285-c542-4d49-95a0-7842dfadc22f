import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("nins")
export class Nins {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "nin", length: 255 })
  nin: string;

  @Column("longtext", { name: "data" })
  data: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
