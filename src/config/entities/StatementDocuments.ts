import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { Users } from "./Users";

@Index("statement_documents_statement_id_foreign", ["statementId"], {})
@Index("statement_documents_user_id_foreign", ["userId"], {})
@Entity("statement_documents")
export class StatementDocuments {
  @PrimaryGeneratedColumn({ type: "int", name: "id", unsigned: true })
  id: number;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @Column("varchar", { name: "file_name", length: 255 })
  fileName: string;

  @Column("longtext", { name: "url" })
  url: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) => personalAccountStatements.statementDocuments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.statementDocuments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
