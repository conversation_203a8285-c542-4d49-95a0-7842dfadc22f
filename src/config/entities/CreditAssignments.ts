import {
  Column,
  Entity,
  Index,
  Join<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyWallets } from "./CompanyWallets";

@Index(
  "credit_assignments_company_account_id_foreign",
  ["companyAccountId"],
  {}
)
@Index("credit_assignments_company_wallet_id_foreign", ["companyWalletId"], {})
@Entity("credit_assignments")
export class CreditAssignments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_wallet_id", unsigned: true })
  companyWalletId: string;

  @Column("bigint", { name: "company_account_id", unsigned: true })
  companyAccountId: string;

  @Column("enum", {
    name: "transaction_type",
    enum: ["card-creation", "top-up", "withdrawal"],
  })
  transactionType: "card-creation" | "top-up" | "withdrawal";

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", { name: "amount_flag", enum: ["positive", "negative"] })
  amountFlag: "positive" | "negative";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.creditAssignments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_account_id", referencedColumnName: "id" }])
  companyAccount: CompanyAccounts;

  @ManyToOne(
    () => CompanyWallets,
    (companyWallets) => companyWallets.creditAssignments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_wallet_id", referencedColumnName: "id" }])
  companyWallet: CompanyWallets;
}
