import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("merchant_password_resets_email_index", ["email"], {})
@Index("merchant_password_resets_token_unique", ["token"], { unique: true })
@Entity("merchant_password_resets")
export class MerchantPasswordResets {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "email", length: 255 })
  email: string;

  @Column("varchar", { name: "token", unique: true, length: 255 })
  token: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
