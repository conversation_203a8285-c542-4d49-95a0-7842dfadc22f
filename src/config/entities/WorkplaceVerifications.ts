import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("workplace_verifications_user_id_foreign", ["userId"], {})
@Entity("workplace_verifications")
export class WorkplaceVerifications {
  @PrimaryGeneratedColumn({ type: "int", name: "id", unsigned: true })
  id: number;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "remark", nullable: true, length: 255 })
  remark: string | null;

  @Column("double", {
    name: "loan_limit",
    nullable: true,
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  loanLimit: number | null;

  @Column("double", {
    name: "credit_limit",
    nullable: true,
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  creditLimit: number | null;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.workplaceVerifications, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
