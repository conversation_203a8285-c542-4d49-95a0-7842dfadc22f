import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DeleteDateColumn,
} from 'typeorm';
import { BaseEntity } from '../repository/base-entity';
import { Users } from './Users';
import { MerchantOrders } from './MerchantOrders';

@Entity('cart_items')
export class CartItems extends BaseEntity {
  @Column('varchar', { length: 255 })
  name: string;

  @Column('int')
  quantity: number;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('varchar', { length: 255, nullable: true })
  merchant: string;

  @Column('varchar', { length: 255 })
  status: string;

  @ManyToOne(() => Users, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: Users;

  @Column('bigint', { name: 'userId', unsigned: true })
  userId: string;

  @ManyToOne(() => MerchantOrders, (order) => order.cartItems, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'merchantOrderId' })
  merchantOrder: MerchantOrders;

  @Column('bigint', { name: 'merchantOrderId', unsigned: true })
  merchantOrderId: string;

  @DeleteDateColumn()
  deletedAt: Date;
}
