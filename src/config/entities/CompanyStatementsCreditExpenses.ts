import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("company_statements_credit_expenses")
export class CompanyStatementsCreditExpenses {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
