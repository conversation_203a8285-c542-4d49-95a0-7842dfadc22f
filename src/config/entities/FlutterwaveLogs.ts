import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("flutterwave_logs_user_id_foreign", ["userId"], {})
@Entity("flutterwave_logs")
export class FlutterwaveLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "transaction", length: 255 })
  transaction: string;

  @Column("varchar", {
    name: "authorization_code",
    nullable: true,
    length: 255,
  })
  authorizationCode: string | null;

  @Column("varchar", { name: "request_ref", length: 255 })
  requestRef: string;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", {
    name: "payment_method",
    nullable: true,
    enum: ["repayment_card", "credit_card"],
  })
  paymentMethod: "repayment_card" | "credit_card" | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.flutterwaveLogs, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
