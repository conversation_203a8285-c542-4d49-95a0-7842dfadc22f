import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Merchants } from "./Merchants";

@Index("merchant_configurations_merchant_id_foreign", ["merchantId"], {})
@Entity("merchant_configurations")
export class MerchantConfigurations {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("double", {
    name: "max_order_amount",
    nullable: true,
    precision: 22,
    default: () => "'5000000'",
  })
  maxOrderAmount: number | null;

  @Column("tinyint", {
    name: "caas_send_funds_to_merchant",
    width: 1,
    default: () => "'0'",
  })
  caasSendFundsToMerchant: boolean;

  @Column("tinyint", { name: "caas_secured", width: 1, default: () => "'0'" })
  caasSecured: boolean;

  @Column("tinyint", {
    name: "caas_partially_secured",
    width: 1,
    default: () => "'0'",
  })
  caasPartiallySecured: boolean;

  @Column("tinyint", { name: "caas_unsecured", width: 1, default: () => "'0'" })
  caasUnsecured: boolean;

  @Column("tinyint", {
    name: "enable_two_factor",
    width: 1,
    default: () => "'0'",
  })
  enableTwoFactor: boolean;

  @Column("tinyint", {
    name: "spread_payment_enabled",
    width: 1,
    default: () => "'0'",
  })
  spreadPaymentEnabled: boolean;

  @Column("varchar", { name: "payment_link", nullable: true, length: 255 })
  paymentLink: string | null;

  @Column("tinyint", {
    name: "show_onboarding",
    nullable: true,
    width: 1,
    default: () => "'1'",
  })
  showOnboarding: boolean | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantConfigurations, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
