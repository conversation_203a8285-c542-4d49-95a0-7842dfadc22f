import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ExpenseCategories } from "./ExpenseCategories";

@Index(
  "expense_sub_categories_expense_category_id_foreign",
  ["expenseCategoryId"],
  {}
)
@Entity("expense_sub_categories")
export class ExpenseSubCategories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "expense_category_id", unsigned: true })
  expenseCategoryId: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => ExpenseCategories,
    (expenseCategories) => expenseCategories.expenseSubCategories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_category_id", referencedColumnName: "id" }])
  expenseCategory: ExpenseCategories;
}
