import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("cache_key_unique", ["key"], { unique: true })
@Entity("cache")
export class Cache {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;
  @Column("varchar", { name: "key", length: 255 })
  key: string;

  @Column("mediumtext", { name: "value" })
  value: string;

  @Column("int", { name: "expiration" })
  expiration: number;
}
