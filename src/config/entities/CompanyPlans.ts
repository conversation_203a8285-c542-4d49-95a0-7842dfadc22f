import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { CompanyCustomPlanRequests } from "./CompanyCustomPlanRequests";
import { CompanyPlanUpgradeRequests } from "./CompanyPlanUpgradeRequests";
import { CompanyUniquePlans } from "./CompanyUniquePlans";
import { FeaturesPlans } from "./FeaturesPlans";
import { PackagesPlans } from "./PackagesPlans";

@Entity("company_plans")
export class CompanyPlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("enum", { name: "plan_type", enum: ["credit", "debit"] })
  planType: "credit" | "debit";

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "monthly_fee", nullable: true, length: 255 })
  monthlyFee: string | null;

  @Column("varchar", { name: "quarterly_fee", nullable: true, length: 255 })
  quarterlyFee: string | null;

  @Column("varchar", { name: "biannually_fee", nullable: true, length: 255 })
  biannuallyFee: string | null;

  @Column("varchar", { name: "yearly_fee", length: 255 })
  yearlyFee: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyCustomPlanRequests,
    (companyCustomPlanRequests) => companyCustomPlanRequests.companyPlan
  )
  companyCustomPlanRequests: CompanyCustomPlanRequests[];

  @OneToMany(
    () => CompanyPlanUpgradeRequests,
    (companyPlanUpgradeRequests) => companyPlanUpgradeRequests.companyPlan
  )
  companyPlanUpgradeRequests: CompanyPlanUpgradeRequests[];

  @OneToMany(
    () => CompanyUniquePlans,
    (companyUniquePlans) => companyUniquePlans.companyPlan
  )
  companyUniquePlans: CompanyUniquePlans[];

  @OneToMany(() => FeaturesPlans, (featuresPlans) => featuresPlans.plan)
  featuresPlans: FeaturesPlans[];

  @OneToMany(() => PackagesPlans, (packagesPlans) => packagesPlans.plan)
  packagesPlans: PackagesPlans[];
}
