import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("virtual_card_account_holders_account_id_index", ["accountId"], {})
@Index("virtual_card_account_holders_user_id_index", ["userId"], {})
@Entity("virtual_card_account_holders")
export class VirtualCardAccountHolders {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("varchar", { name: "account_id", nullable: true, length: 50 })
  accountId: string | null;

  @Column("varchar", {
    name: "provider",
    nullable: true,
    length: 50,
    default: () => "'mono'",
  })
  provider: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
