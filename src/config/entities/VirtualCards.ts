import { Column, Entity, Index } from "typeorm";

@Index("funding_day", ["fundingDay"], {})
@Index("funding_frequency", ["fundingFrequency"], {})
@Index("virtual_cards_auto_fund_index", ["autoFund"], {})
@Index("virtual_cards_card_id_index", ["cardId"], {})
@Index("virtual_cards_provider_index", ["provider"], {})
@Index("virtual_cards_status_index", ["status"], {})
@Index(
  "virtual_cards_user_id_card_id_provider_status_index",
  ["userId", "provider", "cardId", "status"],
  {}
)
@Index("virtual_cards_user_id_index", ["userId"], {})
@Entity("virtual_cards")
export class VirtualCards {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "provider", nullable: true, length: 50 })
  provider: string | null;

  @Column("varchar", { name: "card_id", nullable: true, length: 50 })
  cardId: string | null;

  @Column("decimal", {
    name: "balance",
    precision: 12,
    scale: 2,
    default: () => "'0.00'",
  })
  balance: string;

  @Column("varchar", { name: "currency", nullable: true, length: 10 })
  currency: string | null;

  @Column("varchar", {
    name: "status",
    length: 40,
    default: () => "'inactive'",
  })
  status: string;

  @Column("tinyint", {
    name: "auto_fund",
    unsigned: true,
    default: () => "'0'",
  })
  autoFund: number;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("varchar", { name: "funding_day", nullable: true, length: 10 })
  fundingDay: string | null;

  @Column("varchar", { name: "funding_frequency", nullable: true, length: 30 })
  fundingFrequency: string | null;
}
