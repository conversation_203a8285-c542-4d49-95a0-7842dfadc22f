import {
  Column,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_requests_company_email_unique", ["companyEmail"], {
  unique: true,
})
@Index("company_requests_company_name_unique", ["companyName"], {
  unique: true,
})
@Index("company_requests_employee_email_unique", ["employeeEmail"], {
  unique: true,
})
@Index("company_requests_employee_phone_unique", ["employeePhone"], {
  unique: true,
})
@Entity("company_requests")
export class CompanyRequests {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "company_name", unique: true, length: 255 })
  companyName: string;

  @Column("varchar", { name: "company_email", unique: true, length: 255 })
  companyEmail: string;

  @Column("varchar", { name: "company_address", length: 255 })
  companyAddress: string;

  @Column("varchar", { name: "company_industry", length: 255 })
  companyIndustry: string;

  @Column("varchar", { name: "company_website", nullable: true, length: 255 })
  companyWebsite: string | null;

  @Column("varchar", { name: "company_description", length: 255 })
  companyDescription: string;

  @Column("varchar", { name: "company_size", length: 255 })
  companySize: string;

  @Column("varchar", { name: "employee_first_name", length: 255 })
  employeeFirstName: string;

  @Column("varchar", { name: "employee_email", unique: true, length: 255 })
  employeeEmail: string;

  @Column("varchar", { name: "employee_phone", unique: true, length: 255 })
  employeePhone: string;

  @Column("varchar", { name: "employee_designation", length: 255 })
  employeeDesignation: string;

  @Column("varchar", { name: "email_domain", length: 255 })
  emailDomain: string;

  @Column("varchar", { name: "agent_code", nullable: true, length: 255 })
  agentCode: string | null;

  @Column("enum", { name: "status", enum: ["pending", "approved"] })
  status: "pending" | "approved";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "employee_last_name", length: 255 })
  employeeLastName: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(() => Companies, (companies) => companies.companyRequest)
  companies: Companies[];
}
