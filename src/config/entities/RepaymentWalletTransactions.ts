import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { RepaymentWalletHistories } from "./RepaymentWalletHistories";
import { Loans } from "./Loans";
import { Repayments } from "./Repayments";
import { Users } from "./Users";

@Index("repayment_wallet_transactions_loan_id_foreign", ["loanId"], {})
@Index(
  "repayment_wallet_transactions_repayment_id_foreign",
  ["repaymentId"],
  {}
)
@Index("repayment_wallet_transactions_user_id_foreign", ["userId"], {})
@Entity("repayment_wallet_transactions")
export class RepaymentWalletTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "repayment_id", unsigned: true })
  repaymentId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("decimal", { name: "total_paid", precision: 15, scale: 2 })
  totalPaid: string;

  @Column("decimal", { name: "repayment_amount", precision: 15, scale: 2 })
  repaymentAmount: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => RepaymentWalletHistories,
    (repaymentWalletHistories) => repaymentWalletHistories.repaymentTransaction
  )
  repaymentWalletHistories: RepaymentWalletHistories[];

  @ManyToOne(() => Loans, (loans) => loans.repaymentWalletTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(
    () => Repayments,
    (repayments) => repayments.repaymentWalletTransactions,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: Repayments;

  @ManyToOne(() => Users, (users) => users.repaymentWalletTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
