import { Column, Entity, Index, ManyToMany } from "typeorm";
import { Merchants } from "./Merchants";

@Index("vendor_accesses_slug_unique", ["slug"], { unique: true })
@Entity("vendor_accesses")
export class VendorAccesses {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "slug", unique: true, length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToMany(() => Merchants, (merchants) => merchants.vendorAccesses)
  merchants: Merchants[];
}
