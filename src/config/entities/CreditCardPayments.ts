import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { Users } from "./Users";

@Index("credit_card_payments_statement_id_foreign", ["statementId"], {})
@Index("credit_card_payments_user_id_foreign", ["userId"], {})
@Entity("credit_card_payments")
export class CreditCardPayments {
  @PrimaryGeneratedColumn({ type: "int", name: "id", unsigned: true })
  id: number;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "statement_id", unsigned: true })
  statementId: string;

  @Column("bigint", {
    name: "inherited_payment_id",
    nullable: true,
    unsigned: true,
  })
  inheritedPaymentId: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'success'",
  })
  status: string;

  @Column("decimal", {
    name: "opening_amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  openingAmount: string;

  @Column("decimal", {
    name: "amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: string;

  @Column("decimal", {
    name: "redeemed_amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  redeemedAmount: string;

  @Column("decimal", {
    name: "total_amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  totalAmount: string;

  @Column("decimal", {
    name: "percentage",
    precision: 15,
    scale: 4,
    default: () => "'0.0000'",
  })
  percentage: string;

  @Column("decimal", {
    name: "total_outstanding",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  totalOutstanding: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) => personalAccountStatements.creditCardPayments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.creditCardPayments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
