import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Merchants } from "./Merchants";
import { Users } from "./Users";

@Index("merchant_anonymous_users_company_name_index", ["companyName"], {})
@Index("merchant_anonymous_users_email_index", ["email"], {})
@Index("merchant_anonymous_users_first_name_index", ["firstName"], {})
@Index("merchant_anonymous_users_last_name_index", ["lastName"], {})
@Index("merchant_anonymous_users_merchant_id_foreign", ["merchantId"], {})
@Index("merchant_anonymous_users_phone_index", ["phone"], {})
@Index("merchant_anonymous_users_state_index", ["state"], {})
@Index("merchant_anonymous_users_user_id_foreign", ["userId"], {})
@Entity("merchant_anonymous_users")
export class MerchantAnonymousUsers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("char", { name: "merchant_id", length: 36 })
  merchantId: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("varchar", { name: "source", nullable: true, length: 255 })
  source: string | null;

  @Column("varchar", { name: "order_description", nullable: true, length: 255 })
  orderDescription: string | null;

  @Column("double", {
    name: "order_amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  orderAmount: number | null;

  @Column("varchar", { name: "first_name", length: 255 })
  firstName: string;

  @Column("varchar", { name: "last_name", length: 255 })
  lastName: string;

  @Column("varchar", { name: "email", length: 255 })
  email: string;

  @Column("varchar", { name: "phone", nullable: true, length: 255 })
  phone: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 255 })
  address: string | null;

  @Column("varchar", { name: "lga", nullable: true, length: 255 })
  lga: string | null;

  @Column("varchar", { name: "state", nullable: true, length: 255 })
  state: string | null;

  @Column("varchar", { name: "country", nullable: true, length: 255 })
  country: string | null;

  @Column("varchar", { name: "company_name", nullable: true, length: 255 })
  companyName: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantAnonymousUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @ManyToOne(() => Users, (users) => users.merchantAnonymousUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
