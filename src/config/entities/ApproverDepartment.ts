import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { Groups } from "./Groups";

@Index("approver_department_approver_id_foreign", ["approverId"], {})
@Index("approver_department_department_id_foreign", ["departmentId"], {})
@Entity("approver_department")
export class ApproverDepartment {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "department_id", unsigned: true })
  departmentId: string;

  @Column("bigint", { name: "approver_id", unsigned: true })
  approverId: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.approverDepartments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "approver_id", referencedColumnName: "id" }])
  approver: Users;

  @ManyToOne(() => Groups, (groups) => groups.approverDepartments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "department_id", referencedColumnName: "id" }])
  department: Groups;
}
