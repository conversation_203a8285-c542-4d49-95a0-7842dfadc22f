import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Branches } from "./Branches";
import { CardUsages } from "./CardUsages";
import { CashbackSettings } from "./CashbackSettings";
import { Cashbacks } from "./Cashbacks";
import { EquityContributions } from "./EquityContributions";
import { MerchantAccountInformation } from "./MerchantAccountInformation";
import { MerchantAnonymousUsers } from "./MerchantAnonymousUsers";
import { MerchantBusinessInformation } from "./MerchantBusinessInformation";
import { MerchantClosedModals } from "./MerchantClosedModals";
import { MerchantConfigurations } from "./MerchantConfigurations";
import { MerchantDescriptions } from "./MerchantDescriptions";
import { MerchantOrders } from "./MerchantOrders";
import { MerchantSettings } from "./MerchantSettings";
import { MerchantUser } from "./MerchantUser";
import { VendorAccesses } from "./VendorAccesses";
import { Industries } from "./Industries";
import { Regions } from "./Regions";
import { Users } from "./Users";

@Index("merchants_business_name_index", ["businessName"], {})
@Index("merchants_email_index", ["email"], {})
@Index("merchants_featured_index", ["featured"], {})
@Index("merchants_industry_id_foreign", ["industryId"], {})
@Index("merchants_merchant_code_index", ["merchantCode"], {})
@Index("merchants_merchant_id_foreign", ["merchantId"], {})
@Index("merchants_source_index", ["source"], {})
@Index("merchants_status_index", ["status"], {})
@Index("merchants_status_slug_email_index", ["slug", "email", "status"], {})
@Entity("merchants")
export class Merchants {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "business_name", nullable: true, length: 255 })
  businessName: string | null;

  @Column("varchar", { name: "slug", nullable: true, length: 255 })
  slug: string | null;

  @Column("varchar", { name: "first_name", nullable: true, length: 255 })
  firstName: string | null;

  @Column("varchar", { name: "last_name", nullable: true, length: 255 })
  lastName: string | null;

  @Column("varchar", { name: "email", length: 255 })
  email: string;

  @Column("varchar", { name: "phone", length: 255 })
  phone: string;

  @Column("varchar", { name: "password", length: 255 })
  password: string;

  @Column("varchar", { name: "merchant_code", nullable: true, length: 255 })
  merchantCode: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 255 })
  address: string | null;

  @Column("varchar", { name: "website", nullable: true, length: 255 })
  website: string | null;

  @Column("varchar", { name: "source", nullable: true, length: 255 })
  source: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "approved_at", nullable: true })
  approvedAt: Date | null;

  @Column("timestamp", { name: "rejected_at", nullable: true })
  rejectedAt: Date | null;

  @Column("timestamp", { name: "suspended_at", nullable: true })
  suspendedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("char", { name: "industry_id", nullable: true, length: 36 })
  industryId: string | null;

  @Column("timestamp", { name: "change_password_at", nullable: true })
  changePasswordAt: Date | null;

  @Column("varchar", { name: "image", nullable: true, length: 255 })
  image: string | null;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("tinyint", {
    name: "featured",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  featured: boolean | null;

  @OneToMany(() => Branches, (branches) => branches.merchant)
  branches: Branches[];

  @OneToMany(() => CardUsages, (cardUsages) => cardUsages.merchant)
  cardUsages: CardUsages[];

  @OneToMany(
    () => CashbackSettings,
    (cashbackSettings) => cashbackSettings.merchant
  )
  cashbackSettings: CashbackSettings[];

  @OneToMany(() => Cashbacks, (cashbacks) => cashbacks.merchant)
  cashbacks: Cashbacks[];

  @OneToMany(
    () => EquityContributions,
    (equityContributions) => equityContributions.merchant
  )
  equityContributions: EquityContributions[];

  @OneToMany(
    () => MerchantAccountInformation,
    (merchantAccountInformation) => merchantAccountInformation.merchant
  )
  merchantAccountInformations: MerchantAccountInformation[];

  @OneToMany(
    () => MerchantAnonymousUsers,
    (merchantAnonymousUsers) => merchantAnonymousUsers.merchant
  )
  merchantAnonymousUsers: MerchantAnonymousUsers[];

  @OneToMany(
    () => MerchantBusinessInformation,
    (merchantBusinessInformation) => merchantBusinessInformation.merchant
  )
  merchantBusinessInformations: MerchantBusinessInformation[];

  @OneToMany(
    () => MerchantClosedModals,
    (merchantClosedModals) => merchantClosedModals.merchant
  )
  merchantClosedModals: MerchantClosedModals[];

  @OneToMany(
    () => MerchantConfigurations,
    (merchantConfigurations) => merchantConfigurations.merchant
  )
  merchantConfigurations: MerchantConfigurations[];

  @OneToMany(
    () => MerchantDescriptions,
    (merchantDescriptions) => merchantDescriptions.merchant
  )
  merchantDescriptions: MerchantDescriptions[];

  @OneToMany(() => MerchantOrders, (merchantOrders) => merchantOrders.merchant)
  merchantOrders: MerchantOrders[];

  @OneToMany(
    () => MerchantSettings,
    (merchantSettings) => merchantSettings.merchant
  )
  merchantSettings: MerchantSettings[];

  @OneToMany(() => MerchantUser, (merchantUser) => merchantUser.merchant)
  merchantUsers: MerchantUser[];

  @ManyToMany(
    () => VendorAccesses,
    (vendorAccesses) => vendorAccesses.merchants
  )
  @JoinTable({
    name: "merchant_vendor_access",
    joinColumns: [{ name: "merchant_id", referencedColumnName: "id" }],
    inverseJoinColumns: [
      { name: "vendor_access_id", referencedColumnName: "id" },
    ],
    schema: "uat_live",
  })
  vendorAccesses: VendorAccesses[];

  @ManyToOne(() => Industries, (industries) => industries.merchants, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "industry_id", referencedColumnName: "id" }])
  industry: Industries;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchants, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @OneToMany(() => Merchants, (merchants) => merchants.merchant)
  merchants: Merchants[];

  @OneToMany(() => Regions, (regions) => regions.merchant)
  regions: Regions[];

  @OneToMany(() => Users, (users) => users.merchant)
  users: Users[];
}
