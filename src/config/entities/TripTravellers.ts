import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Trips } from "./Trips";

@Index("trip_travellers_first_name_index", ["firstName"], {})
@Index("trip_travellers_last_name_index", ["lastName"], {})
@Index("trip_travellers_middle_name_index", ["middleName"], {})
@Index("trip_travellers_trip_id_foreign", ["tripId"], {})
@Entity("trip_travellers")
export class TripTravellers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "trip_id", unsigned: true })
  tripId: string;

  @Column("varchar", {
    name: "passenger_type_code",
    nullable: true,
    length: 255,
  })
  passengerTypeCode: string | null;

  @Column("varchar", { name: "first_name", nullable: true, length: 255 })
  firstName: string | null;

  @Column("varchar", { name: "last_name", nullable: true, length: 255 })
  lastName: string | null;

  @Column("varchar", { name: "middle_name", nullable: true, length: 255 })
  middleName: string | null;

  @Column("varchar", { name: "dob", nullable: true, length: 255 })
  dob: string | null;

  @Column("varchar", { name: "title", nullable: true, length: 255 })
  title: string | null;

  @Column("varchar", { name: "gender", nullable: true, length: 255 })
  gender: string | null;

  @Column("json", { name: "address", nullable: true })
  address: object | null;

  @Column("json", { name: "documents", nullable: true })
  documents: object | null;

  @Column("varchar", { name: "e_ticket_number", nullable: true, length: 255 })
  eTicketNumber: string | null;

  @Column("varchar", {
    name: "traveller_reference_id",
    nullable: true,
    length: 255,
  })
  travellerReferenceId: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Trips, (trips) => trips.tripTravellers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "trip_id", referencedColumnName: "id" }])
  trip: Trips;
}
