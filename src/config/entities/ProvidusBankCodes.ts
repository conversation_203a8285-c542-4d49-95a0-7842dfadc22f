import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("providus_bank_codes")
export class ProvidusBankCodes {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "bank_name", length: 255 })
  bankName: string;

  @Column("varchar", { name: "cbn_code", nullable: true, length: 255 })
  cbnCode: string | null;

  @Column("varchar", { name: "providus_code", nullable: true, length: 255 })
  providusCode: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
