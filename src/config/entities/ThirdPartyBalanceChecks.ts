import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("third_party_balance_checks")
export class ThirdPartyBalanceChecks {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "service", length: 255 })
  service: string;

  @Column("bigint", { name: "last_check_balance" })
  lastCheckBalance: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
