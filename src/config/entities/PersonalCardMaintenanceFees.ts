import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { PersonalClearedStatements } from "./PersonalClearedStatements";

@Index("personal_card_maintenance_fees_account_id_foreign", ["accountId"], {})
@Entity("personal_card_maintenance_fees")
export class PersonalCardMaintenanceFees {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", {
    name: "condition",
    enum: ["paid", "part-paid", "unpaid"],
    default: () => "'unpaid'",
  })
  condition: "paid" | "part-paid" | "unpaid";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @OneToMany(
    () => PersonalAccountStatements,
    (personalAccountStatements) => personalAccountStatements.maintenanceFee
  )
  personalAccountStatements: PersonalAccountStatements[];

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalCardMaintenanceFees,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.maintenanceFee
  )
  personalClearedStatements: PersonalClearedStatements[];
}
