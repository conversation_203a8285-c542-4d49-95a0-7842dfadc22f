import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Loans } from "./Loans";
import { Users } from "./Users";

@Index("advancly_loans_loan_id_foreign", ["loanId"], {})
@Index("advancly_loans_user_id_foreign", ["userId"], {})
@Entity("advancly_loans")
export class AdvanclyLoans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("varchar", { name: "aggregator_loan_ref", length: 255 })
  aggregatorLoanRef: string;

  @Column("varchar", { name: "advancly_loan_ref", length: 255 })
  advanclyLoanRef: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Loans, (loans) => loans.advanclyLoans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(() => Users, (users) => users.advanclyLoans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
