import { Column, Entity, Index, OneToMany } from "typeorm";
import { RewardWalletTransactions } from "./RewardWalletTransactions";

@Index(
  "reward_wallets_reward_walletable_type_reward_walletable_id_index",
  ["rewardWalletableType", "rewardWalletableId"],
  {}
)
@Entity("reward_wallets")
export class RewardWallets {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "reward_walletable_type", length: 255 })
  rewardWalletableType: string;

  @Column("bigint", { name: "reward_walletable_id", unsigned: true })
  rewardWalletableId: string;

  @Column("double", {
    name: "balance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  balance: number | null;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => RewardWalletTransactions,
    (rewardWalletTransactions) => rewardWalletTransactions.rewardWallet
  )
  rewardWalletTransactions: RewardWalletTransactions[];
}
