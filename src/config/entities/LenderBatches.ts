import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Lenders } from "./Lenders";
import { Loans } from "./Loans";

@Index("lender_batches_lender_id_foreign", ["lenderId"], {})
@Entity("lender_batches")
export class LenderBatches {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("bigint", { name: "lender_id", unsigned: true })
  lenderId: string;

  @Column("datetime", { name: "disbursed_at", nullable: true })
  disbursedAt: Date | null;

  @Column("double", {
    name: "interest_rate",
    nullable: true,
    precision: 22,
    default: () => "'0'",
  })
  interestRate: number | null;

  @Column("int", { name: "tenure", nullable: true })
  tenure: number | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Lenders, (lenders) => lenders.lenderBatches, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lender_id", referencedColumnName: "id" }])
  lender: Lenders;

  @OneToMany(() => Loans, (loans) => loans.lenderBatch)
  loans: Loans[];
}
