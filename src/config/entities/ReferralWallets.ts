import { Column, Entity, Index, OneToMany } from "typeorm";
import { ReferralWalletTransactions } from "./ReferralWalletTransactions";

@Index(
  "referral_wallets_referral_wallet_type_referral_wallet_id_index",
  ["referralWalletType", "referralWalletId"],
  {}
)
@Entity("referral_wallets")
export class ReferralWallets {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "referral_wallet_type", length: 255 })
  referralWalletType: string;

  @Column("bigint", { name: "referral_wallet_id", unsigned: true })
  referralWalletId: string;

  @Column("double", {
    name: "balance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  balance: number | null;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => ReferralWalletTransactions,
    (referralWalletTransactions) => referralWalletTransactions.referralWallet
  )
  referralWalletTransactions: ReferralWalletTransactions[];
}
