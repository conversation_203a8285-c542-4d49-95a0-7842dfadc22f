import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { PersonalCredits } from "./PersonalCredits";

@Index(
  "personal_credit_applications_authorizer_id_foreign",
  ["authorizerId"],
  {}
)
@Index("personal_credit_applications_user_id_foreign", ["userId"], {})
@Entity("personal_credit_applications")
export class PersonalCreditApplications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "authorizer_id", nullable: true, unsigned: true })
  authorizerId: string | null;

  @Column("enum", {
    name: "status",
    enum: ["pending", "approved", "declined"],
    default: () => "'pending'",
  })
  status: "pending" | "approved" | "declined";

  @Column("text", { name: "comments", nullable: true })
  comments: string | null;

  @Column("timestamp", { name: "attended_at", nullable: true })
  attendedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.personalCreditApplications, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "authorizer_id", referencedColumnName: "id" }])
  authorizer: Users;

  @ManyToOne(() => Users, (users) => users.personalCreditApplications2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;

  @OneToMany(
    () => PersonalCredits,
    (personalCredits) => personalCredits.personalCreditApplication
  )
  personalCredits: PersonalCredits[];
}
