import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_debit_card_request_company_id_foreign", ["companyId"], {})
@Entity("company_debit_card_request")
export class CompanyDebitCardRequest {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("int", { name: "company_plans_id" })
  companyPlansId: number;

  @Column("enum", {
    name: "status",
    enum: ["pending", "approved", "declined", "ready"],
    default: () => "'pending'",
  })
  status: "pending" | "approved" | "declined" | "ready";

  @Column("varchar", { name: "comments", nullable: true, length: 255 })
  comments: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyDebitCardRequests,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
