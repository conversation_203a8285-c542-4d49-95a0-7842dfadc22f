import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Loans } from "./Loans";
import { Users } from "./Users";

@Index("personal_loan_offer_letters_loan_id_foreign", ["loanId"], {})
@Index("personal_loan_offer_letters_user_id_foreign", ["userId"], {})
@Entity("personal_loan_offer_letters")
export class PersonalLoanOfferLetters {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("longtext", { name: "user_data" })
  userData: string;

  @Column("double", { name: "requested_amount", precision: 22 })
  requestedAmount: number;

  @Column("double", { name: "disbursed_amount", precision: 22 })
  disbursedAmount: number;

  @Column("double", { name: "interest_rate", precision: 22 })
  interestRate: number;

  @Column("int", { name: "tenure" })
  tenure: number;

  @Column("double", { name: "repayment_amount", precision: 22 })
  repaymentAmount: number;

  @Column("double", { name: "first_repayment_amount", precision: 22 })
  firstRepaymentAmount: number;

  @Column("longtext", { name: "repayment_structure" })
  repaymentStructure: string;

  @Column("datetime", { name: "first_repayment_date" })
  firstRepaymentDate: Date;

  @Column("datetime", { name: "last_repayment_date" })
  lastRepaymentDate: Date;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("datetime", { name: "accepted_at", nullable: true })
  acceptedAt: Date | null;

  @Column("datetime", { name: "rejected_at", nullable: true })
  rejectedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Loans, (loans) => loans.personalLoanOfferLetters, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(() => Users, (users) => users.personalLoanOfferLetters, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
