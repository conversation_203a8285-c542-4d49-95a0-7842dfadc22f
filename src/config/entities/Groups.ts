import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ApproverDepartment } from "./ApproverDepartment";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyCards } from "./CompanyCards";
import { ExpenseRequests } from "./ExpenseRequests";
import { Companies } from "./Companies";
import { Units } from "./Units";
import { Users } from "./Users";

@Index("groups_company_id_foreign", ["companyId"], {})
@Entity("groups")
export class Groups {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "creator_id", nullable: true, unsigned: true })
  creatorId: string | null;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "slug", nullable: true, length: 255 })
  slug: string | null;

  @Column("varchar", { name: "description", length: 255 })
  description: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => ApproverDepartment,
    (approverDepartment) => approverDepartment.department
  )
  approverDepartments: ApproverDepartment[];

  @OneToMany(() => CompanyAccounts, (companyAccounts) => companyAccounts.group)
  companyAccounts: CompanyAccounts[];

  @OneToMany(() => CompanyCards, (companyCards) => companyCards.group)
  companyCards: CompanyCards[];

  @OneToMany(() => ExpenseRequests, (expenseRequests) => expenseRequests.group)
  expenseRequests: ExpenseRequests[];

  @ManyToOne(() => Companies, (companies) => companies.groups, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @OneToMany(() => Units, (units) => units.group)
  units: Units[];

  @OneToMany(() => Users, (users) => users.group)
  users: Users[];
}
