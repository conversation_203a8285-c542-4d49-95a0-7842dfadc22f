import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Merchants } from "./Merchants";

@Index("merchant_account_information_account_no_unique", ["accountNo"], {
  unique: true,
})
@Index("merchant_account_information_bvn_unique", ["bvn"], { unique: true })
@Index("merchant_account_information_merchant_id_foreign", ["merchantId"], {})
@Entity("merchant_account_information")
export class MerchantAccountInformation {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", length: 36 })
  merchantId: string;

  @Column("varchar", {
    name: "account_no",
    nullable: true,
    unique: true,
    length: 15,
  })
  accountNo: string | null;

  @Column("varchar", { name: "bank_name", nullable: true, length: 255 })
  bankName: string | null;

  @Column("varchar", { name: "account_name", nullable: true, length: 255 })
  accountName: string | null;

  @Column("varchar", { name: "bvn", nullable: true, unique: true, length: 15 })
  bvn: string | null;

  @Column("text", { name: "bvn_data", nullable: true })
  bvnData: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => Merchants,
    (merchants) => merchants.merchantAccountInformations,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
