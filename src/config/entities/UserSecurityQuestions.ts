import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { SecurityQuestions } from "./SecurityQuestions";
import { Users } from "./Users";

@Index(
  "user_security_questions_security_question_id_foreign",
  ["securityQuestionId"],
  {}
)
@Index("user_security_questions_user_id_foreign", ["userId"], {})
@Entity("user_security_questions")
export class UserSecurityQuestions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "security_question_id", unsigned: true })
  securityQuestionId: string;

  @Column("varchar", { name: "answer", nullable: true, length: 255 })
  answer: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => SecurityQuestions,
    (securityQuestions) => securityQuestions.userSecurityQuestions,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "security_question_id", referencedColumnName: "id" }])
  securityQuestion: SecurityQuestions;

  @ManyToOne(() => Users, (users) => users.userSecurityQuestions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
