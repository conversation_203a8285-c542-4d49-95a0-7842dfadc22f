import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyCards } from "./CompanyCards";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";
import { CompanyExpenseSubCategories } from "./CompanyExpenseSubCategories";

@Index(
  "company_credit_transactions_company_account_id_foreign",
  ["companyAccountId"],
  {}
)
@Index(
  "company_credit_transactions_company_card_id_foreign",
  ["companyCardId"],
  {}
)
@Index("company_credit_transactions_company_id_foreign", ["companyId"], {})
@Index(
  "company_credit_transactions_company_statement_id_foreign",
  ["companyStatementId"],
  {}
)
@Index(
  "company_credit_transactions_expense_sub_category_id_foreign",
  ["expenseSubCategoryId"],
  {}
)
@Entity("company_credit_transactions")
export class CompanyCreditTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", {
    name: "company_account_id",
    nullable: true,
    unsigned: true,
  })
  companyAccountId: string | null;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", { name: "company_card_id", nullable: true, unsigned: true })
  companyCardId: string | null;

  @Column("bigint", {
    name: "company_statement_id",
    nullable: true,
    unsigned: true,
  })
  companyStatementId: string | null;

  @Column("bigint", {
    name: "expense_sub_category_id",
    nullable: true,
    unsigned: true,
  })
  expenseSubCategoryId: string | null;

  @Column("enum", {
    name: "transaction_type",
    enum: ["account-create", "top-up", "withdrawal", "new-limit"],
  })
  transactionType: "account-create" | "top-up" | "withdrawal" | "new-limit";

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", { name: "amount_flag", enum: ["positive", "negative"] })
  amountFlag: "positive" | "negative";

  @Column("enum", {
    name: "condition",
    enum: ["paid", "part-paid", "unpaid", "not-applicable"],
    default: () => "'not-applicable'",
  })
  condition: "paid" | "part-paid" | "unpaid" | "not-applicable";

  @Column("enum", {
    name: "usage",
    nullable: true,
    enum: ["personal", "corporate"],
  })
  usage: "personal" | "corporate" | null;

  @Column("varchar", { name: "receipt", nullable: true, length: 255 })
  receipt: string | null;

  @Column("enum", {
    name: "upload_status",
    enum: ["empty", "pending", "completed", "approve", "decline"],
    default: () => "'empty'",
  })
  uploadStatus: "empty" | "pending" | "completed" | "approve" | "decline";

  @Column("text", { name: "description", nullable: true })
  description: string | null;

  @Column("datetime", { name: "transaction_date", nullable: true })
  transactionDate: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @Column("varchar", { name: "category", nullable: true, length: 255 })
  category: string | null;

  @Column("varchar", {
    name: "currency",
    nullable: true,
    length: 255,
    default: () => "'ngn'",
  })
  currency: string | null;

  @Column("enum", {
    name: "status",
    nullable: true,
    enum: ["pending", "success"],
    default: () => "'success'",
  })
  status: "pending" | "success" | null;

  @ManyToOne(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.companyCreditTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_account_id", referencedColumnName: "id" }])
  companyAccount: CompanyAccounts;

  @ManyToOne(
    () => CompanyCards,
    (companyCards) => companyCards.companyCreditTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_card_id", referencedColumnName: "id" }])
  companyCard: CompanyCards;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyCreditTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyCreditTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_statement_id", referencedColumnName: "id" }])
  companyStatement: CompanyStatements;

  @ManyToOne(
    () => CompanyExpenseSubCategories,
    (companyExpenseSubCategories) =>
      companyExpenseSubCategories.companyCreditTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_sub_category_id", referencedColumnName: "id" }])
  expenseSubCategory: CompanyExpenseSubCategories;
}
