import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("password_resets_email_index", ["email"], {})
@Index("password_resets_token_unique", ["token"], { unique: true })
@Entity("password_resets")
export class PasswordResets {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;
  @Column("varchar", { name: "email", length: 255 })
  email: string;

  @Column("varchar", { name: "token", length: 255 })
  token: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;
}
