import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Users } from "./Users";

@Index(
  "cash_wallet_transfer_awaiting_approvals_admin_id_foreign",
  ["adminId"],
  {}
)
@Index(
  "cash_wallet_transfer_awaiting_approvals_user_id_foreign",
  ["userId"],
  {}
)
@Entity("cash_wallet_transfer_awaiting_approvals")
export class CashWalletTransferAwaitingApprovals {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "admin_id", nullable: true, unsigned: true })
  adminId: string | null;

  @Column("varchar", { name: "account_name", length: 255 })
  accountName: string;

  @Column("varchar", { name: "account_no", length: 255 })
  accountNo: string;

  @Column("varchar", { name: "bank_name", length: 255 })
  bankName: string;

  @Column("int", { name: "amount" })
  amount: number;

  @Column("varchar", { name: "status", nullable: true, length: 255 })
  status: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => Users,
    (users) => users.cashWalletTransferAwaitingApprovals,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "admin_id", referencedColumnName: "id" }])
  admin: Users;

  @ManyToOne(
    () => Users,
    (users) => users.cashWalletTransferAwaitingApprovals2,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
