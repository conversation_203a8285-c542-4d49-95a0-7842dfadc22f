import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { CardUsages } from "./CardUsages";
import { Merchants } from "./Merchants";
import { MerchantOrders } from "./MerchantOrders";
import { PointTransactions } from "./PointTransactions";
import { RewardWalletTransactions } from "./RewardWalletTransactions";

@Index("cashbacks_card_usage_id_foreign", ["cardUsageId"], {})
@Index(
  "cashbacks_cashbackable_type_cashbackable_id_index",
  ["cashbackableType", "cashbackableId"],
  {}
)
@Index("cashbacks_merchant_id_foreign", ["merchantId"], {})
@Index("cashbacks_merchant_order_id_foreign", ["merchantOrderId"], {})
@Index("cashbacks_reference_unique", ["reference"], { unique: true })
@Index("cashbacks_source_index", ["source"], {})
@Index("cashbacks_status_index", ["status"], {})
@Index("cashbacks_type_index", ["type"], {})
@Entity("cashbacks")
export class Cashbacks {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "cashbackable_type", length: 255 })
  cashbackableType: string;

  @Column("bigint", { name: "cashbackable_id", unsigned: true })
  cashbackableId: string;

  @Column("varchar", { name: "source", nullable: true, length: 255 })
  source: string | null;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("char", { name: "card_usage_id", nullable: true, length: 36 })
  cardUsageId: string | null;

  @Column("double", {
    name: "commission",
    nullable: true,
    comment:
      "Number of cashback in flat or percentage the merchant is giving out",
    precision: 30,
    scale: 2,
  })
  commission: number | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("double", {
    name: "cashback",
    nullable: true,
    comment: "Total cashback on this transaction in kobo",
    precision: 30,
    scale: 2,
  })
  cashback: number | null;

  @Column("double", {
    name: "sharing_formula",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  sharingFormula: number | null;

  @Column("double", {
    name: "amount",
    nullable: true,
    comment: "Total amount spent on this transaction in kobo",
    precision: 30,
    scale: 2,
  })
  amount: number | null;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("char", { name: "merchant_order_id", nullable: true, length: 36 })
  merchantOrderId: string | null;

  @ManyToOne(() => CardUsages, (cardUsages) => cardUsages.cashbacks, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "card_usage_id", referencedColumnName: "id" }])
  cardUsage: CardUsages;

  @ManyToOne(() => Merchants, (merchants) => merchants.cashbacks, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @ManyToOne(
    () => MerchantOrders,
    (merchantOrders) => merchantOrders.cashbacks,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "merchant_order_id", referencedColumnName: "id" }])
  merchantOrder: MerchantOrders;

  @OneToMany(
    () => PointTransactions,
    (pointTransactions) => pointTransactions.cashback
  )
  pointTransactions: PointTransactions[];

  @OneToMany(
    () => RewardWalletTransactions,
    (rewardWalletTransactions) => rewardWalletTransactions.cashback
  )
  rewardWalletTransactions: RewardWalletTransactions[];
}
