import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { ReferralWallets } from "./ReferralWallets";

@Index("referral_wallet_transactions_created_at_index", ["createdAt"], {})
@Index("referral_wallet_transactions_redeemed_at_index", ["redeemedAt"], {})
@Index(
  "referral_wallet_transactions_redeemed_batch_index",
  ["redeemedBatch"],
  {}
)
@Index("referral_wallet_transactions_reference_unique", ["reference"], {
  unique: true,
})
@Index(
  "referral_wallet_transactions_referral_wallet_id_foreign",
  ["referralWalletId"],
  {}
)
@Index("referral_wallet_transactions_status_index", ["status"], {})
@Index("referral_wallet_transactions_type_index", ["type"], {})
@Entity("referral_wallet_transactions")
export class ReferralWalletTransactions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "referral_wallet_id", nullable: true, length: 36 })
  referralWalletId: string | null;

  @Column("double", { name: "amount", nullable: true, precision: 30, scale: 2 })
  amount: number | null;

  @Column("double", {
    name: "prevBalance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  prevBalance: number | null;

  @Column("double", {
    name: "currentBalance",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  currentBalance: number | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("datetime", { name: "transaction_date", nullable: true })
  transactionDate: Date | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "redeemed_batch", nullable: true, length: 255 })
  redeemedBatch: string | null;

  @Column("datetime", { name: "redeemed_at", nullable: true })
  redeemedAt: Date | null;

  @ManyToOne(
    () => ReferralWallets,
    (referralWallets) => referralWallets.referralWalletTransactions,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "referral_wallet_id", referencedColumnName: "id" }])
  referralWallet: ReferralWallets;
}
