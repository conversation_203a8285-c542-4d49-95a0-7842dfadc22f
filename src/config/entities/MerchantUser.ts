import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Merchants } from "./Merchants";
import { Users } from "./Users";

@Index("merchant_user_merchant_id_foreign", ["merchantId"], {})
@Index("merchant_user_user_id_foreign", ["userId"], {})
@Entity("merchant_user")
export class MerchantUser {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("char", { name: "merchant_id", length: 36 })
  merchantId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "user_agent", nullable: true, length: 255 })
  userAgent: string | null;

  @Column("varchar", { name: "payment_method", nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column("varchar", { name: "order_description", nullable: true, length: 255 })
  orderDescription: string | null;

  @Column("double", {
    name: "order_amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  orderAmount: number | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", {
    name: "status",
    nullable: true,
    comment: "Should be in completed, incomplete and null",
    length: 255,
  })
  status: string | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @ManyToOne(() => Users, (users) => users.merchantUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
