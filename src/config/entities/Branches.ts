import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Merchants } from "./Merchants";
import { Regions } from "./Regions";
import { EquityContributions } from "./EquityContributions";
import { MerchantOrders } from "./MerchantOrders";

@Index("branches_merchant_id_foreign", ["merchantId"], {})
@Index("branches_region_id_foreign", ["regionId"], {})
@Entity("branches")
export class Branches {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("char", { name: "region_id", nullable: true, length: 36 })
  regionId: string | null;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "phone", nullable: true, length: 255 })
  phone: string | null;

  @Column("varchar", { name: "account_no", nullable: true, length: 255 })
  accountNo: string | null;

  @Column("varchar", { name: "bank_name", nullable: true, length: 255 })
  bankName: string | null;

  @Column("tinyint", { name: "show_account", width: 1, default: () => "'1'" })
  showAccount: boolean;

  @Column("varchar", { name: "address", nullable: true, length: 255 })
  address: string | null;

  @Column("varchar", { name: "lga", nullable: true, length: 255 })
  lga: string | null;

  @Column("varchar", { name: "state", nullable: true, length: 255 })
  state: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "url", nullable: true, length: 255 })
  url: string | null;

  @Column("varchar", { name: "short_url", nullable: true, length: 255 })
  shortUrl: string | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.branches, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @ManyToOne(() => Regions, (regions) => regions.branches, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "region_id", referencedColumnName: "id" }])
  region: Regions;

  @OneToMany(
    () => EquityContributions,
    (equityContributions) => equityContributions.branch
  )
  equityContributions: EquityContributions[];

  @OneToMany(() => MerchantOrders, (merchantOrders) => merchantOrders.branch)
  merchantOrders: MerchantOrders[];
}
