import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("loanbot_transactions")
export class LoanbotTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "transaction_reference", length: 255 })
  transactionReference: string;

  @Column("varchar", { name: "description", length: 255 })
  description: string;

  @Column("int", { name: "amount" })
  amount: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
