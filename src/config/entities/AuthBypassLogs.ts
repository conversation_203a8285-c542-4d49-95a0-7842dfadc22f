import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("auth_bypass_logs")
export class AuthBypassLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("int", { name: "bypasser_id", unsigned: true })
  bypasserId: number;

  @Column("varchar", { name: "bypasser_type", length: 255 })
  bypasserType: string;

  @Column("varchar", { name: "ip", length: 255 })
  ip: string;

  @Column("varchar", { name: "bypassed_email", length: 255 })
  bypassedEmail: string;

  @Column("longtext", { name: "location", nullable: true })
  location: string | null;

  @Column("varchar", { name: "agent", length: 255 })
  agent: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
