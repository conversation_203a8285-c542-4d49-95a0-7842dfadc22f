import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("personal_card_requests_status_index", ["status"], {})
@Index("personal_card_requests_type_index", ["type"], {})
@Entity("personal_card_requests")
export class PersonalCardRequests {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "personal_card_plans_id", unsigned: true })
  personalCardPlansId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "paid", "approved", "declined", "ready"],
    default: () => "'pending'",
  })
  status: "pending" | "paid" | "approved" | "declined" | "ready";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;
}
