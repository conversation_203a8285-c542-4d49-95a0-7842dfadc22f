import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { ExpensePolicyRole } from "./ExpensePolicyRole";
import { ExpensePolicyUser } from "./ExpensePolicyUser";

@Index("expense_policies_company_id_foreign", ["companyId"], {})
@Entity("expense_policies")
export class ExpensePolicies {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "range", length: 255 })
  range: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.expensePolicies, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @OneToMany(
    () => ExpensePolicyRole,
    (expensePolicyRole) => expensePolicyRole.expensePolicy
  )
  expensePolicyRoles: ExpensePolicyRole[];

  @OneToMany(
    () => ExpensePolicyUser,
    (expensePolicyUser) => expensePolicyUser.expensePolicy
  )
  expensePolicyUsers: ExpensePolicyUser[];
}
