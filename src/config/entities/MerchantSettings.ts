import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Merchants } from "./Merchants";

@Index("merchant_settings_merchant_id_foreign", ["merchantId"], {})
@Entity("merchant_settings")
export class MerchantSettings {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("varchar", { name: "merchant_discount", nullable: true, length: 255 })
  merchantDiscount: string | null;

  @Column("varchar", { name: "credpal_discount", nullable: true, length: 255 })
  credpalDiscount: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantSettings, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
