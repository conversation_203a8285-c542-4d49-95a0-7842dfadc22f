import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Cashbacks } from './Cashbacks';
import { Branches } from './Branches';
import { Loans } from './Loans';
import { Merchants } from './Merchants';
import { PersonalCardTransactions } from './PersonalCardTransactions';
import { Users } from './Users';
import { CartItems } from './cartItems';

export enum MerchantOrdersStatus {
  AWAITING_PAYMENT = 'awaiting-payment',
  PENDING = 'pending',
  UNDER_REVIEW = 'under-review',
  APPROVED = 'approved',
  REQUEUE = 'requeue',
  CANCELLED = 'cancelled',
}

@Index('merchant_orders_branch_id_foreign', ['branchId'], {})
@Index('merchant_orders_loan_id_foreign', ['loanId'], {})
@Index('merchant_orders_merchant_id_foreign', ['merchantId'], {})
@Index('merchant_orders_order_no_unique', ['orderNo'], { unique: true })
@Index('merchant_orders_order_status_index', ['orderStatus'], {})
@Index('merchant_orders_status_index', ['status'], {})
@Index('merchant_orders_transaction_id_foreign', ['transactionId'], {})
@Index(
  'merchant_orders_user_id_branch_id_merchant_id_index',
  ['merchantId', 'userId', 'branchId'],
  {},
)
@Entity('merchant_orders')
export class MerchantOrders {
  @Column('char', { primary: true, name: 'id', length: 36 })
  id: string;

  @Column('varchar', { name: 'order_no', unique: true, length: 255 })
  orderNo: string;

  @Column('char', { name: 'merchant_id', nullable: true, length: 36 })
  merchantId: string | null;

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: string;

  @Column('char', { name: 'branch_id', nullable: true, length: 36 })
  branchId: string | null;

  @Column('bigint', { name: 'transaction_id', nullable: true, unsigned: true })
  transactionId: string | null;

  @Column('varchar', { name: 'item', length: 255 })
  item: string;

  @Column('longtext', { name: 'description', nullable: true })
  description: string | null;

  @Column('double', { name: 'amount', precision: 30, scale: 2 })
  amount: number;

  @Column('varchar', { name: 'status', nullable: true, length: 255 })
  status: string | null;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('varchar', { name: 'order_status', nullable: true, length: 255 })
  orderStatus: string | null;

  @Column('timestamp', { name: 'delivered_at', nullable: true })
  deliveredAt: Date | null;

  @Column('timestamp', { name: 'cancelled_at', nullable: true })
  cancelledAt: Date | null;

  @Column('bigint', { name: 'loan_id', nullable: true, unsigned: true })
  loanId: string | null;

  @Column('varchar', { name: 'channel', nullable: true, length: 50 })
  channel: string | null;

  @Column('varchar', { name: 'payment_method', nullable: true, length: 255 })
  paymentMethod: string | null;

  @Column('varchar', {
    name: 'lock_code',
    nullable: true,
    comment: 'For Device Financing',
    length: 255,
  })
  lockCode: string | null;

  @Column('varchar', {
    name: 'source',
    nullable: true,
    length: 255,
    default: () => "'BNPL'",
  })
  source: string | null;

  @Column('varchar', { name: 'payment_receipt', nullable: true, length: 255 })
  paymentReceipt: string | null;

  @OneToMany(() => Cashbacks, (cashbacks) => cashbacks.merchantOrder)
  cashbacks: Cashbacks[];

  @ManyToOne(() => Branches, (branches) => branches.merchantOrders, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'branch_id', referencedColumnName: 'id' }])
  branch: Branches;

  @ManyToOne(() => Loans, (loans) => loans.merchantOrders, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'loan_id', referencedColumnName: 'id' }])
  loan: Loans;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantOrders, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'merchant_id', referencedColumnName: 'id' }])
  merchant: Merchants;

  @ManyToOne(
    () => PersonalCardTransactions,
    (personalCardTransactions) => personalCardTransactions.merchantOrders,
    { onDelete: 'NO ACTION', onUpdate: 'NO ACTION' },
  )
  @JoinColumn([{ name: 'transaction_id', referencedColumnName: 'id' }])
  transaction: PersonalCardTransactions;

  @ManyToOne(() => Users, (users) => users.merchantOrders, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: Users;

  @OneToMany(() => CartItems, (cartItems) => cartItems.merchantOrder)
  cartItems: CartItems[];
}
