import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("express_verifications_status_index", ["status"], {})
@Index("express_verifications_type_index", ["type"], {})
@Index("express_verifications_user_id_foreign", ["userId"], {})
@Index("express_verifications_verify_method_index", ["verifyMethod"], {})
@Entity("express_verifications")
export class ExpressVerifications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("varchar", { name: "status", nullable: true, length: 255 })
  status: string | null;

  @Column("varchar", { name: "verify_method", nullable: true, length: 255 })
  verifyMethod: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("datetime", { name: "reviewed_at", nullable: true })
  reviewedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.expressVerifications, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
