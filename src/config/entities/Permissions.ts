import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinT<PERSON>,
  ManyToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Roles } from "./Roles";
import { Users } from "./Users";

@Entity("permissions")
export class Permissions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToMany(() => Roles, (roles) => roles.permissions)
  @JoinTable({
    name: "roles_permissions",
    joinColumns: [{ name: "permission_id", referencedColumnName: "id" }],
    inverseJoinColumns: [{ name: "role_id", referencedColumnName: "id" }],
    schema: "uat_live",
  })
  roles: Roles[];

  @ManyToMany(() => Users, (users) => users.permissions)
  @JoinTable({
    name: "users_permissions",
    joinColumns: [{ name: "permission_id", referencedColumnName: "id" }],
    inverseJoinColumns: [{ name: "user_id", referencedColumnName: "id" }],
    schema: "uat_live",
  })
  users: Users[];
}
