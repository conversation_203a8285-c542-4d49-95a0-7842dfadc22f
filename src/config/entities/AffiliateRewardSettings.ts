import { Column, <PERSON>tity, OneToMany } from "typeorm";
import { AffiliateCodes } from "./AffiliateCodes";

@Entity("affiliate_reward_settings")
export class AffiliateRewardSettings {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "product", nullable: true, length: 255 })
  product: string | null;

  @Column("varchar", { name: "tier", nullable: true, length: 255 })
  tier: string | null;

  @Column("double", {
    name: "minimum",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  minimum: number | null;

  @Column("double", {
    name: "maximum",
    nullable: true,
    precision: 30,
    scale: 2,
    default: () => "'1600000000000000000000000000.00'",
  })
  maximum: number | null;

  @Column("double", {
    name: "commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  commission: number | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @OneToMany(
    () => AffiliateCodes,
    (affiliateCodes) => affiliateCodes.affiliateRewardSetting
  )
  affiliateCodes: AffiliateCodes[];
}
