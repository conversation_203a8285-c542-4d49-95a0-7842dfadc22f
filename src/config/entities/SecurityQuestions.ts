import { Column, <PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON>any, PrimaryGeneratedColumn } from "typeorm";
import { UserSecurityQuestions } from "./UserSecurityQuestions";

@Entity("security_questions")
export class SecurityQuestions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "slug", nullable: true, length: 255 })
  slug: string | null;

  @Column("varchar", { name: "question", length: 255 })
  question: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(
    () => UserSecurityQuestions,
    (userSecurityQuestions) => userSecurityQuestions.securityQuestion
  )
  userSecurityQuestions: UserSecurityQuestions[];
}
