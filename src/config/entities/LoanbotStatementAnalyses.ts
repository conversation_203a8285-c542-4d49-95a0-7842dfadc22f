import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { UserBankStatements } from "./UserBankStatements";
import { Users } from "./Users";

@Index(
  "loanbot_statement_analyses_user_bank_statement_id_foreign",
  ["userBankStatementId"],
  {}
)
@Index("loanbot_statement_analyses_user_id_foreign", ["userId"], {})
@Entity("loanbot_statement_analyses")
export class LoanbotStatementAnalyses {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "user_bank_statement_id", unsigned: true })
  userBankStatementId: string;

  @Column("tinyint", { name: "valid", width: 1 })
  valid: boolean;

  @Column("varchar", { name: "remark", length: 255 })
  remark: string;

  @Column("double", { name: "total_credit", precision: 22 })
  totalCredit: number;

  @Column("int", { name: "number_of_transaction_days" })
  numberOfTransactionDays: number;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "analysis_id", length: 255 })
  analysisId: string;

  @ManyToOne(
    () => UserBankStatements,
    (userBankStatements) => userBankStatements.loanbotStatementAnalyses,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "user_bank_statement_id", referencedColumnName: "id" }])
  userBankStatement: UserBankStatements;

  @ManyToOne(() => Users, (users) => users.loanbotStatementAnalyses, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
