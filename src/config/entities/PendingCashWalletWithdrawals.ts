import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("pending_cash_wallet_withdrawals_reference_unique", ["reference"], {
  unique: true,
})
@Index("pending_cash_wallet_withdrawals_wallet_id_index", ["walletId"], {})
@Entity("pending_cash_wallet_withdrawals")
export class PendingCashWalletWithdrawals {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "wallet_id", length: 255 })
  walletId: string;

  @Column("double", { name: "amount", precision: 8, scale: 2 })
  amount: number;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("varchar", { name: "purpose", length: 255 })
  purpose: string;

  @Column("json", { name: "metadata" })
  metadata: object;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
