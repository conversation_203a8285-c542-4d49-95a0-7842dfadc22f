import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("investments_invest_investment_id_index", ["investInvestmentId"], {})
@Index("investments_investment_type_index", ["investmentType"], {})
@Index("investments_status_index", ["status"], {})
@Entity("investments")
export class Investments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id" })
  userId: string;

  @Column("varchar", { name: "invest_investment_id", length: 255 })
  investInvestmentId: string;

  @Column("varchar", { name: "investment_type", nullable: true, length: 255 })
  investmentType: string | null;

  @Column("varchar", { name: "name", length: 255 })
  name: string;

  @Column("double", {
    name: "amount",
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: number;

  @Column("double", {
    name: "liquidateable",
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  liquidateable: number;

  @Column("double", {
    name: "earnings",
    precision: 32,
    scale: 2,
    default: () => "'0.00'",
  })
  earnings: number;

  @Column("int", { name: "days", nullable: true })
  days: number | null;

  @Column("double", { name: "percentage", precision: 22, default: () => "'0'" })
  percentage: number;

  @Column("double", {
    name: "tax",
    precision: 10,
    scale: 2,
    default: () => "'0.00'",
  })
  tax: number;

  @Column("timestamp", { name: "closing_at", nullable: true })
  closingAt: Date | null;

  @Column("timestamp", { name: "liquidated_at", nullable: true })
  liquidatedAt: Date | null;

  @Column("timestamp", { name: "withdrew_at", nullable: true })
  withdrewAt: Date | null;

  @Column("timestamp", { name: "active_at", nullable: true })
  activeAt: Date | null;

  @Column("timestamp", { name: "withdrawal_requested_at", nullable: true })
  withdrawalRequestedAt: Date | null;

  @Column("varchar", { name: "status", length: 50 })
  status: string;

  @Column("tinyint", { name: "pnd", width: 1, default: () => "'0'" })
  pnd: boolean;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
