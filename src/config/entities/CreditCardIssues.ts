import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("credit_card_issues")
export class CreditCardIssues {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "initiator_id", nullable: true, unsigned: true })
  initiatorId: string | null;

  @Column("varchar", { name: "identifier", length: 255 })
  identifier: string;

  @Column("longtext", { name: "issue" })
  issue: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("json", { name: "data", nullable: true })
  data: object | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;
}
