import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyPlanPackages } from "./CompanyPlanPackages";
import { CompanyPlans } from "./CompanyPlans";

@Index("packages_plans_package_id_foreign", ["packageId"], {})
@Index("packages_plans_plan_id_foreign", ["planId"], {})
@Entity("packages_plans")
export class PackagesPlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "package_id", unsigned: true })
  packageId: string;

  @Column("bigint", { name: "plan_id", unsigned: true })
  planId: string;

  @Column("varchar", { name: "package_count", length: 255 })
  packageCount: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => CompanyPlanPackages,
    (companyPlanPackages) => companyPlanPackages.packagesPlans,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "package_id", referencedColumnName: "id" }])
  package: CompanyPlanPackages;

  @ManyToOne(() => CompanyPlans, (companyPlans) => companyPlans.packagesPlans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "plan_id", referencedColumnName: "id" }])
  plan: CompanyPlans;
}
