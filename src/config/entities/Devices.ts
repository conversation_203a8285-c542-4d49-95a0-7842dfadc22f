import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("devices_created_at_index", ["createdAt"], {})
@Index("devices_device_id_index", ["deviceId"], {})
@Index("devices_device_name_index", ["deviceName"], {})
@Index("devices_is_device_trusted_index", ["isDeviceTrusted"], {})
@Index("devices_user_agent_index", ["userAgent"], {})
@Index("devices_user_id_foreign", ["userId"], {})
@Entity("devices")
export class Devices {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "visitor", nullable: true, length: 45 })
  visitor: string | null;

  @Column("varchar", { name: "user_agent", nullable: true, length: 255 })
  userAgent: string | null;

  @Column("tinyint", {
    name: "is_device_trusted",
    width: 1,
    default: () => "'0'",
  })
  isDeviceTrusted: boolean;

  @Column("varchar", { name: "device_id", nullable: true, length: 100 })
  deviceId: string | null;

  @Column("varchar", { name: "device_name", nullable: true, length: 150 })
  deviceName: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.devices, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
