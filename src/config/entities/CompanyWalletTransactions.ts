import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyWallets } from "./CompanyWallets";

@Index(
  "company_wallet_transactions_company_wallet_id_foreign",
  ["companyWalletId"],
  {}
)
@Entity("company_wallet_transactions")
export class CompanyWalletTransactions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_wallet_id", unsigned: true })
  companyWalletId: string;

  @Column("enum", {
    name: "type",
    enum: [
      "create_wallet",
      "top-up",
      "card-fund",
      "limit-change",
      "clear-credit-balance",
      "card-maintenance-fee",
    ],
  })
  type:
    | "create_wallet"
    | "top-up"
    | "card-fund"
    | "limit-change"
    | "clear-credit-balance"
    | "card-maintenance-fee";

  @Column("varchar", { name: "payment_medium", nullable: true, length: 255 })
  paymentMedium: string | null;

  @Column("enum", { name: "wallet_type", enum: ["credit", "debit"] })
  walletType: "credit" | "debit";

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", { name: "amount_flag", enum: ["negative", "positive"] })
  amountFlag: "negative" | "positive";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @ManyToOne(
    () => CompanyWallets,
    (companyWallets) => companyWallets.companyWalletTransactions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_wallet_id", referencedColumnName: "id" }])
  companyWallet: CompanyWallets;
}
