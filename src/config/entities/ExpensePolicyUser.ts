import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ExpensePolicies } from "./ExpensePolicies";
import { Users } from "./Users";

@Index("expense_policy_user_expense_policy_id_foreign", ["expensePolicyId"], {})
@Index("expense_policy_user_user_id_foreign", ["userId"], {})
@Entity("expense_policy_user")
export class ExpensePolicyUser {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "expense_policy_id", unsigned: true })
  expensePolicyId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => ExpensePolicies,
    (expensePolicies) => expensePolicies.expensePolicyUsers,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_policy_id", referencedColumnName: "id" }])
  expensePolicy: ExpensePolicies;

  @ManyToOne(() => Users, (users) => users.expensePolicyUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
