import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CreditCardPayments } from "./CreditCardPayments";
import { CreditCardPendingRepayments } from "./CreditCardPendingRepayments";
import { CreditCardRepaymentReceipts } from "./CreditCardRepaymentReceipts";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { PersonalCardMaintenanceFees } from "./PersonalCardMaintenanceFees";
import { Users } from "./Users";
import { PersonalCardTransactions } from "./PersonalCardTransactions";
import { PersonalClearedStatements } from "./PersonalClearedStatements";
import { PersonalCreditCardRepayments } from "./PersonalCreditCardRepayments";
import { PersonalDefaultCharges } from "./PersonalDefaultCharges";
import { PersonalDeferredPlanPayments } from "./PersonalDeferredPlanPayments";
import { RewardWalletTransactions } from "./RewardWalletTransactions";
import { StatementDocuments } from "./StatementDocuments";
import { UserStatementComments } from "./UserStatementComments";

@Index("personal_account_statements_account_id_foreign", ["accountId"], {})
@Index("personal_account_statements_condition_index", ["condition"], {})
@Index("personal_account_statements_created_at_index", ["createdAt"], {})
@Index("personal_account_statements_created_date_index", ["createdDate"], {})
@Index("personal_account_statements_end_date_index", ["endDate"], {})
@Index(
  "personal_account_statements_maintenance_fee_id_foreign",
  ["maintenanceFeeId"],
  {}
)
@Index("personal_account_statements_start_date_index", ["startDate"], {})
@Index("personal_account_statements_user_id_foreign", ["userId"], {})
@Entity("personal_account_statements")
export class PersonalAccountStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("bigint", {
    name: "outstanding_statement_id",
    nullable: true,
    unsigned: true,
  })
  outstandingStatementId: string | null;

  @Column("bigint", {
    name: "maintenance_fee_id",
    nullable: true,
    unsigned: true,
  })
  maintenanceFeeId: string | null;

  @Column("varchar", { name: "condition", nullable: true, length: 250 })
  condition: string | null;

  @Column("decimal", {
    name: "opening_balance",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  openingBalance: string | null;

  @Column("decimal", {
    name: "carry_over_balance",
    precision: 8,
    scale: 2,
    default: () => "'0.00'",
  })
  carryOverBalance: string;

  @Column("decimal", {
    name: "interest",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  interest: string | null;

  @Column("decimal", {
    name: "agg_balance",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  aggBalance: string | null;

  @Column("decimal", {
    name: "fees_and_charges",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  feesAndCharges: string | null;

  @Column("decimal", {
    name: "deferred_plan_amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  deferredPlanAmount: string | null;

  @Column("decimal", { name: "total_outstanding", precision: 15, scale: 2 })
  totalOutstanding: string;

  @Column("decimal", {
    name: "payments",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  payments: string | null;

  @Column("decimal", {
    name: "discount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  discount: string | null;

  @Column("int", { name: "notified", nullable: true })
  notified: number | null;

  @Column("datetime", { name: "start_date", nullable: true })
  startDate: Date | null;

  @Column("datetime", { name: "end_date", nullable: true })
  endDate: Date | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @Column("varchar", { name: "wallet_earned", nullable: true, length: 255 })
  walletEarned: string | null;

  @Column("varchar", { name: "wallet_redeemed", nullable: true, length: 255 })
  walletRedeemed: string | null;

  @Column("varchar", { name: "wallet_balance", nullable: true, length: 255 })
  walletBalance: string | null;

  @Column("varchar", {
    name: "wallet_opening_balance",
    nullable: true,
    length: 255,
  })
  walletOpeningBalance: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(
    () => CreditCardPayments,
    (creditCardPayments) => creditCardPayments.statement
  )
  creditCardPayments: CreditCardPayments[];

  @OneToMany(
    () => CreditCardPendingRepayments,
    (creditCardPendingRepayments) => creditCardPendingRepayments.statement
  )
  creditCardPendingRepayments: CreditCardPendingRepayments[];

  @OneToMany(
    () => CreditCardRepaymentReceipts,
    (creditCardRepaymentReceipts) => creditCardRepaymentReceipts.statement
  )
  creditCardRepaymentReceipts: CreditCardRepaymentReceipts[];

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @ManyToOne(
    () => PersonalCardMaintenanceFees,
    (personalCardMaintenanceFees) =>
      personalCardMaintenanceFees.personalAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "maintenance_fee_id", referencedColumnName: "id" }])
  maintenanceFee: PersonalCardMaintenanceFees;

  @ManyToOne(() => Users, (users) => users.personalAccountStatements, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;

  @OneToMany(
    () => PersonalCardTransactions,
    (personalCardTransactions) => personalCardTransactions.statement
  )
  personalCardTransactions: PersonalCardTransactions[];

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.statement
  )
  personalClearedStatements: PersonalClearedStatements[];

  @OneToMany(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) => personalCreditCardRepayments.newStatement
  )
  personalCreditCardRepayments: PersonalCreditCardRepayments[];

  @OneToMany(
    () => PersonalCreditCardRepayments,
    (personalCreditCardRepayments) =>
      personalCreditCardRepayments.personalStatement
  )
  personalCreditCardRepayments2: PersonalCreditCardRepayments[];

  @OneToMany(
    () => PersonalDefaultCharges,
    (personalDefaultCharges) => personalDefaultCharges.originatingStatement
  )
  personalDefaultCharges: PersonalDefaultCharges[];

  @OneToMany(
    () => PersonalDefaultCharges,
    (personalDefaultCharges) => personalDefaultCharges.reportStatement
  )
  personalDefaultCharges2: PersonalDefaultCharges[];

  @OneToMany(
    () => PersonalDeferredPlanPayments,
    (personalDeferredPlanPayments) => personalDeferredPlanPayments.statement
  )
  personalDeferredPlanPayments: PersonalDeferredPlanPayments[];

  @OneToMany(
    () => RewardWalletTransactions,
    (rewardWalletTransactions) => rewardWalletTransactions.statement
  )
  rewardWalletTransactions: RewardWalletTransactions[];

  @OneToMany(
    () => StatementDocuments,
    (statementDocuments) => statementDocuments.statement
  )
  statementDocuments: StatementDocuments[];

  @OneToMany(
    () => UserStatementComments,
    (userStatementComments) => userStatementComments.personalAccountStatement
  )
  userStatementComments: UserStatementComments[];
}
