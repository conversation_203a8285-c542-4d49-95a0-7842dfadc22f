import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Affiliates } from "./Affiliates";

@Index("affiliate_profiles_account_no_unique", ["accountNo"], { unique: true })
@Index("affiliate_profiles_affiliate_id_foreign", ["affiliateId"], {})
@Entity("affiliate_profiles")
export class AffiliateProfiles {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", nullable: true, length: 36 })
  affiliateId: string | null;

  @Column("varchar", {
    name: "account_no",
    nullable: true,
    unique: true,
    length: 255,
  })
  accountNo: string | null;

  @Column("varchar", { name: "bank_code", nullable: true, length: 255 })
  bankCode: string | null;

  @Column("varchar", { name: "bank_name", nullable: true, length: 255 })
  bankName: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "account_name", nullable: true, length: 255 })
  accountName: string | null;

  @Column("timestamp", { name: "originator_at", nullable: true })
  originatorAt: Date | null;

  @ManyToOne(() => Affiliates, (affiliates) => affiliates.affiliateProfiles, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;
}
