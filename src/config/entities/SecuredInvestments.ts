import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index(
  "secured_investments_credit_builder_unlock_paid_at_index",
  ["creditBuilderUnlockPaidAt"],
  {}
)
@Index(
  "secured_investments_credit_builder_unlock_selected_at_index",
  ["creditBuilderUnlockSelectedAt"],
  {}
)
@Index(
  "secured_investments_investment_unlock_paid_at_index",
  ["investmentUnlockPaidAt"],
  {}
)
@Index(
  "secured_investments_investment_unlock_selected_at_index",
  ["investmentUnlockSelectedAt"],
  {}
)
@Index(
  "secured_investments_low_limit_unlock_paid_at_index",
  ["lowLimitUnlockPaidAt"],
  {}
)
@Index(
  "secured_investments_low_limit_unlock_selected_at_index",
  ["lowLimitUnlockSelectedAt"],
  {}
)
@Index("secured_investments_user_id_foreign", ["userId"], {})
@Entity("secured_investments")
export class SecuredInvestments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("datetime", { name: "investment_unlock_selected_at", nullable: true })
  investmentUnlockSelectedAt: Date | null;

  @Column("datetime", { name: "investment_unlock_paid_at", nullable: true })
  investmentUnlockPaidAt: Date | null;

  @Column("datetime", { name: "low_limit_unlock_selected_at", nullable: true })
  lowLimitUnlockSelectedAt: Date | null;

  @Column("datetime", { name: "low_limit_unlock_paid_at", nullable: true })
  lowLimitUnlockPaidAt: Date | null;

  @Column("datetime", {
    name: "credit_builder_unlock_selected_at",
    nullable: true,
  })
  creditBuilderUnlockSelectedAt: Date | null;

  @Column("datetime", { name: "credit_builder_unlock_paid_at", nullable: true })
  creditBuilderUnlockPaidAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.securedInvestments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
