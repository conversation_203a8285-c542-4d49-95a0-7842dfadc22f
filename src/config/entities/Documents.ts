import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("documents_user_id_foreign", ["userId"], {})
@Entity("documents")
export class Documents {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "filename", length: 255 })
  filename: string;

  @Column("longtext", { name: "url" })
  url: string;

  @Column("enum", {
    name: "type",
    nullable: true,
    enum: [
      "work_id",
      "bank_statement",
      "emp_letter",
      "govt_id",
      "custom",
      "credit_report",
      "appeal_letter_of_non_indebtedness",
    ],
  })
  type:
    | "work_id"
    | "bank_statement"
    | "emp_letter"
    | "govt_id"
    | "custom"
    | "credit_report"
    | "appeal_letter_of_non_indebtedness"
    | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("mediumtext", { name: "comments", nullable: true })
  comments: string | null;

  @ManyToOne(() => Users, (users) => users.documents, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
