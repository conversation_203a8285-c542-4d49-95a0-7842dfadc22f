import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Merchants } from "./Merchants";

@Index("cashback_settings_active_index", ["active"], {})
@Index("cashback_settings_merchant_id_foreign", ["merchantId"], {})
@Index("cashback_settings_type_index", ["type"], {})
@Entity("cashback_settings")
export class CashbackSettings {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("double", {
    name: "commission",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  commission: number | null;

  @Column("double", {
    name: "minimum",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  minimum: number | null;

  @Column("double", {
    name: "maximum",
    nullable: true,
    precision: 30,
    scale: 2,
    default: () => "'1600000000000000000000000000.00'",
  })
  maximum: number | null;

  @Column("double", {
    name: "maximum_discount_value",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  maximumDiscountValue: number | null;

  @Column("double", {
    name: "sharing_formula",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  sharingFormula: number | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", {
    name: "credpal_commission",
    nullable: true,
    length: 255,
  })
  credpalCommission: string | null;

  @Column("varchar", {
    name: "credpal_commission_type",
    nullable: true,
    length: 255,
  })
  credpalCommissionType: string | null;

  @Column("varchar", { name: "image", nullable: true, length: 255 })
  image: string | null;

  @Column("tinyint", {
    name: "active",
    nullable: true,
    width: 1,
    default: () => "'1'",
  })
  active: boolean | null;

  @Column("timestamp", { name: "deactivated_at", nullable: true })
  deactivatedAt: Date | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.cashbackSettings, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
