import { Column, Entity, Index, OneToMany } from "typeorm";
import { AffiliateCodes } from "./AffiliateCodes";
import { AffiliateOriginators } from "./AffiliateOriginators";
import { AffiliateProfiles } from "./AffiliateProfiles";
import { AffiliateRewardTransactions } from "./AffiliateRewardTransactions";
import { Clicks } from "./Clicks";
import { Rewards } from "./Rewards";

@Index("affiliates_approved_at_index", ["approvedAt"], {})
@Index("affiliates_created_at_index", ["createdAt"], {})
@Index("affiliates_email_unique", ["email"], { unique: true })
@Index("affiliates_phone_unique", ["phone"], { unique: true })
@Index("affiliates_rejected_at_index", ["rejectedAt"], {})
@Index("affiliates_source_index", ["source"], {})
@Index("affiliates_status_index", ["status"], {})
@Index("affiliates_suspended_at_index", ["suspendedAt"], {})
@Entity("affiliates")
export class Affiliates {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "first_name", nullable: true, length: 255 })
  firstName: string | null;

  @Column("varchar", { name: "last_name", nullable: true, length: 255 })
  lastName: string | null;

  @Column("varchar", { name: "email", unique: true, length: 255 })
  email: string;

  @Column("varchar", { name: "phone", unique: true, length: 255 })
  phone: string;

  @Column("varchar", { name: "password", length: 255 })
  password: string;

  @Column("varchar", { name: "source", length: 255, default: () => "'web'" })
  source: string;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "change_password_at", nullable: true })
  changePasswordAt: Date | null;

  @Column("timestamp", { name: "approved_at", nullable: true })
  approvedAt: Date | null;

  @Column("timestamp", { name: "rejected_at", nullable: true })
  rejectedAt: Date | null;

  @Column("timestamp", { name: "suspended_at", nullable: true })
  suspendedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(() => AffiliateCodes, (affiliateCodes) => affiliateCodes.affiliate)
  affiliateCodes: AffiliateCodes[];

  @OneToMany(
    () => AffiliateOriginators,
    (affiliateOriginators) => affiliateOriginators.affiliate
  )
  affiliateOriginators: AffiliateOriginators[];

  @OneToMany(
    () => AffiliateOriginators,
    (affiliateOriginators) => affiliateOriginators.originator
  )
  affiliateOriginators2: AffiliateOriginators[];

  @OneToMany(
    () => AffiliateProfiles,
    (affiliateProfiles) => affiliateProfiles.affiliate
  )
  affiliateProfiles: AffiliateProfiles[];

  @OneToMany(
    () => AffiliateRewardTransactions,
    (affiliateRewardTransactions) => affiliateRewardTransactions.affiliate
  )
  affiliateRewardTransactions: AffiliateRewardTransactions[];

  @OneToMany(() => Clicks, (clicks) => clicks.affiliate)
  clicks: Clicks[];

  @OneToMany(() => Rewards, (rewards) => rewards.affiliate)
  rewards: Rewards[];
}
