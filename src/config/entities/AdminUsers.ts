import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("admin_users_admin_id_foreign", ["adminId"], {})
@Index("admin_users_user_id_foreign", ["userId"], {})
@Entity("admin_users")
export class AdminUsers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "admin_id", unsigned: true })
  adminId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "contacted", "attended"],
    default: () => "'pending'",
  })
  status: "pending" | "contacted" | "attended";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.adminUsers, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "admin_id", referencedColumnName: "id" }])
  admin: Users;

  @ManyToOne(() => Users, (users) => users.adminUsers2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
