import {
  Column,
  Entity,
  Index,
  Join<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { Users } from "./Users";

@Index(
  "personal_card_utilizations_personal_card_accounts_id_foreign",
  ["personalCardAccountsId"],
  {}
)
@Index("personal_card_utilizations_user_id_foreign", ["userId"], {})
@Entity("personal_card_utilizations")
export class PersonalCardUtilizations {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "personal_card_accounts_id", unsigned: true })
  personalCardAccountsId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("int", { name: "count", default: () => "'0'" })
  count: number;

  @Column("varchar", { name: "month_year", length: 255 })
  monthYear: string;

  @Column("decimal", {
    name: "average",
    nullable: true,
    precision: 15,
    scale: 4,
  })
  average: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalCardUtilizations,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_card_accounts_id", referencedColumnName: "id" },
  ])
  personalCardAccounts: PersonalCardAccounts;

  @ManyToOne(() => Users, (users) => users.personalCardUtilizations, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
