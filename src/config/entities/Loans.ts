import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AdvanclyLoans } from './AdvanclyLoans';
import { EquityContributions } from './EquityContributions';
import { LenderRepayments } from './LenderRepayments';
import { LenderBatches } from './LenderBatches';
import { Lenders } from './Lenders';
import { Users } from './Users';
import { MerchantOrders } from './MerchantOrders';
import { PersonalLoanOfferLetters } from './PersonalLoanOfferLetters';
import { PersonalLoanRepaymentDefaultCharges } from './PersonalLoanRepaymentDefaultCharges';
import { RepaymentWalletHistories } from './RepaymentWalletHistories';
import { RepaymentWalletTransactions } from './RepaymentWalletTransactions';
import { Repayments } from './Repayments';
import { TangarineTasks } from './TangarineTasks';

@Index('loans_accepted_at_index', ['acceptedAt'], {})
@Index('loans_created_at_index', ['createdAt'], {})
@Index('loans_decided_at_index', ['decidedAt'], {})
@Index('loans_disbursed_at_index', ['disbursedAt'], {})
@Index('loans_existing_loan_amount_index', ['existingLoanAmount'], {})
@Index('loans_lender_batch_id_foreign', ['lenderBatchId'], {})
@Index('loans_lender_id_foreign', ['lenderId'], {})
@Index('loans_loan_type_index', ['loanType'], {})
@Index('loans_management_fee_index', ['managementFee'], {})
@Index('loans_meter_number_index', ['meterNumber'], {})
@Index('loans_reason_declined_index', ['reasonDeclined'], {})
@Index('loans_rejected_at_index', ['rejectedAt'], {})
@Index('loans_source_index', ['source'], {})
@Index('loans_status_index', ['status'], {})
@Index('loans_user_id_foreign', ['userId'], {})
@Entity('loans')
export class Loans {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: string;

  @Column('decimal', {
    name: 'loan_amount',
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  loanAmount: string | null;

  @Column('enum', {
    name: 'status',
    enum: [
      'pending',
      'approved',
      'disbursed',
      'declined',
      'accepted',
      'rejected',
      'paid',
      'confirmed',
    ],
    default: () => "'pending'",
  })
  status:
    | 'pending'
    | 'approved'
    | 'disbursed'
    | 'declined'
    | 'accepted'
    | 'rejected'
    | 'paid'
    | 'confirmed';

  @Column('varchar', { name: 'loan_type', nullable: true, length: 255 })
  loanType: string | null;

  @Column('int', { name: 'tenure' })
  tenure: number;

  @Column('double', { name: 'requested_amount', precision: 22 })
  requestedAmount: number;

  @Column('datetime', { name: 'decided_at', nullable: true })
  decidedAt: Date | null;

  @Column('datetime', { name: 'disbursed_at', nullable: true })
  disbursedAt: Date | null;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('datetime', { name: 'in_tangarine_at', nullable: true })
  inTangarineAt: Date | null;

  @Column('datetime', { name: 'in_advancly_at', nullable: true })
  inAdvanclyAt: Date | null;

  @Column('bigint', { name: 'lender_id', nullable: true, unsigned: true })
  lenderId: string | null;

  @Column('bigint', { name: 'lender_batch_id', nullable: true, unsigned: true })
  lenderBatchId: string | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('double', {
    name: 'interest_rate',
    precision: 22,
    default: () => "'0'",
  })
  interestRate: number;

  @Column('datetime', { name: 'accepted_at', nullable: true })
  acceptedAt: Date | null;

  @Column('datetime', { name: 'rejected_at', nullable: true })
  rejectedAt: Date | null;

  @Column('varchar', { name: 'source', nullable: true, length: 255 })
  source: string | null;

  @Column('varchar', { name: 'batch', nullable: true, length: 255 })
  batch: string | null;

  @Column('datetime', { name: 'officer_verified_at', nullable: true })
  officerVerifiedAt: Date | null;

  @Column('datetime', { name: 'operations_verified_at', nullable: true })
  operationsVerifiedAt: Date | null;

  @Column('tinyint', { name: 'existing_loan', width: 1, default: () => "'0'" })
  existingLoan: boolean;

  @Column('decimal', {
    name: 'existing_loan_amount',
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  existingLoanAmount: string | null;

  @Column('varchar', {
    name: 'existing_loan_company',
    nullable: true,
    length: 255,
  })
  existingLoanCompany: string | null;

  @Column('varchar', { name: 'meter_number', nullable: true, length: 255 })
  meterNumber: string | null;

  @Column('varchar', { name: 'reason_declined', nullable: true, length: 255 })
  reasonDeclined: string | null;

  @Column('decimal', {
    name: 'management_fee',
    nullable: true,
    unsigned: true,
    precision: 8,
    scale: 2,
  })
  managementFee: string | null;

  @OneToMany(() => AdvanclyLoans, (advanclyLoans) => advanclyLoans.loan)
  advanclyLoans: AdvanclyLoans[];

  @OneToMany(
    () => EquityContributions,
    (equityContributions) => equityContributions.loan,
  )
  equityContributions: EquityContributions[];

  @OneToMany(
    () => LenderRepayments,
    (lenderRepayments) => lenderRepayments.loan,
  )
  lenderRepayments: LenderRepayments[];

  @ManyToOne(() => LenderBatches, (lenderBatches) => lenderBatches.loans, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'lender_batch_id', referencedColumnName: 'id' }])
  lenderBatch: LenderBatches;

  @ManyToOne(() => Lenders, (lenders) => lenders.loans, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'lender_id', referencedColumnName: 'id' }])
  lender: Lenders;

  @ManyToOne(() => Users, (users) => users.loans, {
    onDelete: 'CASCADE',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: Users;

  @OneToMany(() => MerchantOrders, (merchantOrders) => merchantOrders.loan)
  merchantOrders: MerchantOrders[];

  @OneToMany(
    () => PersonalLoanOfferLetters,
    (personalLoanOfferLetters) => personalLoanOfferLetters.loan,
  )
  personalLoanOfferLetters: PersonalLoanOfferLetters[];

  @OneToMany(
    () => PersonalLoanRepaymentDefaultCharges,
    (personalLoanRepaymentDefaultCharges) =>
      personalLoanRepaymentDefaultCharges.loan,
  )
  personalLoanRepaymentDefaultCharges: PersonalLoanRepaymentDefaultCharges[];

  @OneToMany(
    () => RepaymentWalletHistories,
    (repaymentWalletHistories) => repaymentWalletHistories.loan,
  )
  repaymentWalletHistories: RepaymentWalletHistories[];

  @OneToMany(
    () => RepaymentWalletTransactions,
    (repaymentWalletTransactions) => repaymentWalletTransactions.loan,
  )
  repaymentWalletTransactions: RepaymentWalletTransactions[];

  @OneToMany(() => Repayments, (repayments) => repayments.loan)
  repayments: Repayments[];

  @OneToMany(() => TangarineTasks, (tangarineTasks) => tangarineTasks.loan)
  tangarineTasks: TangarineTasks[];
}
