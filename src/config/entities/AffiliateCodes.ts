import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Affiliates } from "./Affiliates";
import { AffiliateRewardSettings } from "./AffiliateRewardSettings";
import { Clicks } from "./Clicks";
import { Rewards } from "./Rewards";

@Index("affiliate_codes_affiliate_id_foreign", ["affiliateId"], {})
@Index(
  "affiliate_codes_affiliate_reward_setting_id_foreign",
  ["affiliateRewardSettingId"],
  {}
)
@Index("affiliate_codes_code_unique", ["code"], { unique: true })
@Index("affiliate_codes_embed_dynamic_link_unique", ["embedDynamicLink"], {
  unique: true,
})
@Index("affiliate_codes_embed_unique", ["embed"], { unique: true })
@Index("affiliate_codes_social_dynamic_link_unique", ["socialDynamicLink"], {
  unique: true,
})
@Index("affiliate_codes_social_unique", ["social"], { unique: true })
@Entity("affiliate_codes")
export class AffiliateCodes {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", nullable: true, length: 36 })
  affiliateId: string | null;

  @Column("varchar", { name: "code", unique: true, length: 255 })
  code: string;

  @Column("varchar", { name: "embed", unique: true, length: 255 })
  embed: string;

  @Column("varchar", { name: "social", unique: true, length: 255 })
  social: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", {
    name: "embed_dynamic_link",
    nullable: true,
    unique: true,
    length: 255,
  })
  embedDynamicLink: string | null;

  @Column("varchar", {
    name: "social_dynamic_link",
    nullable: true,
    unique: true,
    length: 255,
  })
  socialDynamicLink: string | null;

  @Column("char", {
    name: "affiliate_reward_setting_id",
    nullable: true,
    length: 36,
  })
  affiliateRewardSettingId: string | null;

  @ManyToOne(() => Affiliates, (affiliates) => affiliates.affiliateCodes, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;

  @ManyToOne(
    () => AffiliateRewardSettings,
    (affiliateRewardSettings) => affiliateRewardSettings.affiliateCodes,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "affiliate_reward_setting_id", referencedColumnName: "id" },
  ])
  affiliateRewardSetting: AffiliateRewardSettings;

  @OneToMany(() => Clicks, (clicks) => clicks.affiliateCode)
  clicks: Clicks[];

  @OneToMany(() => Rewards, (rewards) => rewards.affiliateCode)
  rewards: Rewards[];
}
