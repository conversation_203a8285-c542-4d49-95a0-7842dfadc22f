import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { Affiliates } from "./Affiliates";

@Index(
  "affiliate_reward_transactions_affiliate_id_foreign",
  ["affiliateId"],
  {}
)
@Index("affiliate_reward_transactions_batch_index", ["batch"], {})
@Index("affiliate_reward_transactions_created_at_index", ["createdAt"], {})
@Index("affiliate_reward_transactions_provider_index", ["provider"], {})
@Index("affiliate_reward_transactions_reference_unique", ["reference"], {
  unique: true,
})
@Index("affiliate_reward_transactions_status_index", ["status"], {})
@Entity("affiliate_reward_transactions")
export class AffiliateRewardTransactions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", length: 36 })
  affiliateId: string;

  @Column("varchar", { name: "batch", length: 255 })
  batch: string;

  @Column("double", { name: "total_amount", precision: 30, scale: 2 })
  totalAmount: number;

  @Column("double", { name: "transfer_charge", precision: 30, scale: 2 })
  transferCharge: number;

  @Column("double", { name: "disbusable_amount", precision: 30, scale: 2 })
  disbusableAmount: number;

  @Column("varchar", { name: "provider", nullable: true, length: 255 })
  provider: string | null;

  @Column("varchar", { name: "transfer_code", nullable: true, length: 255 })
  transferCode: string | null;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Affiliates,
    (affiliates) => affiliates.affiliateRewardTransactions,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;
}
