import { Column, Entity, Index, OneToMany } from "typeorm";
import { Merchants } from "./Merchants";

@Index("industries_featured_index", ["featured"], {})
@Index("industries_status_index", ["status"], {})
@Entity("industries")
export class Industries {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @Column("varchar", { name: "slug", nullable: true, length: 255 })
  slug: string | null;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "image", nullable: true, length: 255 })
  image: string | null;

  @Column("tinyint", {
    name: "featured",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  featured: boolean | null;

  @Column("text", { name: "icon", nullable: true })
  icon: string | null;

  @Column("bigint", { name: "priority", nullable: true, default: () => "'1'" })
  priority: string | null;

  @OneToMany(() => Merchants, (merchants) => merchants.industry)
  merchants: Merchants[];
}
