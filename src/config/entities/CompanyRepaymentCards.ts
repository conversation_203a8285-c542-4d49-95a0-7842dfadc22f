import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_repayment_cards_company_id_foreign", ["companyId"], {})
@Entity("company_repayment_cards")
export class CompanyRepaymentCards {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "authorization_code", length: 255 })
  authorizationCode: string;

  @Column("varchar", { name: "email", nullable: true, length: 255 })
  email: string | null;

  @Column("varchar", { name: "card_type", nullable: true, length: 255 })
  cardType: string | null;

  @Column("varchar", { name: "last4", nullable: true, length: 255 })
  last4: string | null;

  @Column("varchar", { name: "exp_month", nullable: true, length: 255 })
  expMonth: string | null;

  @Column("varchar", { name: "exp_year", nullable: true, length: 255 })
  expYear: string | null;

  @Column("varchar", { name: "bin", nullable: true, length: 255 })
  bin: string | null;

  @Column("varchar", { name: "bank", nullable: true, length: 255 })
  bank: string | null;

  @Column("varchar", { name: "channel", nullable: true, length: 255 })
  channel: string | null;

  @Column("varchar", { name: "reusable", nullable: true, length: 255 })
  reusable: string | null;

  @Column("varchar", { name: "country_code", nullable: true, length: 255 })
  countryCode: string | null;

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive"],
    default: () => "'active'",
  })
  status: "active" | "inactive";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyRepaymentCards, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
