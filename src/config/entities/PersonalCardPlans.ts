import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { PersonalDeferredPlanPayments } from "./PersonalDeferredPlanPayments";
import { UserPlans } from "./UserPlans";

@Entity("personal_card_plans")
export class PersonalCardPlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "plan_name", length: 255 })
  planName: string;

  @Column("double", { name: "interest", precision: 8, scale: 2 })
  interest: number;

  @Column("int", { name: "default_fee" })
  defaultFee: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => PersonalDeferredPlanPayments,
    (personalDeferredPlanPayments) =>
      personalDeferredPlanPayments.personalCardPlans
  )
  personalDeferredPlanPayments: PersonalDeferredPlanPayments[];

  @OneToMany(() => UserPlans, (userPlans) => userPlans.originalPlan)
  userPlans: UserPlans[];
}
