import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>um<PERSON>,
  <PERSON>ToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("personal_repayment_cards_user_id_foreign", ["userId"], {})
@Entity("personal_repayment_cards")
export class PersonalRepaymentCards {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "authorization_code", length: 255 })
  authorizationCode: string;

  @Column("varchar", { name: "signature", nullable: true, length: 100 })
  signature: string | null;

  @Column("varchar", { name: "email", nullable: true, length: 255 })
  email: string | null;

  @Column("varchar", { name: "card_type", nullable: true, length: 255 })
  cardType: string | null;

  @Column("varchar", { name: "last4", nullable: true, length: 255 })
  last4: string | null;

  @Column("varchar", { name: "exp_month", nullable: true, length: 255 })
  expMonth: string | null;

  @Column("varchar", { name: "exp_year", nullable: true, length: 255 })
  expYear: string | null;

  @Column("varchar", { name: "bin", nullable: true, length: 255 })
  bin: string | null;

  @Column("varchar", { name: "bank", nullable: true, length: 255 })
  bank: string | null;

  @Column("varchar", { name: "channel", nullable: true, length: 255 })
  channel: string | null;

  @Column("varchar", { name: "reusable", nullable: true, length: 255 })
  reusable: string | null;

  @Column("varchar", { name: "country_code", nullable: true, length: 255 })
  countryCode: string | null;

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive"],
    default: () => "'active'",
  })
  status: "active" | "inactive";

  @Column("tinyint", { name: "valid", nullable: true, width: 1 })
  valid: boolean | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "charge_at", nullable: true })
  chargeAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.personalRepaymentCards, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
