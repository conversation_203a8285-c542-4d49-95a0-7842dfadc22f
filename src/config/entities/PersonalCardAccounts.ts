import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { CreditCardActivities } from './CreditCardActivities';
import { CreditCardWalletHistories } from './CreditCardWalletHistories';
import { LenderCreditCardLoans } from './LenderCreditCardLoans';
import { LenderCreditCardRepayments } from './LenderCreditCardRepayments';
import { PersonalAccountStatements } from './PersonalAccountStatements';
import { Users } from './Users';
import { PersonalCardMaintenanceFees } from './PersonalCardMaintenanceFees';
import { PersonalCardTransactions } from './PersonalCardTransactions';
import { PersonalCardUtilizations } from './PersonalCardUtilizations';
import { PersonalClearedStatements } from './PersonalClearedStatements';
import { PersonalDefaultCharges } from './PersonalDefaultCharges';
import { PersonalRepaymentTransactions } from './PersonalRepaymentTransactions';
import { UserStatementComments } from './UserStatementComments';

@Index('personal_card_accounts_account_no_unique', ['accountNo'], {
  unique: true,
})
@Index('personal_card_accounts_created_at_index', ['createdAt'], {})
@Index('personal_card_accounts_cycle_status_index', ['cycleStatus'], {})
@Index('personal_card_accounts_status_index', ['status'], {})
@Index('personal_card_accounts_type_index', ['type'], {})
@Index('personal_card_accounts_user_id_foreign', ['userId'], {})
@Entity('personal_card_accounts')
export class PersonalCardAccounts {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id', unsigned: true })
  id: string;

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: string;

  @Column('varchar', { name: 'account_no', unique: true, length: 255 })
  accountNo: string;

  @Column('decimal', {
    name: 'available_balance',
    nullable: true,
    precision: 15,
    scale: 2,
  })
  availableBalance: string | null;

  @Column('decimal', {
    name: 'credit_card_limit',
    nullable: true,
    precision: 15,
    scale: 2,
  })
  creditCardLimit: string | null;

  @Column('decimal', {
    name: 'available_credit',
    nullable: true,
    precision: 15,
    scale: 2,
  })
  availableCredit: string | null;

  @Column('enum', {
    name: 'status',
    enum: ['active', 'inactive'],
    default: () => "'inactive'",
  })
  status: 'active' | 'inactive';

  @Column('decimal', {
    name: 'repayment_percentage',
    precision: 15,
    scale: 4,
    default: () => "'100.0000'",
  })
  repaymentPercentage: string;

  @Column('varchar', { name: 'pin_status', nullable: true, length: 255 })
  pinStatus: string | null;

  @Column('tinyint', { name: 'freemium', width: 1, default: () => "'0'" })
  freemium: boolean;

  @Column('tinyint', { name: 'ongoing_charge', width: 1, default: () => "'0'" })
  ongoingCharge: boolean;

  @Column('datetime', {
    name: 'credit_card_limit_increased_at',
    nullable: true,
  })
  creditCardLimitIncreasedAt: Date | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('varchar', { name: 'provider', nullable: true, length: 255 })
  provider: string | null;

  @Column('varchar', { name: 'type', nullable: true, length: 255 })
  type: string | null;

  @Column('varchar', { name: 'cycle_status', nullable: true, length: 255 })
  cycleStatus: string | null;

  @Column('varchar', { name: 'mandate_status', nullable: true, length: 255 })
  mandateStatus: string | null;

  @Column('tinyint', {
    name: 'can_calculate_default',
    width: 1,
    default: () => "'1'",
  })
  canCalculateDefault: boolean;

  @Column('date', { name: 'freemium_expire_date', nullable: true })
  freemiumExpireDate: string | null;

  @OneToMany(
    () => CreditCardActivities,
    (creditCardActivities) => creditCardActivities.account,
  )
  creditCardActivities: CreditCardActivities[];

  @OneToMany(
    () => CreditCardWalletHistories,
    (creditCardWalletHistories) => creditCardWalletHistories.wallet,
  )
  creditCardWalletHistories: CreditCardWalletHistories[];

  @OneToMany(
    () => LenderCreditCardLoans,
    (lenderCreditCardLoans) => lenderCreditCardLoans.personalCardAccounts,
  )
  lenderCreditCardLoans: LenderCreditCardLoans[];

  @OneToMany(
    () => LenderCreditCardRepayments,
    (lenderCreditCardRepayments) =>
      lenderCreditCardRepayments.personalCardAccounts,
  )
  lenderCreditCardRepayments: LenderCreditCardRepayments[];

  @OneToMany(
    () => PersonalAccountStatements,
    (personalAccountStatements) => personalAccountStatements.account,
  )
  personalAccountStatements: PersonalAccountStatements[];

  @ManyToOne(() => Users, (users) => users.personalCardAccounts, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: Users;

  @OneToMany(
    () => PersonalCardMaintenanceFees,
    (personalCardMaintenanceFees) => personalCardMaintenanceFees.account,
  )
  personalCardMaintenanceFees: PersonalCardMaintenanceFees[];

  @OneToMany(
    () => PersonalCardTransactions,
    (personalCardTransactions) => personalCardTransactions.personalCardAccounts,
  )
  personalCardTransactions: PersonalCardTransactions[];

  @OneToMany(
    () => PersonalCardUtilizations,
    (personalCardUtilizations) => personalCardUtilizations.personalCardAccounts,
  )
  personalCardUtilizations: PersonalCardUtilizations[];

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.account,
  )
  personalClearedStatements: PersonalClearedStatements[];

  @OneToMany(
    () => PersonalDefaultCharges,
    (personalDefaultCharges) => personalDefaultCharges.account,
  )
  personalDefaultCharges: PersonalDefaultCharges[];

  @OneToMany(
    () => PersonalRepaymentTransactions,
    (personalRepaymentTransactions) => personalRepaymentTransactions.account,
  )
  personalRepaymentTransactions: PersonalRepaymentTransactions[];

  @OneToMany(
    () => UserStatementComments,
    (userStatementComments) => userStatementComments.personalCardAccount,
  )
  userStatementComments: UserStatementComments[];
}
