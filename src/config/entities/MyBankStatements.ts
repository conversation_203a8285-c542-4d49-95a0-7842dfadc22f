import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("my_bank_statements_ticket_id_unique", ["ticketId"], { unique: true })
@Index("my_bank_statements_user_id_foreign", ["userId"], {})
@Entity("my_bank_statements")
export class MyBankStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "account_number", length: 255 })
  accountNumber: string;

  @Column("varchar", { name: "bank_code", length: 255 })
  bankCode: string;

  @Column("varchar", { name: "phone", length: 255 })
  phone: string;

  @Column("varchar", { name: "error", length: 255 })
  error: string;

  @Column("boolean", { name: 'has_saved_doc', default: false })
  hasSavedDoc: boolean;

  @Column("varchar", {
    name: "ticket_id",
    nullable: true,
    unique: true,
    length: 255,
  })
  ticketId: string | null;

  @Column("varchar", { name: "password", nullable: true, length: 255 })
  password: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.myBankStatements, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
