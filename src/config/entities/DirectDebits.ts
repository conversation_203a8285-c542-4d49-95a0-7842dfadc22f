import { Column, Entity } from 'typeorm';

@Entity('direct_debits')
export class DirectDebits {
  @Column('char', { primary: true, name: 'id', length: 36 })
  id: string;

  @Column('varchar', { name: 'user_id', length: 255 })
  userId: string;

  @Column('varchar', { name: 'bank', length: 255 })
  bank: string;

  @Column('varchar', { name: 'authorization_code', length: 50 })
  authorizationCode: string;

  @Column('varchar', { name: 'signature', length: 50, nullable: true })
  signature: string;

  @Column('varchar', { name: 'status', length: 20, nullable: true })
  status: string;

  @Column('varchar', { name: 'bank_code', length: 50 })
  bankCode: string;

  @Column('date', { name: 'start_date' })
  startDate: Date;

  @Column('date', { name: 'end_date', nullable: true })
  endDate: Date | null;
 

  @Column('varchar', { name: 'last4', length: 25 })
  last4: string;

  @Column('varchar', { name: 'email', length: 255 })
  email: string;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;
}
