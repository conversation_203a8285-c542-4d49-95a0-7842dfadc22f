import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  JoinC<PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalClearedStatements } from "./PersonalClearedStatements";
import { PersonalCardPlans } from "./PersonalCardPlans";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { UserPlans } from "./UserPlans";
import { Users } from "./Users";

@Index(
  "personal_deferred_plan_payments_personal_card_plans_id_foreign",
  ["personalCardPlansId"],
  {}
)
@Index(
  "personal_deferred_plan_payments_statement_id_foreign",
  ["statementId"],
  {}
)
@Index(
  "personal_deferred_plan_payments_unique_plan_id_foreign",
  ["uniquePlanId"],
  {}
)
@Index("personal_deferred_plan_payments_user_id_foreign", ["userId"], {})
@Entity("personal_deferred_plan_payments")
export class PersonalDeferredPlanPayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "personal_card_plans_id", unsigned: true })
  personalCardPlansId: string;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("enum", {
    name: "status",
    enum: ["unpaid", "paid"],
    default: () => "'unpaid'",
  })
  status: "unpaid" | "paid";

  @Column("varchar", {
    name: "statement_status",
    length: 255,
    default: () => "'open'",
  })
  statementStatus: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("bigint", { name: "unique_plan_id", nullable: true, unsigned: true })
  uniquePlanId: string | null;

  @Column("bigint", { name: "statement_id", nullable: true, unsigned: true })
  statementId: string | null;

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.deferredPlan
  )
  personalClearedStatements: PersonalClearedStatements[];

  @ManyToOne(
    () => PersonalCardPlans,
    (personalCardPlans) => personalCardPlans.personalDeferredPlanPayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "personal_card_plans_id", referencedColumnName: "id" }])
  personalCardPlans: PersonalCardPlans;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalDeferredPlanPayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "statement_id", referencedColumnName: "id" }])
  statement: PersonalAccountStatements;

  @ManyToOne(
    () => UserPlans,
    (userPlans) => userPlans.personalDeferredPlanPayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "unique_plan_id", referencedColumnName: "id" }])
  uniquePlan: UserPlans;

  @ManyToOne(() => Users, (users) => users.personalDeferredPlanPayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
