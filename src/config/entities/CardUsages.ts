import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { Merchants } from "./Merchants";
import { Cashbacks } from "./Cashbacks";
import { PointTransactions } from "./PointTransactions";

@Index(
  "card_usages_cardusageable_type_cardusageable_id_index",
  ["cardusageableType", "cardusageableId"],
  {}
)
@Index("card_usages_merchant_id_foreign", ["merchantId"], {})
@Entity("card_usages")
export class CardUsages {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("varchar", { name: "card_id", nullable: true, length: 255 })
  cardId: string | null;

  @Column("varchar", { name: "account_type", nullable: true, length: 255 })
  accountType: string | null;

  @Column("varchar", { name: "cardusageable_type", length: 255 })
  cardusageableType: string;

  @Column("bigint", { name: "cardusageable_id", unsigned: true })
  cardusageableId: string;

  @Column("double", { name: "amount", nullable: true, precision: 30, scale: 2 })
  amount: number | null;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("date", { name: "transaction_date", nullable: true })
  transactionDate: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Merchants, (merchants) => merchants.cardUsages, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;

  @OneToMany(() => Cashbacks, (cashbacks) => cashbacks.cardUsage)
  cashbacks: Cashbacks[];

  @OneToMany(
    () => PointTransactions,
    (pointTransactions) => pointTransactions.cardUsage
  )
  pointTransactions: PointTransactions[];
}
