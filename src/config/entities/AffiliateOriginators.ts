import { Column, <PERSON>tity, Index, JoinColumn, ManyToOne } from "typeorm";
import { Affiliates } from "./Affiliates";

@Index("affiliate_originators_affiliate_id_foreign", ["affiliateId"], {})
@Index("affiliate_originators_originator_id_foreign", ["originatorId"], {})
@Entity("affiliate_originators")
export class AffiliateOriginators {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "affiliate_id", length: 36 })
  affiliateId: string;

  @Column("char", { name: "originator_id", length: 36 })
  originatorId: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Affiliates,
    (affiliates) => affiliates.affiliateOriginators,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "affiliate_id", referencedColumnName: "id" }])
  affiliate: Affiliates;

  @ManyToOne(
    () => Affiliates,
    (affiliates) => affiliates.affiliateOriginators2,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "originator_id", referencedColumnName: "id" }])
  originator: Affiliates;
}
