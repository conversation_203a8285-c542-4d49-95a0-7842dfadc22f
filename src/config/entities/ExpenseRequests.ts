import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { ExpenseApprovers } from "./ExpenseApprovers";
import { CompanyAccounts } from "./CompanyAccounts";
import { Companies } from "./Companies";
import { CompanyExpenseSubCategories } from "./CompanyExpenseSubCategories";
import { Groups } from "./Groups";
import { Users } from "./Users";

@Index("expense_requests_company_account_id_foreign", ["companyAccountId"], {})
@Index("expense_requests_company_id_foreign", ["companyId"], {})
@Index(
  "expense_requests_expense_sub_category_id_foreign",
  ["expenseSubCategoryId"],
  {}
)
@Index("expense_requests_group_id_foreign", ["groupId"], {})
@Index("expense_requests_user_id_foreign", ["userId"], {})
@Entity("expense_requests")
export class ExpenseRequests {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", {
    name: "company_account_id",
    nullable: true,
    unsigned: true,
  })
  companyAccountId: string | null;

  @Column("bigint", { name: "group_id", unsigned: true })
  groupId: string;

  @Column("bigint", { name: "expense_sub_category_id", unsigned: true })
  expenseSubCategoryId: string;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("enum", {
    name: "status",
    enum: ["approved", "declined", "pending"],
    default: () => "'pending'",
  })
  status: "approved" | "declined" | "pending";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => ExpenseApprovers,
    (expenseApprovers) => expenseApprovers.expenseRequest
  )
  expenseApprovers: ExpenseApprovers[];

  @ManyToOne(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.expenseRequests,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_account_id", referencedColumnName: "id" }])
  companyAccount: CompanyAccounts;

  @ManyToOne(() => Companies, (companies) => companies.expenseRequests, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyExpenseSubCategories,
    (companyExpenseSubCategories) =>
      companyExpenseSubCategories.expenseRequests,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "expense_sub_category_id", referencedColumnName: "id" }])
  expenseSubCategory: CompanyExpenseSubCategories;

  @ManyToOne(() => Groups, (groups) => groups.expenseRequests, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "group_id", referencedColumnName: "id" }])
  group: Groups;

  @ManyToOne(() => Users, (users) => users.expenseRequests, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
