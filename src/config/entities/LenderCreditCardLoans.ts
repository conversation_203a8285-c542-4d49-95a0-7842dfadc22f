import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Lenders } from "./Lenders";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { Users } from "./Users";

@Index("lender_credit_card_loans_lender_id_foreign", ["lenderId"], {})
@Index("lender_credit_card_loans_loan_id_unique", ["loanId"], { unique: true })
@Index(
  "lender_credit_card_loans_personal_card_accounts_id_foreign",
  ["personalCardAccountsId"],
  {}
)
@Index("lender_credit_card_loans_user_id_foreign", ["userId"], {})
@Entity("lender_credit_card_loans")
export class LenderCreditCardLoans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "lender_id", unsigned: true })
  lenderId: string;

  @Column("bigint", { name: "personal_card_accounts_id", unsigned: true })
  personalCardAccountsId: string;

  @Column("varchar", { name: "batch", length: 255 })
  batch: string;

  @Column("varchar", { name: "loan_id", unique: true, length: 255 })
  loanId: string;

  @Column("double", { name: "interest", precision: 22 })
  interest: number;

  @Column("int", { name: "tenure" })
  tenure: number;

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Lenders, (lenders) => lenders.lenderCreditCardLoans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lender_id", referencedColumnName: "id" }])
  lender: Lenders;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.lenderCreditCardLoans,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_card_accounts_id", referencedColumnName: "id" },
  ])
  personalCardAccounts: PersonalCardAccounts;

  @ManyToOne(() => Users, (users) => users.lenderCreditCardLoans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
