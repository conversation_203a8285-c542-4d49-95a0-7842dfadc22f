import {
  Column,
  Entity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Lenders } from "./Lenders";
import { Loans } from "./Loans";

@Index("lender_repayments_lender_id_foreign", ["lenderId"], {})
@Index("lender_repayments_lender_repayment_id_unique", ["lenderRepaymentId"], {
  unique: true,
})
@Index("lender_repayments_loan_id_foreign", ["loanId"], {})
@Entity("lender_repayments")
export class LenderRepayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("bigint", { name: "lender_id", unsigned: true })
  lenderId: string;

  @Column("varchar", { name: "lender_repayment_id", unique: true, length: 255 })
  lenderRepaymentId: string;

  @Column("varchar", { name: "batch", nullable: true, length: 255 })
  batch: string | null;

  @Column("decimal", { name: "principal_amount", precision: 15, scale: 2 })
  principalAmount: string;

  @Column("decimal", { name: "interest_amount", precision: 15, scale: 2 })
  interestAmount: string;

  @Column("decimal", { name: "repayment_amount", precision: 15, scale: 2 })
  repaymentAmount: string;

  @Column("double", {
    name: "interest_rate",
    nullable: true,
    precision: 22,
    default: () => "'0'",
  })
  interestRate: number | null;

  @Column("int", { name: "tenure", nullable: true })
  tenure: number | null;

  @Column("decimal", {
    name: "amount_paid",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amountPaid: string | null;

  @Column("decimal", { name: "fee", nullable: true, precision: 15, scale: 2 })
  fee: string | null;

  @Column("date", { name: "due_date" })
  dueDate: string;

  @Column("varchar", { name: "lender_status", nullable: true, length: 255 })
  lenderStatus: string | null;

  @Column("varchar", { name: "credpal_status", nullable: true, length: 255 })
  credpalStatus: string | null;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("datetime", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Lenders, (lenders) => lenders.lenderRepayments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lender_id", referencedColumnName: "id" }])
  lender: Lenders;

  @ManyToOne(() => Loans, (loans) => loans.lenderRepayments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;
}
