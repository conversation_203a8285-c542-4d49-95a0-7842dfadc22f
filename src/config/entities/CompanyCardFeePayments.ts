import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";
import { CompanyWallets } from "./CompanyWallets";

@Index("company_card_fee_payments_company_id_foreign", ["companyId"], {})
@Index(
  "company_card_fee_payments_company_statement_id_foreign",
  ["companyStatementId"],
  {}
)
@Index(
  "company_card_fee_payments_company_wallet_id_foreign",
  ["companyWalletId"],
  {}
)
@Entity("company_card_fee_payments")
export class CompanyCardFeePayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", {
    name: "company_wallet_id",
    nullable: true,
    unsigned: true,
  })
  companyWalletId: string | null;

  @Column("bigint", {
    name: "company_statement_id",
    nullable: true,
    unsigned: true,
  })
  companyStatementId: string | null;

  @Column("enum", { name: "type", enum: ["credit", "debit"] })
  type: "credit" | "debit";

  @Column("decimal", { name: "amount", precision: 15, scale: 2 })
  amount: string;

  @Column("enum", {
    name: "condition",
    enum: ["paid", "part-paid", "unpaid"],
    default: () => "'unpaid'",
  })
  condition: "paid" | "part-paid" | "unpaid";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyCardFeePayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyCardFeePayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_statement_id", referencedColumnName: "id" }])
  companyStatement: CompanyStatements;

  @ManyToOne(
    () => CompanyWallets,
    (companyWallets) => companyWallets.companyCardFeePayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_wallet_id", referencedColumnName: "id" }])
  companyWallet: CompanyWallets;
}
