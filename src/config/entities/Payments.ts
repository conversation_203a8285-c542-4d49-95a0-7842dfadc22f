import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { Users } from "./Users";

@Index("payments_company_id_foreign", ["companyId"], {})
@Index(
  "payments_payable_type_payable_id_index",
  ["payableType", "payableId"],
  {}
)
@Index("payments_user_id_foreign", ["userId"], {})
@Entity("payments")
export class Payments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "reference", length: 255 })
  reference: string;

  @Column("varchar", { name: "description", length: 255 })
  description: string;

  @Column("varchar", { name: "payable_type", length: 255 })
  payableType: string;

  @Column("bigint", { name: "payable_id", unsigned: true })
  payableId: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("double", { name: "amount", precision: 22 })
  amount: number;

  @Column("enum", {
    name: "payment_method",
    enum: [
      "paystack",
      "flutterwave",
      "bank_transfer",
      "korapay",
      "cash_wallet",
    ],
  })
  paymentMethod:
    | "paystack"
    | "flutterwave"
    | "bank_transfer"
    | "korapay"
    | "cash_wallet";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.payments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(() => Users, (users) => users.payments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
