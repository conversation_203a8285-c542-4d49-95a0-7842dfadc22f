import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Loans } from "./Loans";
import { Repayments } from "./Repayments";
import { RepaymentWalletTransactions } from "./RepaymentWalletTransactions";
import { Users } from "./Users";

@Index("repayment_wallet_histories_loan_id_foreign", ["loanId"], {})
@Index("repayment_wallet_histories_repayment_id_foreign", ["repaymentId"], {})
@Index(
  "repayment_wallet_histories_repayment_transaction_id_foreign",
  ["repaymentTransactionId"],
  {}
)
@Index("repayment_wallet_histories_user_id_foreign", ["userId"], {})
@Entity("repayment_wallet_histories")
export class RepaymentWalletHistories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", { name: "repayment_id", unsigned: true })
  repaymentId: string;

  @Column("bigint", { name: "repayment_transaction_id", unsigned: true })
  repaymentTransactionId: string;

  @Column("bigint", { name: "loan_id", unsigned: true })
  loanId: string;

  @Column("decimal", { name: "previous_total_paid", precision: 15, scale: 2 })
  previousTotalPaid: string;

  @Column("decimal", { name: "current_total_paid", precision: 15, scale: 2 })
  currentTotalPaid: string;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("decimal", { name: "fee", nullable: true, precision: 15, scale: 2 })
  fee: string | null;

  @Column("longtext", { name: "data" })
  data: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Loans, (loans) => loans.repaymentWalletHistories, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "loan_id", referencedColumnName: "id" }])
  loan: Loans;

  @ManyToOne(
    () => Repayments,
    (repayments) => repayments.repaymentWalletHistories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: Repayments;

  @ManyToOne(
    () => RepaymentWalletTransactions,
    (repaymentWalletTransactions) =>
      repaymentWalletTransactions.repaymentWalletHistories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "repayment_transaction_id", referencedColumnName: "id" },
  ])
  repaymentTransaction: RepaymentWalletTransactions;

  @ManyToOne(() => Users, (users) => users.repaymentWalletHistories, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
