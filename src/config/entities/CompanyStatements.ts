import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccountStatements } from "./CompanyAccountStatements";
import { CompanyCardFeePayments } from "./CompanyCardFeePayments";
import { CompanyCreditCardRepayments } from "./CompanyCreditCardRepayments";
import { CompanyCreditTransactions } from "./CompanyCreditTransactions";
import { CompanyDefaultCharges } from "./CompanyDefaultCharges";
import { CompanyStatementLogs } from "./CompanyStatementLogs";
import { CompanyStatementPayments } from "./CompanyStatementPayments";
import { Companies } from "./Companies";
import { CompanyWallets } from "./CompanyWallets";

@Index("company_statements_company_id_foreign", ["companyId"], {})
@Index("company_statements_company_wallet_id_foreign", ["companyWalletId"], {})
@Entity("company_statements")
export class CompanyStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "company_wallet_id", unsigned: true })
  companyWalletId: string;

  @Column("int", { name: "outstanding_statement_id", nullable: true })
  outstandingStatementId: number | null;

  @Column("decimal", {
    name: "aggregate_wallet_available_credit",
    precision: 15,
    scale: 2,
  })
  aggregateWalletAvailableCredit: string;

  @Column("decimal", {
    name: "maintenance_fee_deducted_from_wallet",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  maintenanceFeeDeductedFromWallet: string | null;

  @Column("decimal", {
    name: "maintenance_fee",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  maintenanceFee: string | null;

  @Column("decimal", {
    name: "aggregate_account_available_credit",
    precision: 15,
    scale: 2,
  })
  aggregateAccountAvailableCredit: string;

  @Column("decimal", { name: "aggregate_balance", precision: 15, scale: 2 })
  aggregateBalance: string;

  @Column("varchar", { name: "aggregate_interest", length: 255 })
  aggregateInterest: string;

  @Column("varchar", { name: "aggregate_fees", nullable: true, length: 255 })
  aggregateFees: string | null;

  @Column("decimal", {
    name: "total_amount_paid",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  totalAmountPaid: string | null;

  @Column("decimal", { name: "total_outstanding", precision: 15, scale: 2 })
  totalOutstanding: string;

  @Column("enum", {
    name: "condition",
    enum: ["paid", "part-paid", "unpaid"],
    default: () => "'unpaid'",
  })
  condition: "paid" | "part-paid" | "unpaid";

  @Column("enum", {
    name: "status",
    enum: ["open", "closed"],
    default: () => "'open'",
  })
  status: "open" | "closed";

  @Column("date", { name: "start_date" })
  startDate: string;

  @Column("date", { name: "end_date" })
  endDate: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyAccountStatements,
    (companyAccountStatements) => companyAccountStatements.companyStatement
  )
  companyAccountStatements: CompanyAccountStatements[];

  @OneToMany(
    () => CompanyCardFeePayments,
    (companyCardFeePayments) => companyCardFeePayments.companyStatement
  )
  companyCardFeePayments: CompanyCardFeePayments[];

  @OneToMany(
    () => CompanyCreditCardRepayments,
    (companyCreditCardRepayments) =>
      companyCreditCardRepayments.companyStatement
  )
  companyCreditCardRepayments: CompanyCreditCardRepayments[];

  @OneToMany(
    () => CompanyCreditTransactions,
    (companyCreditTransactions) => companyCreditTransactions.companyStatement
  )
  companyCreditTransactions: CompanyCreditTransactions[];

  @OneToMany(
    () => CompanyDefaultCharges,
    (companyDefaultCharges) => companyDefaultCharges.originatingStatement
  )
  companyDefaultCharges: CompanyDefaultCharges[];

  @OneToMany(
    () => CompanyDefaultCharges,
    (companyDefaultCharges) => companyDefaultCharges.reportStatement
  )
  companyDefaultCharges2: CompanyDefaultCharges[];

  @OneToMany(
    () => CompanyStatementLogs,
    (companyStatementLogs) => companyStatementLogs.companyStatement
  )
  companyStatementLogs: CompanyStatementLogs[];

  @OneToMany(
    () => CompanyStatementPayments,
    (companyStatementPayments) => companyStatementPayments.companyStatement
  )
  companyStatementPayments: CompanyStatementPayments[];

  @ManyToOne(() => Companies, (companies) => companies.companyStatements, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyWallets,
    (companyWallets) => companyWallets.companyStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_wallet_id", referencedColumnName: "id" }])
  companyWallet: CompanyWallets;
}
