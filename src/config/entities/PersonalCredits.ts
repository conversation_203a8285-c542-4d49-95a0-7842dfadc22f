import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { PersonalCreditApplications } from "./PersonalCreditApplications";

@Index("personal_credits_authorizer_id_foreign", ["authorizerId"], {})
@Index(
  "personal_credits_personal_credit_application_id_foreign",
  ["personalCreditApplicationId"],
  {}
)
@Index("personal_credits_user_id_foreign", ["userId"], {})
@Entity("personal_credits")
export class PersonalCredits {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "authorizer_id", nullable: true, unsigned: true })
  authorizerId: string | null;

  @Column("bigint", {
    name: "personal_credit_application_id",
    nullable: true,
    unsigned: true,
  })
  personalCreditApplicationId: string | null;

  @Column("double", {
    name: "amount",
    nullable: true,
    precision: 22,
    default: () => "'0'",
  })
  amount: number | null;

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive", "expired"],
    default: () => "'active'",
  })
  status: "active" | "inactive" | "expired";

  @Column("timestamp", { name: "expires_at", nullable: true })
  expiresAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("double", {
    name: "loan_limit",
    nullable: true,
    precision: 22,
    default: () => "'0'",
  })
  loanLimit: number | null;

  @ManyToOne(() => Users, (users) => users.personalCredits, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "authorizer_id", referencedColumnName: "id" }])
  authorizer: Users;

  @ManyToOne(
    () => PersonalCreditApplications,
    (personalCreditApplications) => personalCreditApplications.personalCredits,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_credit_application_id", referencedColumnName: "id" },
  ])
  personalCreditApplication: PersonalCreditApplications;

  @ManyToOne(() => Users, (users) => users.personalCredits2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
