import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Lenders } from "./Lenders";
import { PersonalCardAccounts } from "./PersonalCardAccounts";

@Index("lender_credit_card_repayments_lender_id_foreign", ["lenderId"], {})
@Index(
  "lender_credit_card_repayments_lender_repayment_id_unique",
  ["lenderRepaymentId"],
  { unique: true }
)
@Index(
  "lender_credit_card_repayments_personal_card_accounts_id_foreign",
  ["personalCardAccountsId"],
  {}
)
@Entity("lender_credit_card_repayments")
export class LenderCreditCardRepayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "personal_card_accounts_id", unsigned: true })
  personalCardAccountsId: string;

  @Column("bigint", { name: "lender_id", unsigned: true })
  lenderId: string;

  @Column("varchar", { name: "lender_repayment_id", unique: true, length: 255 })
  lenderRepaymentId: string;

  @Column("varchar", { name: "loan_id", length: 255 })
  loanId: string;

  @Column("varchar", { name: "batch", nullable: true, length: 255 })
  batch: string | null;

  @Column("varchar", { name: "tenure", nullable: true, length: 255 })
  tenure: string | null;

  @Column("decimal", { name: "principal_amount", precision: 15, scale: 2 })
  principalAmount: string;

  @Column("decimal", { name: "interest_amount", precision: 15, scale: 2 })
  interestAmount: string;

  @Column("decimal", { name: "repayment_amount", precision: 15, scale: 2 })
  repaymentAmount: string;

  @Column("decimal", {
    name: "amount_paid",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amountPaid: string | null;

  @Column("decimal", { name: "fee", nullable: true, precision: 15, scale: 2 })
  fee: string | null;

  @Column("date", { name: "due_date" })
  dueDate: string;

  @Column("varchar", { name: "lender_status", nullable: true, length: 255 })
  lenderStatus: string | null;

  @Column("varchar", { name: "credpal_status", nullable: true, length: 255 })
  credpalStatus: string | null;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("datetime", { name: "paid_at", nullable: true })
  paidAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Lenders, (lenders) => lenders.lenderCreditCardRepayments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "lender_id", referencedColumnName: "id" }])
  lender: Lenders;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.lenderCreditCardRepayments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_card_accounts_id", referencedColumnName: "id" },
  ])
  personalCardAccounts: PersonalCardAccounts;
}
