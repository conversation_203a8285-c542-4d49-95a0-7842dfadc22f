import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("beneficiaries_account_number_unique", ["accountNumber"], {
  unique: true,
})
@Index("beneficiaries_company_id_foreign", ["companyId"], {})
@Index("beneficiaries_name_unique", ["name"], { unique: true })
@Entity("beneficiaries")
export class Beneficiaries {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", unique: true, length: 255 })
  name: string;

  @Column("varchar", { name: "bank_id", length: 255 })
  bankId: string;

  @Column("varchar", { name: "account_number", unique: true, length: 255 })
  accountNumber: string;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "approved", "declined"],
    default: () => "'pending'",
  })
  status: "pending" | "approved" | "declined";

  @Column("varchar", {
    name: "paystack_reference",
    nullable: true,
    length: 255,
  })
  paystackReference: string | null;

  @Column("text", { name: "decline_reason", nullable: true })
  declineReason: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.beneficiaries, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
