import {
  Column,
  Entity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { RiskProfiles } from "./RiskProfiles";

@Index("repayment_options_risk_profile_id_foreign", ["riskProfileId"], {})
@Entity("repayment_options")
export class RepaymentOptions {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "risk_profile_id", unsigned: true })
  riskProfileId: string;

  @Column("double", { name: "value", precision: 22 })
  value: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => RiskProfiles,
    (riskProfiles) => riskProfiles.repaymentOptions,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "risk_profile_id", referencedColumnName: "id" }])
  riskProfile: RiskProfiles;
}
