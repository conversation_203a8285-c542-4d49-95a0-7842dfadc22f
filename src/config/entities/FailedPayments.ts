import {
  Column,
  <PERSON>tity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index(
  "failed_payments_payable_type_payable_id_index",
  ["payableType", "payableId"],
  {}
)
@Index("failed_payments_user_id_foreign", ["userId"], {})
@Index("failed_payments_vendor_index", ["vendor"], {})
@Entity("failed_payments")
export class FailedPayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "reference", nullable: true, length: 255 })
  reference: string | null;

  @Column("varchar", { name: "payable_type", nullable: true, length: 255 })
  payableType: string | null;

  @Column("bigint", { name: "payable_id", nullable: true })
  payableId: string | null;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "status", length: 255 })
  status: string;

  @Column("json", { name: "response" })
  response: object;

  @Column("double", { name: "amount", precision: 22 })
  amount: number;

  @Column("varchar", { name: "payment_method", nullable: true, length: 250 })
  paymentMethod: string | null;

  @Column("varchar", { name: "vendor", nullable: true, length: 255 })
  vendor: string | null;

  @Column("json", { name: "meta", nullable: true })
  meta: object | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.failedPayments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
