import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index(
  "wallet_limit_requests_index",
  ["walletId", "currentLimit", "requestedLimit", "status"],
  {}
)
@Index("wallet_limit_requests_user_id_foreign", ["userId"], {})
@Entity("wallet_limit_requests")
export class WalletLimitRequests {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "wallet_id", length: 100 })
  walletId: string;

  @Column("decimal", { name: "current_limit", precision: 15, scale: 2 })
  currentLimit: string;

  @Column("decimal", { name: "requested_limit", precision: 15, scale: 2 })
  requestedLimit: string;

  @Column("decimal", {
    name: "approved_limit",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  approvedLimit: string | null;

  @Column("varchar", {
    name: "status",
    length: 255,
    default: () => "'pending'",
  })
  status: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.walletLimitRequests, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
