import {
  Column,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { RepaymentOptions } from "./RepaymentOptions";

@Index("risk_profiles_name_unique", ["name"], { unique: true })
@Index("risk_profiles_slug_unique", ["slug"], { unique: true })
@Entity("risk_profiles")
export class RiskProfiles {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "name", unique: true, length: 255 })
  name: string;

  @Column("varchar", { name: "slug", unique: true, length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("int", { name: "value" })
  value: number;

  @OneToMany(
    () => RepaymentOptions,
    (repaymentOptions) => repaymentOptions.riskProfile
  )
  repaymentOptions: RepaymentOptions[];
}
