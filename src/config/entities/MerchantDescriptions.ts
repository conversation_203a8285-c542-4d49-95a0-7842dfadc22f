import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from "typeorm";
import { Merchants } from "./Merchants";

@Index("merchant_descriptions_merchant_id_foreign", ["merchantId"], {})
@Entity("merchant_descriptions")
export class MerchantDescriptions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "merchant_id", nullable: true, length: 36 })
  merchantId: string | null;

  @Column("varchar", { name: "description", nullable: true, length: 255 })
  description: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "workplace_email", nullable: true, length: 255 })
  workplaceEmail: string | null;

  @Column("varchar", { name: "region", nullable: true, length: 255 })
  region: string | null;

  @Column("varchar", { name: "branch", nullable: true, length: 255 })
  branch: string | null;

  @Column("varchar", { name: "state", nullable: true, length: 255 })
  state: string | null;

  @Column("varchar", { name: "lga", nullable: true, length: 255 })
  lga: string | null;

  @Column("varchar", { name: "country", nullable: true, length: 255 })
  country: string | null;

  @Column("varchar", { name: "merchant_role", nullable: true, length: 255 })
  merchantRole: string | null;

  @Column("tinyint", { name: "spread_enabled", width: 1, default: () => "'1'" })
  spreadEnabled: boolean;

  @ManyToOne(() => Merchants, (merchants) => merchants.merchantDescriptions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "merchant_id", referencedColumnName: "id" }])
  merchant: Merchants;
}
