import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CreditCardPendingRepayments } from "./CreditCardPendingRepayments";
import { CreditCardRepaymentReceipts } from "./CreditCardRepaymentReceipts";
import { Users } from "./Users";
import { PersonalAccountStatements } from "./PersonalAccountStatements";

@Index("personal_credit_card_repayments_created_at_index", ["createdAt"], {})
@Index("personal_credit_card_repayments_editor_id_foreign", ["editorId"], {})
@Index(
  "personal_credit_card_repayments_new_statement_id_foreign",
  ["newStatementId"],
  {}
)
@Index(
  "personal_credit_card_repayments_payment_method_index",
  ["paymentMethod"],
  {}
)
@Index(
  "personal_credit_card_repayments_personal_statement_id_foreign",
  ["personalStatementId"],
  {}
)
@Index("personal_credit_card_repayments_status_index", ["status"], {})
@Index("personal_credit_card_repayments_user_id_foreign", ["userId"], {})
@Index("personal_credit_card_repayments_vendor_index", ["vendor"], {})
@Entity("personal_credit_card_repayments")
export class PersonalCreditCardRepayments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("bigint", {
    name: "personal_statement_id",
    nullable: true,
    unsigned: true,
  })
  personalStatementId: string | null;

  @Column("bigint", {
    name: "new_statement_id",
    nullable: true,
    unsigned: true,
  })
  newStatementId: string | null;

  @Column("bigint", { name: "editor_id", nullable: true, unsigned: true })
  editorId: string | null;

  @Column("varchar", { name: "payment_method", length: 255 })
  paymentMethod: string;

  @Column("varchar", { name: "vendor", nullable: true, length: 255 })
  vendor: string | null;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("decimal", {
    name: "current_outstanding",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  currentOutstanding: string | null;

  @Column("decimal", {
    name: "total_outstanding",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  totalOutstanding: string | null;

  @Column("decimal", {
    name: "processing_fee",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  processingFee: string | null;

  @Column("decimal", {
    name: "percentage",
    nullable: true,
    precision: 15,
    scale: 4,
  })
  percentage: string | null;

  @Column("varchar", { name: "clearance_type", nullable: true, length: 255 })
  clearanceType: string | null;

  @Column("varchar", { name: "status", nullable: true, length: 250 })
  status: string | null;

  @Column("json", { name: "meta", nullable: true })
  meta: object | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "settlement_status", nullable: true, length: 255 })
  settlementStatus: string | null;

  @Column("datetime", { name: "settled_at", nullable: true })
  settledAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(
    () => CreditCardPendingRepayments,
    (creditCardPendingRepayments) => creditCardPendingRepayments.repayment
  )
  creditCardPendingRepayments: CreditCardPendingRepayments[];

  @OneToMany(
    () => CreditCardRepaymentReceipts,
    (creditCardRepaymentReceipts) => creditCardRepaymentReceipts.repayment
  )
  creditCardRepaymentReceipts: CreditCardRepaymentReceipts[];

  @ManyToOne(() => Users, (users) => users.personalCreditCardRepayments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "editor_id", referencedColumnName: "id" }])
  editor: Users;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalCreditCardRepayments,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "new_statement_id", referencedColumnName: "id" }])
  newStatement: PersonalAccountStatements;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalCreditCardRepayments2,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "personal_statement_id", referencedColumnName: "id" }])
  personalStatement: PersonalAccountStatements;

  @ManyToOne(() => Users, (users) => users.personalCreditCardRepayments2, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
