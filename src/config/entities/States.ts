import { Column, Entity, OneToMany } from "typeorm";
import { Lgas } from "./Lgas";
import { UserProfiles } from "./UserProfiles";

@Entity("states")
export class States {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @Column("tinyint", { name: "is_active", width: 1, default: () => "'1'" })
  isActive: boolean;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(() => Lgas, (lgas) => lgas.state)
  lgases: Lgas[];

  @OneToMany(() => UserProfiles, (userProfiles) => userProfiles.state_2)
  userProfiles: UserProfiles[];
}
