import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("risk_matrices")
export class RiskMatrices {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "company_tier", length: 255 })
  companyTier: string;

  @Column("double", { name: "salary_start", precision: 22 })
  salaryStart: number;

  @Column("double", { name: "salary_end", precision: 22 })
  salaryEnd: number;

  @Column("double", { name: "monthly_interest", precision: 22 })
  monthlyInterest: number;

  @Column("double", { name: "debt_to_income_ratio", precision: 22 })
  debtToIncomeRatio: number;

  @Column("int", { name: "fractional_repayment" })
  fractionalRepayment: number;

  @Column("int", { name: "loan_tenure" })
  loanTenure: number;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("varchar", { name: "name", length: 255 })
  name: string;
}
