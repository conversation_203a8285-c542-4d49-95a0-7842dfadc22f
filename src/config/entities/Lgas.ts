import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import { States } from "./States";
import { UserProfiles } from "./UserProfiles";

@Index("lgas_state_id_foreign", ["stateId"], {})
@Entity("lgas")
export class Lgas {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "state_id", length: 36 })
  stateId: string;

  @Column("varchar", { name: "name", nullable: true, length: 255 })
  name: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => States, (states) => states.lgases, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "state_id", referencedColumnName: "id" }])
  state: States;

  @OneToMany(() => UserProfiles, (userProfiles) => userProfiles.lga_2)
  userProfiles: UserProfiles[];
}
