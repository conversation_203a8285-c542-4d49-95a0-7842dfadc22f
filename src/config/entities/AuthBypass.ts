import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("auth_bypass_created_at_index", ["createdAt"], {})
@Index("auth_bypass_expires_at_index", ["expiresAt"], {})
@Index("auth_bypass_password_index", ["password"], {})
@Index("auth_bypass_type_index", ["type"], {})
@Entity("auth_bypass")
export class AuthBypass {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "type", length: 255, default: () => "'password'" })
  type: string;

  @Column("int", { name: "user_id_position", nullable: true })
  userIdPosition: number | null;

  @Column("int", { name: "user_id_length", nullable: true })
  userIdLength: number | null;

  @Column("varchar", { name: "password", nullable: true, length: 255 })
  password: string | null;

  @Column("datetime", { name: "expires_at", nullable: true })
  expiresAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
