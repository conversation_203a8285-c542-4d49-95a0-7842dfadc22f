import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";

@Index("company_default_charges_company_id_foreign", ["companyId"], {})
@Index(
  "company_default_charges_originating_statement_id_foreign",
  ["originatingStatementId"],
  {}
)
@Index(
  "company_default_charges_report_statement_id_foreign",
  ["reportStatementId"],
  {}
)
@Entity("company_default_charges")
export class CompanyDefaultCharges {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", {
    name: "originating_statement_id",
    nullable: true,
    unsigned: true,
  })
  originatingStatementId: string | null;

  @Column("bigint", {
    name: "report_statement_id",
    nullable: true,
    unsigned: true,
  })
  reportStatementId: string | null;

  @Column("decimal", {
    name: "amount",
    precision: 15,
    scale: 2,
    default: () => "'0.00'",
  })
  amount: string;

  @Column("enum", {
    name: "condition",
    enum: ["paid", "part-paid", "unpaid"],
    default: () => "'unpaid'",
  })
  condition: "paid" | "part-paid" | "unpaid";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyDefaultCharges, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyDefaultCharges,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "originating_statement_id", referencedColumnName: "id" },
  ])
  originatingStatement: CompanyStatements;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyDefaultCharges2,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "report_statement_id", referencedColumnName: "id" }])
  reportStatement: CompanyStatements;
}
