import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index(
  "company_credit_card_limit_request_company_id_foreign",
  ["companyId"],
  {}
)
@Entity("company_credit_card_limit_request")
export class CompanyCreditCardLimitRequest {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("int", { name: "company_plans_id", nullable: true })
  companyPlansId: number | null;

  @Column("varchar", { name: "business_name", length: 255 })
  businessName: string;

  @Column("varchar", { name: "no_of_years", length: 255 })
  noOfYears: string;

  @Column("varchar", { name: "annual_revenue", length: 255 })
  annualRevenue: string;

  @Column("varchar", { name: "est_monthly_expenses", length: 255 })
  estMonthlyExpenses: string;

  @Column("varchar", { name: "annual_gross_revenue", length: 255 })
  annualGrossRevenue: string;

  @Column("varchar", { name: "directors_bvn", length: 255 })
  directorsBvn: string;

  @Column("varchar", { name: "directors_bvn_two", nullable: true, length: 255 })
  directorsBvnTwo: string | null;

  @Column("varchar", {
    name: "directors_bvn_three",
    nullable: true,
    length: 255,
  })
  directorsBvnThree: string | null;

  @Column("enum", {
    name: "status",
    enum: ["pending", "approved", "declined", "ready"],
    default: () => "'pending'",
  })
  status: "pending" | "approved" | "declined" | "ready";

  @Column("varchar", { name: "comments", nullable: true, length: 255 })
  comments: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyCreditCardLimitRequests,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
