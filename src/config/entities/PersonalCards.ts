import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("personal_cards_pan_unique", ["pan"], { unique: true })
@Entity("personal_cards")
export class PersonalCards {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "personal_card_accounts_id", unsigned: true })
  personalCardAccountsId: string;

  @Column("varchar", { name: "cms_card_id", length: 255 })
  cmsCardId: string;

  @Column("varchar", { name: "pan", nullable: true, unique: true, length: 255 })
  pan: string | null;

  @Column("varchar", { name: "cvv", nullable: true, length: 255 })
  cvv: string | null;

  @Column("varchar", { name: "pin", length: 255, default: () => "'1234'" })
  pin: string;

  @Column("varchar", { name: "expires_at", nullable: true, length: 255 })
  expiresAt: string | null;

  @Column("datetime", { name: "activation_date", nullable: true })
  activationDate: Date | null;

  @Column("varchar", { name: "issuer_code", nullable: true, length: 255 })
  issuerCode: string | null;

  @Column("enum", {
    name: "status",
    enum: ["active", "inactive"],
    default: () => "'inactive'",
  })
  status: "active" | "inactive";

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
