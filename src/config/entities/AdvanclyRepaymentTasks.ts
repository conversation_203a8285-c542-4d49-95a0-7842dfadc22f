import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Repayments } from "./Repayments";

@Index("advancly_repayment_tasks_repayment_id_foreign", ["repaymentId"], {})
@Entity("advancly_repayment_tasks")
export class AdvanclyRepaymentTasks {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "repayment_id", unsigned: true })
  repaymentId: string;

  @Column("enum", {
    name: "status",
    enum: ["pending", "success", "error"],
    default: () => "'pending'",
  })
  status: "pending" | "success" | "error";

  @Column("json", { name: "response", nullable: true })
  response: object | null;

  @Column("datetime", { name: "completed_at", nullable: true })
  completedAt: Date | null;

  @Column("varchar", { name: "pusher_type", nullable: true, length: 255 })
  pusherType: string | null;

  @Column("bigint", { name: "pusher_id", nullable: true, unsigned: true })
  pusherId: string | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => Repayments,
    (repayments) => repayments.advanclyRepaymentTasks,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "repayment_id", referencedColumnName: "id" }])
  repayment: Repayments;
}
