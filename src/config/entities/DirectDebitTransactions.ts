import { Column, Entity, Index } from 'typeorm';

@Index('direct_debit_transactions_reference_unique', ['reference'], {
  unique: true,
})
@Entity('direct_debit_transactions')
export class DirectDebitTransactions {
  @Column('char', { primary: true, name: 'id', length: 36 })
  id: string;

  @Column('varchar', { name: 'user_id', length: 255 })
  userId: string;

  @Column('varchar', { name: 'reference', unique: true, length: 255 })
  reference: string;

  @Column('decimal', { name: 'amount', precision: 15, scale: 2 })
  amount: string;

  @Column('varchar', { name: 'status', length: 50 })
  status: string;

  @Column('varchar', { name: 'type', length: 10, default: 'debit' })
  type: string;

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null;

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null;
}
