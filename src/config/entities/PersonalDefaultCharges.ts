import {
  Column,
  <PERSON>tity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { PersonalClearedStatements } from "./PersonalClearedStatements";
import { PersonalCardAccounts } from "./PersonalCardAccounts";
import { PersonalAccountStatements } from "./PersonalAccountStatements";

@Index("personal_default_charges_account_id_foreign", ["accountId"], {})
@Index("personal_default_charges_condition_index", ["condition"], {})
@Index("personal_default_charges_created_at_index", ["createdAt"], {})
@Index("personal_default_charges_created_date_index", ["createdDate"], {})
@Index(
  "personal_default_charges_originating_statement_id_foreign",
  ["originatingStatementId"],
  {}
)
@Index(
  "personal_default_charges_report_statement_id_foreign",
  ["reportStatementId"],
  {}
)
@Index("personal_default_charges_start_date_index", ["startDate"], {})
@Entity("personal_default_charges")
export class PersonalDefaultCharges {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "account_id", nullable: true, unsigned: true })
  accountId: string | null;

  @Column("bigint", {
    name: "originating_statement_id",
    nullable: true,
    unsigned: true,
  })
  originatingStatementId: string | null;

  @Column("bigint", {
    name: "report_statement_id",
    nullable: true,
    unsigned: true,
  })
  reportStatementId: string | null;

  @Column("decimal", {
    name: "unit_amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  unitAmount: string | null;

  @Column("double", { name: "amount", nullable: true, precision: 15, scale: 5 })
  amount: number | null;

  @Column("varchar", { name: "condition", nullable: true, length: 250 })
  condition: string | null;

  @Column("int", { name: "days", nullable: true, default: () => "'0'" })
  days: number | null;

  @Column("datetime", { name: "start_date", nullable: true })
  startDate: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @OneToMany(
    () => PersonalClearedStatements,
    (personalClearedStatements) => personalClearedStatements.defaultCharge
  )
  personalClearedStatements: PersonalClearedStatements[];

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.personalDefaultCharges,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "account_id", referencedColumnName: "id" }])
  account: PersonalCardAccounts;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalDefaultCharges,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "originating_statement_id", referencedColumnName: "id" },
  ])
  originatingStatement: PersonalAccountStatements;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.personalDefaultCharges2,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "report_statement_id", referencedColumnName: "id" }])
  reportStatement: PersonalAccountStatements;
}
