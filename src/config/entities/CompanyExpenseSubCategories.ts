import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyCreditTransactions } from "./CompanyCreditTransactions";
import { CompanyDebitTransactions } from "./CompanyDebitTransactions";
import { CompanyExpenseCategories } from "./CompanyExpenseCategories";
import { Companies } from "./Companies";
import { ExpenseRequests } from "./ExpenseRequests";

@Index(
  "company_expense_sub_categories_company_category_id_foreign",
  ["companyCategoryId"],
  {}
)
@Index("company_expense_sub_categories_company_id_foreign", ["companyId"], {})
@Entity("company_expense_sub_categories")
export class CompanyExpenseSubCategories {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", { name: "company_category_id", unsigned: true })
  companyCategoryId: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => CompanyCreditTransactions,
    (companyCreditTransactions) => companyCreditTransactions.expenseSubCategory
  )
  companyCreditTransactions: CompanyCreditTransactions[];

  @OneToMany(
    () => CompanyDebitTransactions,
    (companyDebitTransactions) => companyDebitTransactions.expenseSubCategory
  )
  companyDebitTransactions: CompanyDebitTransactions[];

  @ManyToOne(
    () => CompanyExpenseCategories,
    (companyExpenseCategories) =>
      companyExpenseCategories.companyExpenseSubCategories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_category_id", referencedColumnName: "id" }])
  companyCategory: CompanyExpenseCategories;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyExpenseSubCategories,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @OneToMany(
    () => ExpenseRequests,
    (expenseRequests) => expenseRequests.expenseSubCategory
  )
  expenseRequests: ExpenseRequests[];
}
