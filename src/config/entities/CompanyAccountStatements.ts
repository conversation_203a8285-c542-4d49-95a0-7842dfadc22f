import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyAccounts } from "./CompanyAccounts";
import { CompanyCards } from "./CompanyCards";
import { Companies } from "./Companies";
import { CompanyStatements } from "./CompanyStatements";

@Index(
  "company_account_statements_company_account_id_foreign",
  ["companyAccountId"],
  {}
)
@Index(
  "company_account_statements_company_card_id_foreign",
  ["companyCardId"],
  {}
)
@Index("company_account_statements_company_id_foreign", ["companyId"], {})
@Index(
  "company_account_statements_company_statement_id_foreign",
  ["companyStatementId"],
  {}
)
@Entity("company_account_statements")
export class CompanyAccountStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, unsigned: true })
  companyId: string | null;

  @Column("bigint", { name: "company_account_id", unsigned: true })
  companyAccountId: string;

  @Column("bigint", { name: "company_card_id", nullable: true, unsigned: true })
  companyCardId: string | null;

  @Column("bigint", {
    name: "company_statement_id",
    nullable: true,
    unsigned: true,
  })
  companyStatementId: string | null;

  @Column("enum", {
    name: "condition",
    enum: ["paid", "unpaid", "part_paid"],
    default: () => "'unpaid'",
  })
  condition: "paid" | "unpaid" | "part_paid";

  @Column("decimal", { name: "balance", precision: 15, scale: 2 })
  balance: string;

  @Column("decimal", { name: "interest", precision: 15, scale: 2 })
  interest: string;

  @Column("varchar", { name: "group", length: 255 })
  group: string;

  @Column("decimal", { name: "available_credit", precision: 15, scale: 2 })
  availableCredit: string;

  @Column("date", { name: "start_date" })
  startDate: string;

  @Column("date", { name: "end_date" })
  endDate: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => CompanyAccounts,
    (companyAccounts) => companyAccounts.companyAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_account_id", referencedColumnName: "id" }])
  companyAccount: CompanyAccounts;

  @ManyToOne(
    () => CompanyCards,
    (companyCards) => companyCards.companyAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_card_id", referencedColumnName: "id" }])
  companyCard: CompanyCards;

  @ManyToOne(
    () => Companies,
    (companies) => companies.companyAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;

  @ManyToOne(
    () => CompanyStatements,
    (companyStatements) => companyStatements.companyAccountStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "company_statement_id", referencedColumnName: "id" }])
  companyStatement: CompanyStatements;
}
