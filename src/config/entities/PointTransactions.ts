import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, <PERSON>ToOne } from "typeorm";
import { CardUsages } from "./CardUsages";
import { Cashbacks } from "./Cashbacks";
import { Points } from "./Points";

@Index("point_transactions_card_usage_id_foreign", ["cardUsageId"], {})
@Index("point_transactions_cashback_id_foreign", ["cashbackId"], {})
@Index("point_transactions_point_id_foreign", ["pointId"], {})
@Index("point_transactions_reference_unique", ["reference"], { unique: true })
@Index("point_transactions_source_index", ["source"], {})
@Index("point_transactions_type_index", ["type"], {})
@Entity("point_transactions")
export class PointTransactions {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("char", { name: "point_id", nullable: true, length: 36 })
  pointId: string | null;

  @Column("char", { name: "cashback_id", nullable: true, length: 36 })
  cashbackId: string | null;

  @Column("char", { name: "card_usage_id", nullable: true, length: 36 })
  cardUsageId: string | null;

  @Column("double", { name: "point", nullable: true, precision: 30, scale: 2 })
  point: number | null;

  @Column("double", {
    name: "prevPoint",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  prevPoint: number | null;

  @Column("double", {
    name: "currentPoint",
    nullable: true,
    precision: 30,
    scale: 2,
  })
  currentPoint: number | null;

  @Column("varchar", { name: "type", nullable: true, length: 255 })
  type: string | null;

  @Column("varchar", { name: "source", nullable: true, length: 255 })
  source: string | null;

  @Column("varchar", { name: "reference", unique: true, length: 255 })
  reference: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => CardUsages, (cardUsages) => cardUsages.pointTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "card_usage_id", referencedColumnName: "id" }])
  cardUsage: CardUsages;

  @ManyToOne(() => Cashbacks, (cashbacks) => cashbacks.pointTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "cashback_id", referencedColumnName: "id" }])
  cashback: Cashbacks;

  @ManyToOne(() => Points, (points) => points.pointTransactions, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "point_id", referencedColumnName: "id" }])
  point_2: Points;
}
