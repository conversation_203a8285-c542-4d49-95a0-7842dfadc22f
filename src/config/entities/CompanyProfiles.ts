import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_profiles_company_id_foreign", ["companyId"], {})
@Entity("company_profiles")
export class CompanyProfiles {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("bigint", { name: "no_of_staff", unsigned: true })
  noOfStaff: string;

  @Column("bigint", { name: "no_of_exec", unsigned: true })
  noOfExec: string;

  @Column("text", { name: "company_address" })
  companyAddress: string;

  @Column("varchar", { name: "company_website", length: 255 })
  companyWebsite: string;

  @Column("int", { name: "salary_day" })
  salaryDay: number;

  @Column("int", { name: "billing_day_diff", nullable: true })
  billingDayDiff: number | null;

  @Column("int", { name: "payment_day_diff", nullable: true })
  paymentDayDiff: number | null;

  @Column("varchar", { name: "default_charge", nullable: true, length: 255 })
  defaultCharge: string | null;

  @Column("date", { name: "remita_mandate_expiry_date", nullable: true })
  remitaMandateExpiryDate: string | null;

  @Column("enum", {
    name: "salary_advance_type",
    nullable: true,
    enum: [
      "none",
      "salary_advance",
      "car",
      "housing",
      "emergency",
      "health",
      "education",
    ],
  })
  salaryAdvanceType:
    | "none"
    | "salary_advance"
    | "car"
    | "housing"
    | "emergency"
    | "health"
    | "education"
    | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyProfiles, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
