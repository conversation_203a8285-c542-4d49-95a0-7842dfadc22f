import {
  Column,
  <PERSON>ti<PERSON>,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { CommunicationTemplates } from "./CommunicationTemplates";

@Index("communications_admin_id_foreign", ["adminId"], {})
@Index("communications_last_read_at_index", ["lastReadAt"], {})
@Index("communications_show_call_to_action_index", ["showCallToAction"], {})
@Index("communications_subject_index", ["subject"], {})
@Index("communications_template_id_foreign", ["templateId"], {})
@Index("communications_times_read_index", ["timesRead"], {})
@Index("communications_to_app_index", ["toApp"], {})
@Index("communications_to_mail_index", ["toMail"], {})
@Index("communications_to_sms_index", ["toSms"], {})
@Index("communications_user_id_foreign", ["userId"], {})
@Entity("communications")
export class Communications {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "admin_id", unsigned: true })
  adminId: string;

  @Column("bigint", { name: "template_id", unsigned: true })
  templateId: string;

  @Column("varchar", { name: "subject", length: 255 })
  subject: string;

  @Column("text", { name: "body" })
  body: string;

  @Column("tinyint", { name: "to_sms", width: 1, default: () => "'0'" })
  toSms: boolean;

  @Column("tinyint", { name: "to_mail", width: 1, default: () => "'0'" })
  toMail: boolean;

  @Column("tinyint", { name: "to_app", width: 1, default: () => "'1'" })
  toApp: boolean;

  @Column("tinyint", {
    name: "show_call_to_action",
    width: 1,
    default: () => "'0'",
  })
  showCallToAction: boolean;

  @Column("int", { name: "times_read", default: () => "'0'" })
  timesRead: number;

  @Column("datetime", { name: "last_read_at", nullable: true })
  lastReadAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.communications, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "admin_id", referencedColumnName: "id" }])
  admin: Users;

  @ManyToOne(
    () => CommunicationTemplates,
    (communicationTemplates) => communicationTemplates.communications,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "template_id", referencedColumnName: "id" }])
  template: CommunicationTemplates;

  @ManyToOne(() => Users, (users) => users.communications2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
