import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { UserBankStatements } from "./UserBankStatements";
import { Users } from "./Users";

@Index("loanbot_blank_statements_retries_index", ["retries"], {})
@Index(
  "loanbot_blank_statements_user_bank_statement_id_foreign",
  ["userBankStatementId"],
  {}
)
@Index("loanbot_blank_statements_user_id_foreign", ["userId"], {})
@Entity("loanbot_blank_statements")
export class LoanbotBlankStatements {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", {
    name: "user_bank_statement_id",
    nullable: true,
    unsigned: true,
  })
  userBankStatementId: string | null;

  @Column("int", { name: "retries", default: () => "'0'" })
  retries: number;

  @Column("text", { name: "data" })
  data: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(
    () => UserBankStatements,
    (userBankStatements) => userBankStatements.loanbotBlankStatements,
    { onDelete: "NO ACTION", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "user_bank_statement_id", referencedColumnName: "id" }])
  userBankStatement: UserBankStatements;

  @ManyToOne(() => Users, (users) => users.loanbotBlankStatements, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
