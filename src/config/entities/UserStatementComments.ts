import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";
import { PersonalAccountStatements } from "./PersonalAccountStatements";
import { PersonalCardAccounts } from "./PersonalCardAccounts";

@Index("user_statement_comments_admin_id_foreign", ["adminId"], {})
@Index(
  "user_statement_comments_personal_account_statement_id_foreign",
  ["personalAccountStatementId"],
  {}
)
@Index(
  "user_statement_comments_personal_card_account_id_foreign",
  ["personalCardAccountId"],
  {}
)
@Index("user_statement_comments_user_id_foreign", ["userId"], {})
@Entity("user_statement_comments")
export class UserStatementComments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "admin_id", unsigned: true })
  adminId: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "personal_card_account_id", unsigned: true })
  personalCardAccountId: string;

  @Column("bigint", { name: "personal_account_statement_id", unsigned: true })
  personalAccountStatementId: string;

  @Column("text", { name: "comment" })
  comment: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.userStatementComments, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "admin_id", referencedColumnName: "id" }])
  admin: Users;

  @ManyToOne(
    () => PersonalAccountStatements,
    (personalAccountStatements) =>
      personalAccountStatements.userStatementComments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_account_statement_id", referencedColumnName: "id" },
  ])
  personalAccountStatement: PersonalAccountStatements;

  @ManyToOne(
    () => PersonalCardAccounts,
    (personalCardAccounts) => personalCardAccounts.userStatementComments,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([
    { name: "personal_card_account_id", referencedColumnName: "id" },
  ])
  personalCardAccount: PersonalCardAccounts;

  @ManyToOne(() => Users, (users) => users.userStatementComments2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
