import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_transfers_company_id_foreign", ["companyId"], {})
@Entity("company_transfers")
export class CompanyTransfers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("int", { name: "beneficiary_id" })
  beneficiaryId: number;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "label", length: 255 })
  label: string;

  @Column("varchar", { name: "description", length: 255 })
  description: string;

  @Column("int", { name: "amount" })
  amount: number;

  @Column("int", { name: "expense_category_id" })
  expenseCategoryId: number;

  @Column("datetime", { name: "request_time" })
  requestTime: Date;

  @Column("int", { name: "approver_id", nullable: true })
  approverId: number | null;

  @Column("int", { name: "requester_id" })
  requesterId: number;

  @Column("varchar", {
    name: "paystack_reference",
    nullable: true,
    length: 255,
  })
  paystackReference: string | null;

  @Column("enum", {
    name: "status",
    enum: ["pending", "approved", "successful", "declined"],
    default: () => "'pending'",
  })
  status: "pending" | "approved" | "successful" | "declined";

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyTransfers, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
