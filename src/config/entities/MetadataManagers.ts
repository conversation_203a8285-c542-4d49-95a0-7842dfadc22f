import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("metadata_managers")
export class MetadataManagers {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "code", length: 255 })
  code: string;

  @Column("varchar", { name: "metadata", length: 255 })
  metadata: string;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("varchar", { name: "status", nullable: true, length: 30 })
  status: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
