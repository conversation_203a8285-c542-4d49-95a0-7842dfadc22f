import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Users } from "./Users";

@Index("remita_logs_user_id_foreign", ["userId"], {})
@Entity("remita_logs")
export class RemitaLogs {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;
  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("varchar", { name: "slug", length: 255 })
  slug: string;

  @Column("varchar", { name: "request_id", length: 255 })
  requestId: string;

  @Column("varchar", { name: "mandate_id", nullable: true, length: 255 })
  mandateId: string | null;

  @Column("varchar", { name: "email", nullable: true, length: 255 })
  email: string | null;

  @Column("decimal", {
    name: "amount",
    nullable: true,
    precision: 15,
    scale: 2,
  })
  amount: string | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.remitaLogs, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
