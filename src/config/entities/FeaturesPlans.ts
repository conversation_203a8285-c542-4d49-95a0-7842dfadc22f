import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CompanyPlanFeatures } from "./CompanyPlanFeatures";
import { CompanyPlans } from "./CompanyPlans";

@Index("features_plans_feature_id_foreign", ["featureId"], {})
@Index("features_plans_plan_id_foreign", ["planId"], {})
@Entity("features_plans")
export class FeaturesPlans {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "feature_id", unsigned: true })
  featureId: string;

  @Column("bigint", { name: "plan_id", unsigned: true })
  planId: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(
    () => CompanyPlanFeatures,
    (companyPlanFeatures) => companyPlanFeatures.featuresPlans,
    { onDelete: "CASCADE", onUpdate: "NO ACTION" }
  )
  @JoinColumn([{ name: "feature_id", referencedColumnName: "id" }])
  feature: CompanyPlanFeatures;

  @ManyToOne(() => CompanyPlans, (companyPlans) => companyPlans.featuresPlans, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "plan_id", referencedColumnName: "id" }])
  plan: CompanyPlans;
}
