import { Column, Entity, Index, OneToMany } from "typeorm";
import { PointTransactions } from "./PointTransactions";

@Index(
  "points_pointable_type_pointable_id_index",
  ["pointableType", "pointableId"],
  {}
)
@Entity("points")
export class Points {
  @Column("char", { primary: true, name: "id", length: 36 })
  id: string;

  @Column("varchar", { name: "pointable_type", length: 255 })
  pointableType: string;

  @Column("bigint", { name: "pointable_id", unsigned: true })
  pointableId: string;

  @Column("double", { name: "point", nullable: true, precision: 30, scale: 2 })
  point: number | null;

  @Column("varchar", { name: "status", length: 255, default: () => "'active'" })
  status: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @OneToMany(
    () => PointTransactions,
    (pointTransactions) => pointTransactions.point_2
  )
  pointTransactions: PointTransactions[];
}
