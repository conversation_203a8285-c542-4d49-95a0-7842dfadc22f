import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("user_leads")
export class UserLeads {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("varchar", { name: "first_name", length: 255 })
  firstName: string;

  @Column("varchar", { name: "last_name", length: 255 })
  lastName: string;

  @Column("enum", {
    name: "employment_status",
    enum: ["self_employed", "student", "corps", "unemployed"],
    default: () => "'self_employed'",
  })
  employmentStatus: "self_employed" | "student" | "corps" | "unemployed";

  @Column("varchar", { name: "email", length: 255 })
  email: string;

  @Column("varchar", { name: "phone_no", length: 255 })
  phoneNo: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;
}
