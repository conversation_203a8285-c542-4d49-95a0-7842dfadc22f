import {
  Column,
  <PERSON><PERSON><PERSON>,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("secure_cards_mark_as_paid_by_foreign", ["markAsPaidBy"], {})
@Index("secure_cards_user_id_foreign", ["userId"], {})
@Entity("secure_cards")
export class SecureCards {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", nullable: true, unsigned: true })
  userId: string | null;

  @Column("tinyint", { name: "payment_status", width: 1, default: () => "'0'" })
  paymentStatus: boolean;

  @Column("bigint", { name: "mark_as_paid_by", nullable: true, unsigned: true })
  markAsPaidBy: string | null;

  @Column("varchar", { name: "receipt_path", nullable: true, length: 255 })
  receiptPath: string | null;

  @Column("timestamp", { name: "receipt_upload_at", nullable: true })
  receiptUploadAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.secureCards, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "mark_as_paid_by", referencedColumnName: "id" }])
  markAsPaidBy2: Users;

  @ManyToOne(() => Users, (users) => users.secureCards2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
