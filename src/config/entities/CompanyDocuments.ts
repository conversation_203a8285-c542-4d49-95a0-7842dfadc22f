import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Companies } from "./Companies";

@Index("company_documents_company_id_foreign", ["companyId"], {})
@Entity("company_documents")
export class CompanyDocuments {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "company_id", unsigned: true })
  companyId: string;

  @Column("varchar", { name: "bank_statements", length: 255 })
  bankStatements: string;

  @Column("varchar", { name: "share_allotment_doc", length: 255 })
  shareAllotmentDoc: string;

  @Column("varchar", { name: "director_particulars_doc", length: 255 })
  directorParticularsDoc: string;

  @Column("varchar", { name: "director_national_id", length: 255 })
  directorNationalId: string;

  @Column("varchar", {
    name: "second_director_national_id",
    nullable: true,
    length: 255,
  })
  secondDirectorNationalId: string | null;

  @Column("varchar", {
    name: "third_director_national_id",
    nullable: true,
    length: 255,
  })
  thirdDirectorNationalId: string | null;

  @Column("varchar", { name: "resolution_docs", length: 255 })
  resolutionDocs: string;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Companies, (companies) => companies.companyDocuments, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "company_id", referencedColumnName: "id" }])
  company: Companies;
}
