import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Users } from "./Users";

@Index("assignable_admins_authenticator_id_foreign", ["authenticatorId"], {})
@Index("assignable_admins_user_id_foreign", ["userId"], {})
@Entity("assignable_admins")
export class AssignableAdmins {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", unsigned: true })
  id: string;

  @Column("bigint", { name: "user_id", unsigned: true })
  userId: string;

  @Column("bigint", { name: "authenticator_id", unsigned: true })
  authenticatorId: string;

  @Column("varchar", { name: "type", length: 255 })
  type: string;

  @Column("timestamp", { name: "deleted_at", nullable: true })
  deletedAt: Date | null;

  @Column("timestamp", { name: "created_at", nullable: true })
  createdAt: Date | null;

  @Column("timestamp", { name: "updated_at", nullable: true })
  updatedAt: Date | null;

  @ManyToOne(() => Users, (users) => users.assignableAdmins, {
    onDelete: "NO ACTION",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "authenticator_id", referencedColumnName: "id" }])
  authenticator: Users;

  @ManyToOne(() => Users, (users) => users.assignableAdmins2, {
    onDelete: "CASCADE",
    onUpdate: "NO ACTION",
  })
  @JoinColumn([{ name: "user_id", referencedColumnName: "id" }])
  user: Users;
}
