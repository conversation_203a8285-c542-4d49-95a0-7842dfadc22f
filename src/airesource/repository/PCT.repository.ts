import { PersonalCardTransactions } from 'src/config/entities/PersonalCardTransactions';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class PCTRepository extends TypeOrmRepository<PersonalCardTransactions> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalCardTransactions, dataSource.createEntityManager());
  }
}
