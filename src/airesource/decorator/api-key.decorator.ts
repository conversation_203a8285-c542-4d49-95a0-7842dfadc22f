import {
  createParamDecorator,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import config from 'src/config';

export const GetApiKey = createParamDecorator((_, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  const apiKey = request.headers['cp-cc-api-key'];

  if (!apiKey || apiKey !== config.FT_API_KEY) {
    throw new UnauthorizedException('Invalid API key');
  }

  return apiKey;
});
