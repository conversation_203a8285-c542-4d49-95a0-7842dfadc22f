import { Injectable } from '@nestjs/common';
import {
  IPaginationMeta,
  IPaginationOptions,
  paginate,
} from 'nestjs-typeorm-paginate';
import { Users } from 'src/config/entities/Users';
import { UserRepository } from 'src/user/repository/user.repository';
import { PCTRepository } from './repository/PCT.repository';
import { PersonalCardTransactions } from 'src/config/entities/PersonalCardTransactions';

@Injectable()
export class AiresourceService {
  constructor(
    private readonly userRepo: UserRepository,
    private readonly tranxRepo: PCTRepository,
  ) {
    // setTimeout(() => {
    //   this.getUser('1').then((res) => {
    //     console.log(res);
    //   });
    // }, 4000);
  }

  async getUsers(options: IPaginationOptions<IPaginationMeta>) {
    const queryBuilder = this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userProfiles', 'profile')
      .orderBy('user.createdAt', 'DESC');

    const paginatedResult = await paginate<Users>(queryBuilder, options);
    const transformed = paginatedResult.items.map((user) => ({
      ...user,
      userProfile: user.userProfiles?.[0] || null,
      userProfiles: null,
    }));
    return { ...paginatedResult, items: transformed };
  }

  async getUser(id: string) {
    const user = await this.userRepo.findOne({
      where: { id },
      relations: ['userProfiles'],
    });

    return {
      ...user,
      userProfile: user.userProfiles?.[0] ?? null,
      userProfiles: null,
    };
  }

  async getTransactions(
    options: IPaginationOptions<IPaginationMeta>,
    userId?: string,
  ) {
    const queryBuilder = this.tranxRepo
      .createQueryBuilder('transaction')
      .orderBy('transaction.createdAt', 'DESC');

    if (userId) {
      queryBuilder.where('transaction.userId = :userId', { userId });
    }

    return paginate<PersonalCardTransactions>(queryBuilder, options);
  }
}
