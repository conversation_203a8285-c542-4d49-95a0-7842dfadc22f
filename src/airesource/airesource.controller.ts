import {
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { AiresourceService } from './airesource.service';
import { GetApiKey } from './decorator/api-key.decorator';

@Controller('airesource')
export class AiresourceController {
  constructor(private readonly airesourceService: AiresourceService) {}

  @Get('/users')
  create(
    @GetApiKey() apikey: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe)
    page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    const options = {
      page,
      limit,
    };
    return this.airesourceService.getUsers(options);
  }

  @Get('/users/:id')
  user(@GetApiKey() apikey: string, @Param('id') id: string) {
    return this.airesourceService.getUser(id);
  }

  @Get('/transactions')
  findAll(
    @GetApiKey() apikey: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe)
    page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('user') user?: string,
  ) {
    const options = {
      page,
      limit,
    };
    return this.airesourceService.getTransactions(options, user);
  }
}
