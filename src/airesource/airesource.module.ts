import { Module } from '@nestjs/common';
import { AiresourceService } from './airesource.service';
import { AiresourceController } from './airesource.controller';
import { PCTRepository } from './repository/PCT.repository';
import { UserRepository } from 'src/user/repository/user.repository';

@Module({
  controllers: [AiresourceController],
  providers: [AiresourceService, PCTRepository, UserRepository],
})
export class AiresourceModule {}
