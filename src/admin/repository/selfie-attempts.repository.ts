import { Injectable } from "@nestjs/common";
import { SelfieAttempt } from "src/config/entities/SelfieAttempt";
import { TypeOrmRepository } from "src/config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class SelfieAttemptsRepository  extends TypeOrmRepository<SelfieAttempt>{
    constructor(
        private readonly dataSource: DataSource
    ) {
       super(SelfieAttempt, dataSource.createEntityManager()) 
    }
}