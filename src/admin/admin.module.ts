import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { SelfieAttemptsRepository } from './repository/selfie-attempts.repository';
import { VerificationService } from '@app/verification';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: `${config.verification.uri}`,
      headers: { 
        'Content-Type': 'application/json',
        Accept: 'application/json', 
      },
    })],
  controllers: [AdminController],
  providers: [AdminService, SelfieAttemptsRepository, VerificationService],
})
export class AdminModule {}
