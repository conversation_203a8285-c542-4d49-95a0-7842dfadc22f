import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class UserSerfieAttemptsDto {
  @ApiProperty({ example: 1, description: 'User ID' })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiPropertyOptional({ example: 1, description: 'Page number' })
  @IsOptional()
  @IsNumber()
  page: number;

  @ApiPropertyOptional({ example: 10, description: 'Limit' })
  @IsOptional()
  @IsNumber()
  limit: number;
}
