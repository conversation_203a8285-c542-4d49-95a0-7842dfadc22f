import { Controller, Get, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { UserSerfieAttemptsDto } from './dto/user-selfie-attempts.dto';
import { BvnDetailsDto } from './dto/bvn-details-payload.dto';

@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get()
  getUserSelfieAttempts(@Query() payload: UserSerfieAttemptsDto) {
    payload.limit = payload.limit || 10;
    payload.page = payload.page || 1;
    return this.adminService.getuserSelfieAttempts(payload);
  }

  @Get()
  getUserBvnDetails(payload: BvnDetailsDto) {
    return this.adminService.getUserBvnDetails(payload.bvn);
  }
}
