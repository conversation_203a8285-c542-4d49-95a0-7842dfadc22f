import { Injectable, Logger } from '@nestjs/common';
import { SelfieAttemptsRepository } from './repository/selfie-attempts.repository';
import { UserSerfieAttemptsDto } from './dto/user-selfie-attempts.dto';
import { VerificationService } from '@app/verification';

@Injectable()
export class AdminService {
  private logger: Logger = new Logger(AdminService.name);
  constructor(
      private readonly selfieAttemptsRepository: SelfieAttemptsRepository,
      private readonly verificationService: VerificationService
  ) {}

  async getuserSelfieAttempts(payload: UserSerfieAttemptsDto) {
    try {
      return await this.selfieAttemptsRepository.findMany(
        {
          page: payload.page,
          limit: payload.limit,
        },
        {
          where: {
            userId: payload.userId,
          },
        },
      );
    } catch (error) {
      this.logger.log(`Error getting user selfie attempts: ${error.message}`);
      throw error;
    }
  }
    
    async getUserBvnDetails(bvn: string) {
    try {
     return await this.verificationService.getBvn(bvn);
    } catch (error) {
      this.logger.log(`Error getting user bvn details: ${error.message}`);
      throw error;
    }
  }
}
