import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { typeOrmConfig } from './config/data-source';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RabbitmqModule, RedisModule } from '@crednet/utils';
import config from './config';
import { BullModule } from '@nestjs/bullmq';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CashEvents, Exchanges, UserEvents } from './utils';
import { UserModule } from './user/user.module';
import { CronModule } from './cron/cron.module';
import { CreditModule } from './credit/credit.module';
import { AiresourceModule } from './airesource/airesource.module';
import { TranxRequestModule } from './tranx-request/tranx-request.module';
import { TransactionModule } from './transaction/transaction.module';
import { CacheManagerModule } from '@crednet/authmanager';
import { NotificationModule } from './notification/notification.module';
import { KycModule } from './kyc/kyc.module';
import { ReferralModule } from './referral/referral.module';
import { DirectDebitModule } from './direct-debit/direct-debit.module';
import { BankStatementModule } from './bank-statement/bank-statement.module';
import { CustomerIoModule } from './customer-io/customer-io.module';
import { AdminModule } from './admin/admin.module';
import { OrdersModule } from './orders/orders.module';

@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmConfig),
    CacheManagerModule.register(),
    BullModule.forRoot({
      prefix: `credit-${config.env.toLowerCase()}`,
      connection: {
        username: config.redis.user,
        password: config.redis.password,
        host: config.redis.host,
        port: config.redis.port,
        db: 6,
      },
    }),
    EventEmitterModule.forRoot({
      global: true,
    }),
    RedisModule.forRoot({
      redis: {
        url: config.redis.url,
      },
    }),
    UserModule,
    CronModule,
    CreditModule,
    AiresourceModule,
    TranxRequestModule,
    TransactionModule,
    NotificationModule,
    KycModule,
    ReferralModule,
    DirectDebitModule,
    BankStatementModule,
    CustomerIoModule,
    AdminModule,
    OrdersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
