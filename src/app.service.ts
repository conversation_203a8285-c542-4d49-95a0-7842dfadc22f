import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Events } from './utils';

@Injectable()
export class AppService {
  constructor(
    private readonly eventEmitter2: EventEmitter2,
  ) {}
  getHello(): string {
    return 'Hello World!';
  }

  verifyMandate(reference: string): string {
    this.eventEmitter2.emit(Events.VERIFY_MANDATE, {reference});
    return 'Mandate verified';
  }
}
