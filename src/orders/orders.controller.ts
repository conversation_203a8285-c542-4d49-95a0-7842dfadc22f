import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { OrdersService } from './orders.service';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth } from '@nestjs/swagger';
import {
  CheckoutDto,
  GetAmountDto,
  MakeFirstEquityPaymentDto,
} from './dtos/orders.dto';

@Controller('orders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post('/checkout')
  checkout(
    @Body() dto: CheckoutDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.ordersService.checkout(dto, auth.id + '');
  }

  @Get('/get-amount')
  getAmount(
    @GetAuthData() auth: AuthData,
    @Query() getAmountDto: GetAmountDto,
  ): Promise<any> {
    return this.ordersService.getAmount(auth.id + '', getAmountDto);
  }

  @Post('/make-initial-equity-payment')
  @PinRequirement('pin')
  makeInitialEquityPayment(
    @Body() dto: MakeFirstEquityPaymentDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.ordersService.makeInitialEquityPaymentForAnOrder(dto, auth);
  }
}
