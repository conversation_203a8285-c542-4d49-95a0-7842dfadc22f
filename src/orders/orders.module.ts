import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { MerchantOrderRepository } from './repositories/orders.repository';
import { CartItemsRepository } from './repositories/cart.items.repository';
import { EquityContributionsRepository } from './repositories/equity.contributions.repositories';
import { PersonalCardAccountsRepository } from '../credit/repository/personal-card-account.repository';
import { UserRepository } from '../user/repository/user.repository';
import { UserProfileRepository } from '../user/repository/user-profile.repository';

@Module({
  controllers: [OrdersController],
  providers: [
    OrdersService,
    MerchantOrderRepository,
    EquityContributionsRepository,
    PersonalCardAccountsRepository,
    CartItemsRepository,
    UserRepository,
    UserProfileRepository,
  ],
})
export class OrdersModule {}
