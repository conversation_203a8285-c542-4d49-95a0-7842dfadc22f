import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  ValidateNested,
  IsNumber,
  Length,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CartItemDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsString()
  status: string;

  @ApiProperty()
  @IsString()
  merchant: string;
}

export enum PaymentOptions {
  FULL_PAYMENT = 'full-payment',
  BNPL = 'bnpl',
}

export class CheckoutDto {
  @ApiProperty({ type: [CartItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemDto)
  cartItems: CartItemDto[];
}

export class MakeFirstEquityPaymentDto {
  @ApiProperty()
  @IsString()
  orderNo: string;

  @ApiProperty()
  @IsBoolean()
  isPayingUpfront: boolean;

  @ApiProperty()
  @IsBoolean()
  hasAccount: boolean;

  @ApiProperty()
  @IsBoolean()
  hasGoodCredit: boolean;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty()
  @IsString()
  @Length(4, 4)
  pin: string;
}

export class GetAmountDto {
  @ApiProperty()
  @IsString()
  orderNo: string;

  @ApiProperty({ enum: PaymentOptions })
  @IsEnum(PaymentOptions)
  paymentOption: PaymentOptions;
}
