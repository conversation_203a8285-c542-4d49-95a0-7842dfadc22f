import { BadRequestException, Injectable } from '@nestjs/common';
import { MerchantOrderRepository } from './repositories/orders.repository';
import { CartItemsRepository } from './repositories/cart.items.repository';
import {
  CheckoutDto,
  MakeFirstEquityPaymentDto,
  GetAmountDto,
  PaymentOptions,
} from './dtos/orders.dto';
import { randomUUID } from 'crypto';
import { DataSource } from 'typeorm';
import {
  MerchantOrders,
  MerchantOrdersStatus,
} from '../config/entities/MerchantOrders';
import { CartItems } from '../config/entities/cartItems';
import { AuthData } from '@crednet/authmanager';
import { EquityContributionsRepository } from './repositories/equity.contributions.repositories';
import { PersonalCardAccountsRepository } from '../credit/repository/personal-card-account.repository';
import { UserRepository } from '../user/repository/user.repository';
import {
  EquityContributions,
  status,
} from '../config/entities/EquityContributions';
import {
  Currency,
  PaymentCacheService,
  PaymentTransactionSource,
  PaymentTransactionWalletType,
} from '@crednet/utils';
import { PaymentRequestEventTypes } from '../utils/events';
import { UserProfileRepository } from '../user/repository/user-profile.repository';
import { ConfigurationRepository } from '../credit/repository/configuration.repository';

@Injectable()
export class OrdersService {
  constructor(
    private readonly merchantOrderRepository: MerchantOrderRepository,
    private readonly equityContributionsRepository: EquityContributionsRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly cartItemsRepository: CartItemsRepository,
    private readonly userRepository: UserRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly configurationRepository: ConfigurationRepository,
    private readonly dataSource: DataSource,
  ) {}

  async checkout(dto: CheckoutDto, userId: string): Promise<any> {
    const userProfile = await this.userProfileRepository.findOne({
      where: { userId },
    });

    if (
      userProfile.status !== 'activated' &&
      userProfile.status !== 'pre-approved'
    ) {
      throw new BadRequestException('Your profile is not activated');
    }

    return await this.dataSource.transaction(async (manager) => {
      const totalAmount = dto.cartItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
      );

      const merchantOrder = await manager.save(MerchantOrders, {
        orderNo: randomUUID(),
        userId: userId,
        item: dto.cartItems[0]?.name || '',
        amount: totalAmount,
        status: 'awaiting-payment',
        channel: 'Chocho',
        //todo add address later
      });

      const cartItems = dto.cartItems.map((item) => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        status: 'pending',
        merchant: 'Chocho',
        userId: userId,
        merchantOrderId: merchantOrder.id,
      }));

      await manager.save(CartItems, cartItems);

      return {
        orderId: merchantOrder.id,
        orderNo: merchantOrder.orderNo,
        totalAmount: totalAmount,
        itemCount: dto.cartItems.length,
      };
    });
  }

  async getMarchantOrder(orderNo: string) {
    return await this.merchantOrderRepository.findOne({
      where: { orderNo },
      relations: ['user', 'cartItems'],
    });
  }

  async getAmount(userId: string, getAmountDto: GetAmountDto) {
    const orderNo = getAmountDto.orderNo;

    const order = await this.merchantOrderRepository.findOne({
      where: { orderNo },
    });

    if (!order) {
      throw new BadRequestException('Order not found');
    }

    //case 1 user wants to pay 100 percent of the amount, the amount is 100 percent of the amount, the type is full payment
    // case 2 user wants to use BNPL and has a good credit, the amount is 40 percent of the amount
    // case 3 user wants to use BNPL and has a bad credit, the amount is 40 percent of the amount + (total price minus avaiable credit)
    // case 4 user is not eligible for cedit yet so we take the default 40 percent of the amount
    let amountToPay = order.amount;
    let isPayingUpfront: boolean = true;
    let hasAccount: boolean = false;
    let hasGoodCredit: boolean = false;

    if (getAmountDto.paymentOption === PaymentOptions.BNPL) {
      isPayingUpfront = false;
      const amountToPayForBNPL = order.amount * 0.4;
      const amountRemainingForCredit = order.amount * 0.6;

      const account = await this.personalCardAccountsRepository.findOne({
        where: { userId: userId },
      });

      if (account) {
        hasAccount = true;
        //todo do i check user profile credit limit too?
        const availableCredit = account.availableCredit;

        if (parseFloat(availableCredit) >= order.amount * 0.6) {
          hasGoodCredit = true;
          amountToPay = amountToPayForBNPL;
        } else {
          amountToPay =
            amountRemainingForCredit -
            parseFloat(availableCredit) +
            amountToPayForBNPL;
        }
      } else {
        amountToPay = amountToPayForBNPL;
      }
      return {
        amount: amountToPay,
        paymentOption: getAmountDto.paymentOption,
        isPayingUpfront,
        hasAccount,
        hasGoodCredit,
      };
    }
    return {
      amount: amountToPay,
      paymentOption: getAmountDto.paymentOption,
      isPayingUpfront,
      hasAccount,
      hasGoodCredit,
    };
  }

  async makeInitialEquityPaymentForAnOrder(
    dto: MakeFirstEquityPaymentDto,
    auth: AuthData,
  ) {
    this.checkPnd(auth);

    const order = await this.merchantOrderRepository.findOne({
      where: { orderNo: dto.orderNo },
    });

    const equityPayment = {
      userId: auth.id.toString(),
      amount: dto.amount,
      status: status.PENDING,
      reference: randomUUID(),
      orderId: order.id,
      orderAmount: order.amount,
      equityAmount: dto.amount,
      equityPercentage: (
        ((order.amount - dto.amount) / order.amount) *
        100
      ).toString(),
    };

    const equity = await this.equityContributionsRepository.save(equityPayment);

    return this.initiatePayment(equity, dto);
  }

  private async initiatePayment(
    equity: EquityContributions,
    dto: MakeFirstEquityPaymentDto,
  ) {
    await this.paymentCacheService.savePayment({
      source: PaymentTransactionSource.INVEST_SERVICE, //Todo: change source later
      userId: equity.userId,
      reference: equity.reference,
      walletType: PaymentTransactionWalletType.CREDPAL_CASH,
      currency: Currency.NGN,
      amount: equity.equityAmount,
      description: 'Equity Contribution',
      returningRoutingKey: PaymentRequestEventTypes.FUND_PAYMENT_STATUS,
      meta: {
        reference: equity.reference,
        amount: equity.equityAmount,
        isPayingUpfront: dto.isPayingUpfront,
        hasAccount: dto.hasAccount,
        hasGoodCredit: dto.hasGoodCredit,
      },
    });

    await this.equityContributionsRepository.update(
      { reference: equity.reference },
      { status: status.PROCESSING },
    );

    return equity;
  }

  async finalisePayment(dto: MakeFirstEquityPaymentDto, reference: string) {
    const equity = await this.equityContributionsRepository.findOne({
      where: { reference },
    });

    await this.equityContributionsRepository.update(
      { reference },
      { status: status.SUCCESS },
    );

    if (dto.isPayingUpfront) {
      await this.merchantOrderRepository.update(
        { id: equity.orderId },
        { status: MerchantOrdersStatus.APPROVED },
      );
    } else {
      if (dto.hasAccount) {
        const configuration = await this.configurationRepository.findOne({
          where: { name: 'days_before_payment' },
        });
        const allowedNumberOfDaysToPermitTheFirstMonThToBeFirstRepaymnt =
          configuration.value;
      }
    }

    return equity;
  }

  private checkPnd(auth: AuthData) {
    if (auth.pnd == 1) {
      throw new BadRequestException(
        'You are not allowed to complete this operation, please contact support',
      );
    }
  }
}
