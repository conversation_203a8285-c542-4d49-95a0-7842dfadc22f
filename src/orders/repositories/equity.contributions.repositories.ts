import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { EquityContributions } from 'src/config/entities/EquityContributions';

@Injectable()
export class EquityContributionsRepository extends TypeOrmRepository<EquityContributions> {
  constructor(private readonly dataSource: DataSource) {
    super(EquityContributions, dataSource.createEntityManager());
  }
}
