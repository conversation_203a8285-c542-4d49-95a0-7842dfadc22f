import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { MerchantOrders } from '../../config/entities/MerchantOrders';

@Injectable()
export class MerchantOrderRepository extends TypeOrmRepository<MerchantOrders> {
  constructor(private readonly dataSource: DataSource) {
    super(MerchantOrders, dataSource.createEntityManager());
  }
}
