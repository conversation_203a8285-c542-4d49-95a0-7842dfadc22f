import { Module } from '@nestjs/common';
import { CronService } from './cron.service';
import { CronController } from './cron.controller';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';
import { UserPlansRepository } from 'src/credit/repository/user-plan.repository';
import { CpcashWalletsRepository } from 'src/credit/repository/cp-cash-wallets.repository';
import { DirectDebitTransactions } from 'src/config/entities/DirectDebitTransactions';
import { DirectDebitTransactionsRepository } from 'src/direct-debit/repository/direct-debit-transactions.repositrory';
import { DirectDebitRepository } from 'src/direct-debit/repository/direct-debit.repository';
import { MbsRepository } from 'src/bank-statement/repositories/mbs.repository';
import { CustomerIoService } from 'src/customer-io/customer-io.service';
import { UserRepository } from 'src/user/repository/user.repository';

@Module({
  controllers: [CronController],
  providers: [
    CronService,
    PersonalCardAccountsRepository,
    UserPlansRepository,
    CpcashWalletsRepository,
    DirectDebitTransactionsRepository,
    DirectDebitRepository,
    MbsRepository,
    CustomerIoService,
    UserRepository,
  ],
})
export class CronModule {}
