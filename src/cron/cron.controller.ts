import { Controller } from '@nestjs/common';
import { CronService } from './cron.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Controller('cron')
export class CronController {
  constructor(private readonly cronService: CronService) {}

  // @Cron(CronExpression.EVERY_10_MINUTES)
  // syncAllRepaymentPercentage() {
  //   return this.cronService.syncAllRepaymentPercentage(1);
  // }

  @Cron(CronExpression.EVERY_10_MINUTES)
  verifyAllRepaymentPercentage() {
    return this.cronService.verifyMandateTransactions(1);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  verifyMandate() {
    return this.cronService.verifyMandate(1);
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  updateAccountMandateStatus() {
    return this.cronService.updateAccountMandateStatus(1);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  fetchStatementDocForPending() {
    return this.cronService.fetchStatementDocForPending(1);
  }
}
