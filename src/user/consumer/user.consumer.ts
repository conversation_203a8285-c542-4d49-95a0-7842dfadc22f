import { Injectable, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { UserService } from '../user.service';
import { CashEvents, Exchanges, UserEvents } from 'src/utils';
import { FirebaseTokenRepository } from '../repository/firebase-token.repository';
import { CashRepository } from '../repository/cash.repository';

@Injectable()
export class UserConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly firebaseTokenRepository: FirebaseTokenRepository,
    private readonly userService: UserService,
    private readonly cashRepository: CashRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.CREDIT}.${UserEvents.GET_USER}`,
      async ({ data, ack }) => {
        await this.getOneUser(data, ack);
      },
    );

    this.rmqService.subscribe(
      `${Exchanges.CREDIT}.${UserEvents.SYNC_CASH_USER}`,
      async ({ data, ack }) => {
        await this.syncCashUser(data, ack);
      },
    );
  }

  async syncCashUser(payload, ack): Promise<any> {
    console.log('payload', payload);
    ack(); // aknowledge the message reagrless of the outcome

    const { userId } = payload;

    const cashUser = await this.cashRepository.findOne({
      where: { userId },
    });

    if (!cashUser) {
      return;
    }

    await this.rmqService.send(Exchanges.CASH, {
      key: CashEvents.UPDATE_USERID,
      data: {
        userId,
        walletId: cashUser.walletId,
      },
    });
  }

  async getOneUser(payload, ack): Promise<any> {
    try {
      console.log('payload', payload);
      ack(); // aknowledge the message reagrless of the outcome
      const { userId } = payload;
      const user = await this.userService.findOne(userId);
      const token = await this.firebaseTokenRepository.findOne({
        where: { userId },
        select: ['deviceToken'],
      });

      await this.rmqService.send(Exchanges.CREDIT, {
        key: UserEvents.GET_USER_RESPONSE,
        data: {
          firstName: user.name,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNo,
          userId: user.id,
          deviceToken: token?.deviceToken,
        },
      });
    } catch (e) {
      console.log(e);
    }
  }
}
