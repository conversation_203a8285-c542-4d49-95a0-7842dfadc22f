import { Body, Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { AuthData, GetAuthData, JwtAuthGuard } from '@crednet/authmanager';
import { InitiatePurchaseDto } from 'src/credit/dto/initiate-purchase.dto';
import { ApiBearerAuth } from '@nestjs/swagger';
import { GetApiKey } from 'src/airesource/decorator/api-key.decorator';

@Controller('user')
// @UseGuards(JwtAuthGuard)
// @ApiBearerAuth('JWT')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('/account-details/:id')
  getAccountDetails(
    @GetApiKey() _,
    @Param('id') id: string,
  ): Promise<any> {
    return this.userService.getAccountDetails(id);
  }


  @Get('/card-details/:authorizationCode')
  getCardDetails(
    @GetApiKey() _,
    @Param('authorizationCode') authorizationCode: string,
  ): Promise<any> {
    return this.userService.getCardDetails(authorizationCode);
  }


 @Get('/user-details/:email')
  getUserDetails(
    @GetApiKey() _,
    @Param('email') email: string,
  ): Promise<any> {
    return this.userService.getUserDetails(email);
  }


}
