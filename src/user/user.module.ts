import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { UserConsumer } from './consumer/user.consumer';
import { UserRepository } from './repository/user.repository';
import { FirebaseTokenRepository } from './repository/firebase-token.repository';
import { RabbitmqModule } from '@crednet/utils';
import config from 'src/config';
import { Exchanges, UserEvents } from 'src/utils';
import { CashRepository } from './repository/cash.repository';
import { UserProfileRepository } from './repository/user-profile.repository';
import { CardRepository } from './repository/card.repository';
import { LoanbotVerificationsRepository } from 'src/credit/repository/loanbot-verification.repository';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';

@Module({
  imports: [
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'credit-user.queue',
      prefetchCount: 10,
      deadLetterQueueInterval: 100000,
      consumeDeadLetterQueue: config.isCronEnabled,
      showLog: true,
      global: false,
      producer: {
        name: Exchanges.CREDIT,
        durable: true,
      },
      subscriptions: [ 
        `${Exchanges.CREDIT}.${UserEvents.GET_USER}`, 
        `${Exchanges.CREDIT}.${UserEvents.SYNC_CASH_USER}`,
      ],
    }),
  ],

  controllers: [UserController],
  providers: [
    UserService,
    UserConsumer,
    UserRepository,
    FirebaseTokenRepository,
    CashRepository,
    UserProfileRepository, 
    CardRepository,
    LoanbotVerificationsRepository, 
    PersonalCardAccountsRepository
  ],
})
export class UserModule {}
