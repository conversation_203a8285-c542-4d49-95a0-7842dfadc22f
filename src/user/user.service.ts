import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from './repository/user.repository';
import { UserProfileRepository } from './repository/user-profile.repository';
import { CardRepository } from './repository/card.repository';
import { bvns, users } from './users';
import { RabbitmqService } from '@crednet/utils';
import { CreditScoreEvents, Exchanges, LoanbotEvents } from 'src/utils';
import { LoanbotVerificationsRepository } from 'src/credit/repository/loanbot-verification.repository';
import { IsNull, Not } from 'typeorm';
import { PersonalCardAccountsRepository } from 'src/credit/repository/personal-card-account.repository';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly profileRepository: UserProfileRepository,
    private readonly personalCardAccountsRepository: PersonalCardAccountsRepository,
    private readonly cardRepository: CardRepository,
    private readonly rabbitmqService: RabbitmqService,
    private readonly loanbotVerificationsRepository: LoanbotVerificationsRepository,
  ) {
    setTimeout(() => {
      // this.processReport();
    }, 10000);
  }
  async delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  async processReport() {
    // for (const user of users) {
    //   const profile = await this.profileRepository.findOne({
    //     where: { userId: user },
    //     select: ['bvn'],
    //   });
    //   if (profile.bvn) {
    //     console.log('running for ', profile.bvn, user)
    //     this.rabbitmqService.send(Exchanges.LOANBOT, {
    //       key: LoanbotEvents.PROCESS_CREDIT_REPORT,
    //       data: {
    //         bvn: profile.bvn,
    //       },
    //     });
    //   }
    // }
    for (const bvn of bvns) {
        console.log('running for ', bvn)
      await this.rabbitmqService.send(Exchanges.LOANBOT, {
        key: LoanbotEvents.PROCESS_CREDIT_REPORT,
        data: {
          bvn: bvn.toString()
        },
      });
    await this.delay(3000);
    }
  }

  async pushCreditReport(page = 1) {
    const { items, meta } = await this.loanbotVerificationsRepository.findMany(
      { page, limit: 50 },
      {
        where: {
          userId: '612974',
          // hasMadeFirstTimePayment: true,
          // creditScoreData: Not(IsNull()),
        },
      },
    );

    for (const item of items) {
      console.log(item.creditScoreData);
      const profile = await this.profileRepository.findOne({
        where: { userId: item.userId },
        select: ['userId', 'bvn'],
      });
      if (item.creditScoreData['credit_score'])
        await this.rabbitmqService.send(Exchanges.CREDIT_SCORE, {
          key: CreditScoreEvents.SEED_SCORE,
          data: {
            bvn: profile.bvn,
            userId: profile.userId,
            score: item.creditScoreData['credit_score'],
            scoreDate: item.startedAt,
            ...item.creditScoreData,
          },
        });
    }

    if (meta.totalPages > page) {
      await this.pushCreditReport(page + 1);
    }
  }

  findAll() {
    return `This action returns all user`;
  }

  findOne(id: string) {
    return this.userRepository.findOne({
      where: { id },
      select: ['id', 'name', 'email', 'phoneNo', 'lastName'],
    });
  }

  async getAccountDetails(userId: string) {
    const profile = await this.profileRepository.findOne({
      where: { userId },
      select: ['accountNo', 'bankName'],
    });

    if (!profile) {
      throw new NotFoundException('Account not found');
    }

    return {
      accountNumber: profile.accountNo,
      bankcode: profile.bankName,
    };
  }

  async getCardDetails(authorizationCode: string) {
    const card = await this.cardRepository.findOne({
      where: { authorizationCode },
      select: [
        'authorizationCode',
        'email',
        'userId',
        'expMonth',
        'expYear',
        'reusable',
      ],
    });

    if (!card) {
      throw new NotFoundException('Card not found');
    }

    return card;
  }

  async getUserDetails(email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const profile = await this.profileRepository.findOne({
      where: { userId: user.id },
    });
    const account = await this.personalCardAccountsRepository.findOne({
      where: { userId: user.id },
    });

    return {
      userId: user.id,
      firstName: user.name,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNo,
      bvn: profile.bvn,
      status: profile.status,
      creditLimit: account?.creditCardLimit ?? profile.creditLimit,
      standardLoanLimit: account?.creditCardLimit ?? profile.standardLoanLimit,
      createdAt: user.createdAt,
    };
  }
}
