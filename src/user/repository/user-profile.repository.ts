import { Injectable } from "@nestjs/common";
import { UserProfiles } from "../../config/entities/UserProfiles";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class UserProfileRepository extends TypeOrmRepository<UserProfiles> {
  constructor(private readonly dataSource: DataSource) {
    super(UserProfiles, dataSource.createEntityManager());
  }
}
