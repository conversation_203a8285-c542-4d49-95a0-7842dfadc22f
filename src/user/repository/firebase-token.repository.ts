import { Injectable } from "@nestjs/common";
import { FireBaseDeviceTokens } from "src/config/entities/FireBaseDeviceTokens";
import { TypeOrmRepository } from "src/config/repository/typeorm.repository";
import { DataSource } from "typeorm";

@Injectable()
export class FirebaseTokenRepository extends TypeOrmRepository<FireBaseDeviceTokens> {
  constructor(private readonly dataSource: DataSource) {
    super(FireBaseDeviceTokens, dataSource.createEntityManager());
  }
}
