import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { PersonalRepaymentCards } from "src/config/entities/PersonalRepaymentCards";

@Injectable()
export class CardRepository extends TypeOrmRepository<PersonalRepaymentCards> {
  constructor(private readonly dataSource: DataSource) {
    super(PersonalRepaymentCards, dataSource.createEntityManager());
  }
}
