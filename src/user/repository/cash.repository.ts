import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { CpcashWallets } from "src/config/entities/CpcashWallets";

@Injectable()
export class CashRepository extends TypeOrmRepository<CpcashWallets> {
  constructor(private readonly dataSource: DataSource) {
    super(CpcashWallets, dataSource.createEntityManager());
  }
}
