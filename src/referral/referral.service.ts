import {
  BadRequestException,
  Injectable,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { UserReferralRepository } from './repository/user-referral.repository';
import { ReferralWalletsRepository } from './repository/referral-wallets.repository';
import { ReferralWalletTransactionRepository } from './repository/referral-wallet-transaction.repository';
import { randomUUID } from 'crypto';
import { NotificationService } from 'src/notification/notification.service';
import { referrals } from './referral';
import { UserRepository } from 'src/user/repository/user.repository';
import { IsNull } from 'typeorm';

@Injectable()
export class ReferralService implements OnModuleInit {
  constructor(
    private readonly userReferralRepository: UserReferralRepository,
    private readonly userRepository: UserRepository,
    private readonly referralWalletsRepository: ReferralWalletsRepository,
    private readonly referralWalletTransactionRepository: ReferralWalletTransactionRepository,
    private readonly notificationService: NotificationService,
  ) {}

  async onModuleInit() {
    setTimeout(async () => {
      // this.notificationService.sendReferralNotification('356766', 100, 'Mera',  'Wisdom Ekeh');
      // this.notificationService.sendReferralNotification('26', 100, 'Wisdom', 'Mera Agbadudu');

      // this.resolveReferralWallets();

    //   for (const referral of referrals) {
    //     try {
    //       await this.payReferralBonus(
    //         referral.ID,
    //         Number(String(referral.Bonus_Amount)?.replace(/,/g, '')),
    //       );
    //     } catch (error) {
    //       console.log('Error paying referral bonus for', referral.ID, error);
    //     }
    //   }
    }, 10000);
  }

  async resolveReferralWallets() {
    const referrals = await this.userReferralRepository.find({
      where: {
        status: 'approved',
        redeemedAt: IsNull(),
      },
    });
    console.log('crediting referral for user', referrals.length);

    for (const referral of referrals) {
      let referralWallet = await this.referralWalletsRepository.findOne({
        where: { referralWalletId: referral.userId },
      });

      if (!referralWallet) {
        referralWallet = await this.referralWalletsRepository.save({
          referralWalletId: referral.userId,
          balance: 0,
          referralWalletType: 'App\User',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          id: randomUUID(),
        });
      }

      const refs = await this.userReferralRepository.find({
        where: {
          status: 'approved',
          redeemedAt: IsNull(),
          userId: referral.userId,
        },
      });

      console.log('crediting referral for user', referral.userId, refs.length);

      if (
      refs.length > 0 &&
         referralWallet.balance <
        refs.map((ref) => ref.userCommission).reduce((a, b) => a + b)
      ) {
        await this.referralWalletsRepository.update(
          { id: referralWallet.id },
          {
            balance: refs
              .map((ref) => ref.userCommission)
              .reduce((a, b) => a + b),
            updatedAt: new Date(),
          },
        );
      }
    }
  }
  async payReferralBonus(referralId: string, amount: number) {
    console.log('Paying referral bonus for', referralId, amount);
    const userReferral = await this.userReferralRepository.findOne({
      where: {
        id: referralId,
      },
    });

    if (!userReferral) {
      throw new NotFoundException('Referral not found');
    }
    if (userReferral.userCommission != amount) {
      await this.userReferralRepository.update(
        { id: referralId },
        {
          userCommission: amount,
          updatedAt: new Date(),
        },
      );
    }

    if (userReferral.status == 'approved' && userReferral.confirmedAt != null) {
      throw new BadRequestException('Referral already confirmed');
    }

    let referralWallet = await this.referralWalletsRepository.findOne({
      where: { referralWalletId: userReferral.userId },
    });

    if (!referralWallet) {
      referralWallet = await this.referralWalletsRepository.save({
        referralWalletId: userReferral.userId,
        balance: 0,
        referralWalletType: 'App\User',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        id: randomUUID(),
      });
    }

    const referralWalletTransaction =
      await this.referralWalletTransactionRepository.save({
        referralWalletId: referralWallet.id,
        amount: amount,
        transactionType: 'cr',
        status: 'earned',
        transactionDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        id: randomUUID(),
        reference: randomUUID(),
        description: 'Referral Bonus earned',
      });

    console.log('Referral Bonus paid for', referralId, amount);

    await this.userReferralRepository.update(
      { id: referralId },
      {
        status: 'approved',
        confirmedAt: new Date(),
      },
    );

    await this.referralWalletsRepository.update(
      { id: referralWallet.id },
      {
        balance: referralWallet.balance + amount,
        updatedAt: new Date(),
      },
    );
    const user = await this.userRepository.findOne({
      where: {
        id: userReferral.userId,
      },
    });
    const referred = await this.userRepository.findOne({
      where: {
        id: userReferral.userId,
      },
    });
    this.notificationService.sendReferralNotification(
      userReferral.userId,
      amount,
      user.name,
      `${referred.name} ${referred.lastName}`,
    );

    return referralWalletTransaction;
  }
}
