import { Module } from '@nestjs/common';
import { ReferralService } from './referral.service';
import { ReferralController } from './referral.controller';
import { UserReferralRepository } from './repository/user-referral.repository';
import { ReferralWalletsRepository } from './repository/referral-wallets.repository';
import { ReferralWalletTransactionRepository } from './repository/referral-wallet-transaction.repository';
import { NotificationModule } from 'src/notification/notification.module';
import { UserRepository } from 'src/user/repository/user.repository';

@Module({
  imports: [NotificationModule],
  controllers: [ReferralController],
  providers: [ReferralService, UserRepository, UserReferralRepository, ReferralWalletsRepository, ReferralWalletTransactionRepository],
})
export class ReferralModule {}
