import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { ReferralWalletTransactions } from "src/config/entities/ReferralWalletTransactions";

@Injectable()
export class ReferralWalletTransactionRepository extends TypeOrmRepository<ReferralWalletTransactions> {
  constructor(private readonly dataSource: DataSource) {
    super(ReferralWalletTransactions, dataSource.createEntityManager());
  }
}
