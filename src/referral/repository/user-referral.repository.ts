import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { UserReferrals } from "src/config/entities/UserReferrals";

@Injectable()
export class UserReferralRepository extends TypeOrmRepository<UserReferrals> {
  constructor(private readonly dataSource: DataSource) {
    super(UserReferrals, dataSource.createEntityManager());
  }
}
