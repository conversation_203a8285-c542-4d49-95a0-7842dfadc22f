import { Injectable } from "@nestjs/common";
import { TypeOrmRepository } from "../../config/repository/typeorm.repository";
import { DataSource } from "typeorm";
import { ReferralWallets } from "src/config/entities/ReferralWallets";

@Injectable()
export class ReferralWalletsRepository extends TypeOrmRepository<ReferralWallets> {
  constructor(private readonly dataSource: DataSource) {
    super(ReferralWallets, dataSource.createEntityManager());
  }
}
