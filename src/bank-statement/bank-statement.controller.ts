import {
  Body,
  Controller,
  Post,
  UseGuards,
  Get,
  Query,
  Param,
} from '@nestjs/common';
import { BankStatementService } from './bank-statement.service';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PaginationQueryDto } from '@crednet/utils';
import { ConfirmStatementDto, RequestStatementDto } from './dto/statement.dto';

@Controller('bank-statement')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('Bank Statement')
export class BankStatementController {
  constructor(private readonly bankStatementService: BankStatementService) {}

  @Post('/request')
  requestStatement(
    @Body() dto: RequestStatementDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.bankStatementService.requestStatement(dto, auth);
  }

    @Post('/request-statement')
  requestStatementNew(
    @Body() dto: RequestStatementDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.bankStatementService.requestStatementNew(dto, auth);
  }

  @Get('/feedback/:requestId')
  getFeedback(
    @Param('requestId') requestId: string,
  ): Promise<any> {
    return this.bankStatementService.getFeedback(requestId);
  }

  @Post('/confirm')
  confirmRequestStatement(
    @Body() dto: ConfirmStatementDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.bankStatementService.confirmRequestStatement(dto, auth.id +'');
  }

  @Get('/mystatements')
  queryMyStatements(
    @Query() dto: PaginationQueryDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.bankStatementService.getMyStatements(auth, dto);
  }

  @Get('/statements')
  queryStatements(@Query() dto: PaginationQueryDto): Promise<any> {
    return this.bankStatementService.getStatements(dto);
  }

  @Get('/statement/:id')
  getStatement(
    @Param('id') id: string,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.bankStatementService.getStatement(auth, id);
  }

  @Get('/banks')
  getBanks(): Promise<any> {
    return this.bankStatementService.getBanks();
  }
}
