import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { Events } from 'src/utils';
import { MbsService } from '@app/mbs';
import { MbsRepository } from './repositories/mbs.repository';
import { BankStatementService } from './bank-statement.service';
import { StatementRepository } from './repositories/statement.repository';
import { DocumentsRepository } from 'src/credit/repository/documents.repository';

@Processor(Events.SAVE_STATEMENT)
export class SaveStatementConsumer extends WorkerHost {
  constructor(
    private readonly mbsRepository: MbsRepository,
    private readonly mbsService: MbsService,
    private readonly bankStatementService: BankStatementService,
    private readonly statementRepository: StatementRepository,
    private readonly documentsRepository: DocumentsRepository,
  ) {
    super();
  }
  private readonly logger = new Logger(SaveStatementConsumer.name);

  async process(job: Job) {
    this.logger.debug(
      `processing job for ${Events.SAVE_STATEMENT}, `,
      job.asJSON(),
    );

    const mbs = await this.mbsRepository.findOne({
      where: { id: job.data.id },
    });
    console.log(mbs);

    if (mbs) {
      try {
        const data = await this.mbsService.getPDF(mbs.ticketId);

        if (data.status == '00') {
          const link = await this.bankStatementService.getFileUrl(data.result);
          //   console.log(link);
          await this.documentsRepository.insert({
            filename: 'bank statement',
            comments: mbs.password,
            url: link,
            type: 'bank_statement',
            userId: mbs.userId,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          await this.mbsRepository.update(
            { id: mbs.id },
            { hasSavedDoc: true },
          );
        }
      } catch (error) {
        console.log(error);
        if (
          error.message == 'ticketNo is not in the right format' ||
          error.message == 'ticketNo is not in the right format'
        ) {
          await this.mbsRepository.update(
            { id: job.data.id },
            { error: error.message },
          );
        }
      }
    }
  }
}
