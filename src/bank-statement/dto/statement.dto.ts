import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  Matches,
  Min,
} from 'class-validator';

// accountNo: data.accountNo,
//       bankId: data.bankId,
//       destinationId: data.destinationId,
//       startDate: data.startDate,
//       endDate: data.endDate,
//       role: data.role,
//       userName: config.mbs.clientId,
//       country: 'NG',
//       phone: data.phone,
//       applicants: [{ name: data.applicantName }],

export class RequestStatementDto {
  @ApiProperty()
  @IsString()
  accountNo: string;

  @ApiProperty()
  @IsString()
  @Matches(new RegExp('^[0][7-9]{1}[0-1]{1}[0-9]{8,9}$'), {
    message: 'Phone number must be a valid Nigerian number',
  })
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bankId: string;

  // @ApiProperty()
  // @IsString()
  // @IsNotEmpty()
  // applicantName: string;

  // @ApiProperty()
  // @IsString()
  // @IsNotEmpty()
  // role: string;

  // @ApiProperty()
  // @IsString()
  // @IsNotEmpty()
  // endDate: string;

  // @ApiProperty()
  // @IsString()
  // @IsNotEmpty()
  // startDate: string;
}

export class ConfirmStatementDto  {
  @ApiProperty()
  @IsString()
  ticketNo: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otp: string;
  
}
