import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { UserBankStatements } from 'src/config/entities/UserBankStatements';
import { MyBankStatements } from 'src/config/entities/MyBankStatements';

@Injectable()
export class MbsRepository extends TypeOrmRepository<MyBankStatements> {
  constructor(private readonly dataSource: DataSource) {
    super(MyBankStatements, dataSource.createEntityManager());
  }
}
