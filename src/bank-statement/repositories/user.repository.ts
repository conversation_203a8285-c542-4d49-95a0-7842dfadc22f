// import { Injectable } from '@nestjs/common';
// import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
// import { DataSource } from 'typeorm';
// import { UserData } from '../entities/user.entity';

// @Injectable()
// export class UserDataRepository extends TypeOrmRepository<UserData> {
//   constructor(private readonly dataSource: DataSource) {
//     super(UserData, dataSource.createEntityManager());
//   }
// }
