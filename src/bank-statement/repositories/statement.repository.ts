import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { UserBankStatements } from 'src/config/entities/UserBankStatements';

@Injectable()
export class StatementRepository extends TypeOrmRepository<UserBankStatements> {
  constructor(private readonly dataSource: DataSource) {
    super(UserBankStatements, dataSource.createEntityManager());
  }
}
