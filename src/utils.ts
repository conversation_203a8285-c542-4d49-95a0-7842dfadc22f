export enum Exchanges {
  CREDIT = 'credit',
  CREDIT_SCORE = 'credit_score',
  WEBHOOK = 'webhook',
  NOTIFICATION = 'notification',
  CASH = 'cash',
  PAYMENT = 'payment',
  LOANBOT = 'loanbot',
}

export enum LoanbotEvents {
  PROCESS_CREDIT_REPORT = 'processReport',
}

export enum CreditScoreEvents {
  SEED_SCORE = 'seed_score',
}

export enum PaystackEvents {
  DIRECT_DEBIT_ACTIVE = 'direct_debit.authorization.active',
  DIRECT_DEBIT_CREATED = 'direct_debit.authorization.created',
}

export enum CashEvents {
  VIRTUAL_FUNDING = 'virtual-funding',
  UPDATE_USERID = 'update-userid',
  REFUND_TRANSACTION = 'refund-transaction',
  REVERSE_TRANSACTION = 'reverse-transaction',
  QUERY_TRANSACTION = 'query-transaction',
}

export enum Providers {
  WEMA = 'wema',
}

export enum TransactionType {
  TOP_UP = 'top_up',
  WITHDRAWAL = 'withdrawal',
  REFUND = 'refund',
}

export enum TransactionStatus {
  UNPROCESSED = 'unprocessed',
  PENDING = 'pending',
  REFUNDED = 'refunded',
  SUCCESS = 'success',
  FAILED = 'failed',
}

export enum QueueEvents {
  WALLET_DEBIT = 'wallet-debit',
}

export enum Events {
  SEND_NOTIFICATION = 'send-mid-priority-notification	',
  SYNC_STATEMENT = 'sync-statement',
  SEND_TRANSACTION = 'send-transaction',
  WALLET_NOTIFICATION = 'send-wallet-notification',
  VERIFY_MANDATE = 'verify-mandate',
  CHECK_MANDATE_STATUS = 'check-mandate-status',
  UPDATE_ACCOUNT_MANDATE_STATUS = 'update-account-mandate-status',
  SAVE_STATEMENT = 'save-statement',
  FETCH_STATEMENT_DOC = 'fetch-statement-doc',
}

export enum NotificationTemplates {
  TRANSATION_REFUNDED = 'transaction_refunded',
  WALLET_TRANSACTION = 'wallet-transaction',
  REFERRAL_BONUS = 'referral_bonus_paid',
}

export enum UserEvents {
  GET_USER = 'get-user',
  GET_USER_RESPONSE = 'get-user-response',
  SYNC_CASH_USER = 'sync-cash-user',
}

export function randomNumberSecure() {
  return Math.floor(
    (crypto.getRandomValues(new Uint32Array(1))[0] / **********) *
      (999999 - 100000 + 1) +
      100000,
  );
}
export enum TranxStatus {
  FAILED = 'failed',
  SUCCESS = 'success',
  ABANDONED = 'abandoned',
}

export const formatPhoneNumber = (phoneNumber: string): string => {
  // Remove any non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // If number already starts with 234, return as is
  if (cleaned.startsWith('234')) {
    return cleaned;
  }

  // If number starts with 0, remove it
  const withoutLeadingZero = cleaned.replace(/^0+/, '');

  // Add 234 prefix if not present
  return `234${withoutLeadingZero}`;
};

export interface CustomerIoPayload {
  userId: string;
  traits?: {
    [key: string]: any;
  };
  context?: {
    [key: string]: any;
  };
  integration?: {
    [key: string]: any;
  };
  timestamp?: string;
}

export class BvnDto {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  middleName: string;
  dateOfBirth: string;
  phoneNumber1: string;
  phoneNumber2: null;
  gender: string;
  stateOfOrigin: string;
  bvn: string;
  nin: string;
  lgaOfOrigin: string;
  lgaOfResidence: string;
  maritalStatus: string;
  watchlisted: boolean;
  registrationDate: string;
  photoUrl: string;
  createdAt: Date;
  updatedAt: Date;
}
