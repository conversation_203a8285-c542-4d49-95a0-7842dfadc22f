import { Test, TestingModule } from '@nestjs/testing';
import { VerificationService } from './verification.service';
import { HttpModule } from '@nestjs/axios';

describe('VerificationService', () => {
  let service: VerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [HttpModule],
      providers: [VerificationService],
    }).compile();

    service = module.get<VerificationService>(VerificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
