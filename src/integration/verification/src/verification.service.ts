import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { VerifyFaceDto, VerifyFaceResponseDto } from './dto';

@Injectable()
export class VerificationService {
  constructor(private readonly httpService: HttpService) {
    // this.fxRate();
  }

  async verifyFace(dto: VerifyFaceDto): Promise<VerifyFaceResponseDto> {
    const body = {};
    const { data } = await firstValueFrom(
      this.httpService.post(`/verifications/bvn/face/url`, dto).pipe(
        catchError((error) => {
          console.log(error);

          throw new UnprocessableEntityException(error.response?.data ?? error);
        }),
      ),
    );
    console.log(data);

    return data?.data as VerifyFaceResponseDto;
  }
}
