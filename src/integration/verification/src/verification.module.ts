import { Module } from '@nestjs/common';
import { VerificationService } from './verification.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [HttpModule.register({
    timeout: 100000,
    maxRedirects: 5,
    baseURL: `${config.verification.uri}`,
    headers: { 
      'Content-Type': 'application/json',
      Accept: 'application/json', 
    },
  })],
  providers: [VerificationService],
  exports: [VerificationService],
})
export class VerificationModule {}
