import { Injectable } from '@nestjs/common';
import axios from 'axios';
import config from 'src/config';
import { formatPhoneNumber } from 'src/utils';

@Injectable()
export class TelegramService {
  private readonly axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: 'https://gatewayapi.telegram.org',
      headers: {
        Authorization: `Bearer ${config.telegram.apiKey}`,
        'Content-Type': 'application/json',
      },
    });
  }
 

  async sendMessage(phoneNumber: string, otp: string): Promise<any> {
    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      const response = await this.axiosInstance.post(
        `/sendVerificationMessage`,
        {
          phone_number: `+${formattedPhone}`,
          code_length: 6,
          payload: 'Your CredPal verification code is {{code}}',
          code: otp,
        },
      );

      return response.data;
    } catch (error) {
      console.error('WhatsApp API Error:', error);
      throw error;
    }
  }
}
