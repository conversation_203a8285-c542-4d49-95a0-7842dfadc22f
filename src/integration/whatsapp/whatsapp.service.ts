import { Injectable } from '@nestjs/common';
import axios from 'axios';
import config from 'src/config';
import { formatPhoneNumber } from 'src/utils';

@Injectable()
export class WhatsAppService {
  private readonly axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      // baseURL: 'https://waapi.app/api/v1',
      baseURL: 'https://graph.facebook.com/v22.0',
      headers: {
        Authorization: `Bearer ${config.whatsapp.apiKey}`,
        'Content-Type': 'application/json',
      },
    });
    // this.sendMessage('08033078715', '949444').then(console.log);
  }

  // async sendMessage(phoneNumber: string, message: string): Promise<any> {
  //   try {
  //     const formattedPhone = formatPhoneNumber(phoneNumber);

  //     const response = await this.axiosInstance.post('/instances/56072/client/action/send-message', {
  //       chatId: `${formattedPhone}@c.us`,
  //       message,
  //     });

  //     return response.data;
  //   } catch (error) {
  //     console.error('WhatsApp API Error:', error);
  //     throw error;
  //   }
  // }

  async sendMessage(phoneNumber: string, otp: string): Promise<any> {
    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      const response = await this.axiosInstance.post(
        `/${config.whatsapp.phoneNumber}/messages`,
        {
          messaging_product: 'whatsapp',
          to: formattedPhone,
          type: 'template',
          template: {
            name: 'account_verification',
            language: {
              code: 'en',
            },
            components: [
              {
                type: 'body',
                parameters: [
                  {
                    type: 'text',
                    text: otp,
                  },
                ],
              },
              {
                type: 'button',
                sub_type: 'url',
                index: '0',
                parameters: [
                  {
                    type: 'text',
                    text: otp,
                  },
                ],
              },
            ],
          },
        },
      );

      return response.data;
    } catch (error) {
      console.error('WhatsApp API Error:', error);
      throw error?.response?.data;
    }
  }
}
