import { Injectable } from '@nestjs/common';
import { FiveLinxService } from './fivelinx.service';
import { formatPhoneNumber } from 'src/utils';
import config from 'src/config';
import { EmisriService } from './emisri.service';

@Injectable()
export class SmsService {
  constructor(
    private readonly fiveLinxService: FiveLinxService,
    private readonly emisriService: EmisriService,
  ) {
      // this.sendSms('07010766624', 'Your CredPal OTP is 408090, DO NOT SHARE WITH ANYONE').then(console.log)
      // this.sendSms('08033078715', 'Your CredPal OTP is 408090, DO NOT SHARE WITH ANYONE').then(console.log)
  }

  async sendSms(recipient: string, message: string) {
    try {
      if (config.smsProvider === 'emisri') {
        return await this.emisriService.sendSms(recipient, message);
      }
      if (config.smsProvider === 'fivelinx') {
        const formattedPhone = formatPhoneNumber(recipient);
        await this.fiveLinxService.sendPlain(formattedPhone, message);
      }
    } catch (error) {
      throw error;
    }
  }
}
