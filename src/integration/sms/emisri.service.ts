import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import config from 'src/config';

@Injectable()
export class EmisriService {
  private axiosInstance: AxiosInstance;
  constructor() {
    this.axiosInstance = axios.create({
      baseURL: config.emisri.url,
      headers: {
        Authorization: `Basic ${Buffer.from(`${config.emisri.username}:${config.emisri.password}`).toString('base64')}`,
        'Content-Type': 'application/json',
        'X-Access-Token': config.emisri.apiKey,
      },
    });
  }

  async sendSms(to: string, text: string) {
    try {
    const response =  await this.axiosInstance.post('SendBulkSMS', {
        source: config.emisri.sender,
        destination: [to],
        text,
      });
      console.log(response)
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
