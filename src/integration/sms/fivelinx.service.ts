import { AxiosInstance, AxiosResponse } from 'axios';
import axios from 'axios';
import config from 'src/config';
import { Injectable } from '@nestjs/common';

@Injectable()
export class FiveLinxService {
  private axiosInstance: AxiosInstance;
  constructor() {
    this.axiosInstance = axios.create({
      baseURL: config.fiveLinx.baseUrl,
    });
  }

  async sendPlain(recipient: string, message: string): Promise<string> {
    try {
      const response: AxiosResponse = await this.axiosInstance.get(
        '/sendsms/plain',
        {
          params: {
            user: config.fiveLinx.user,
            password: config.fiveLinx.password,
            sender: config.fiveLinx.sender,
            SMSText: message,
            GSM: recipient,
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async campaignSms(recipient: string, message: string): Promise<string> {
    try {
      const response: AxiosResponse = await this.axiosInstance.get(
        '/sendsms/plain',
        {
          params: {
            user: config.fiveLinx.user,
            password: config.fiveLinx.password,
            sender: config.fiveLinx.sender,
            SMSText: message,
            GSM: recipient,
            type: 'longSMS',
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
