import { Injectable } from '@nestjs/common';
import {
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
} from '@crednet/utils';
import { PersonalCardATransactionsRepository } from 'src/credit/repository/personal-card-transactions.repository';
import { Events, Exchanges } from 'src/utils';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class TransactionService {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly transactionRepository: PersonalCardATransactionsRepository,
  ) {}

  @OnEvent(Events.SEND_TRANSACTION)
  public async sendTransactionEvent(payload: {
    transactionReference: string;
    returningRoutingKey: string;
  }): Promise<any> {
    console.log('sending transaction', payload.transactionReference);
    await this.sendTransaction(
      payload.transactionReference,
      payload.returningRoutingKey,
    );
  }

  public async sendTransaction(
    transactionReference: string,
    returningRoutingKey: string,
  ): Promise<any> {
    console.log('sending transaction', transactionReference);
    const transaction = await this.transactionRepository.findOne({
      where: { transactionReference },
      // relations: ['wallet'],
    });

    let status = PaymentTransactionStatus.PENDING;
    if (transaction) {
      console.log(
        'transaction',
        transaction.transactionReference,
        transaction.status,
        returningRoutingKey,
      );

      switch (transaction.status) {
        case 'success':
          status = PaymentTransactionStatus.SUCCESSFUL;
          break;
        case 'refunded':
        case 'reversed':
          status = PaymentTransactionStatus.REVERSED;
          break;
        case 'failed':
          status = PaymentTransactionStatus.FAILED;

          break;

        default:
          break;
      }
    } else {
      status = PaymentTransactionStatus.NOT_FOUND;
    }

    await this.rmqService.send(Exchanges.PAYMENT, {
      key: returningRoutingKey,
      data: {
        transaction: {
          amount: transaction ? parseFloat(transaction.amount) : null,
          category: transaction?.category,
          description: transaction?.description,
          status: status,
          reference: transaction?.transactionReference ?? transactionReference,
          walletType: PaymentTransactionWalletType.CREDPAL_CREDIT,
        },
        status,
        reference: transactionReference,
      } as QueryTransactionResponseDto,
    });
  }
}
