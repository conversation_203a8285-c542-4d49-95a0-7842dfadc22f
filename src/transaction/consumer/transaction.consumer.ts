import { QueryTransactionDto, RabbitmqService } from '@crednet/utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { CashEvents, Exchanges } from 'src/utils';
import { TransactionService } from '../transaction.service';

@Injectable()
export class TransactionConsumerService implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly transactionsService: TransactionService,
  ) {}

  async queryTransaction(data: QueryTransactionDto) {
    return this.transactionsService.sendTransaction(
      data.reference,
      data.returningRoutingKey,
    );
  }

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${CashEvents.QUERY_TRANSACTION}`,
      ({ data, ack }) => {
        console.log(
          `${Exchanges.PAYMENT}.${CashEvents.QUERY_TRANSACTION}`,
          'message received',
          data,
        );
        ack();

        this.queryTransaction(data);
      },
    );
  }
}
