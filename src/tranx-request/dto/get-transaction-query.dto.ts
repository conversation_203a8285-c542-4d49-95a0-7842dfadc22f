import { Transform, Type } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
} from 'class-validator';
import e from 'express';
import { TranxStatus } from 'src/utils';
import { PaginationQueryDTO } from './get-transfer-query.dto';

export class TransactionsQueryDTO extends PaginationQueryDTO {
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  customer: number;

  @IsOptional()
  @IsString()
  terminalid: string;

  @IsOptional()
  @IsEnum(TranxStatus)
  status: TranxStatus;

  @IsOptional()
  @Transform(({ value }) => (value ? parseFloat(value) : undefined))
  @IsNumber()
  @IsPositive()
  amount: number;
}
