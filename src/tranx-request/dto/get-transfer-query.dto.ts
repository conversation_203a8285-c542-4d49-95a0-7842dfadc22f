import { Transform, Type } from 'class-transformer';
import { IsDate, IsN<PERSON>ber, IsOptional, IsPositive } from 'class-validator';

export class PaginationQueryDTO {
  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  perPage: number;

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  page: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  from: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  to: Date;

}

export class TransferQueryDTO  extends PaginationQueryDTO {

  @IsOptional()
  @Transform(({ value }) => (value ? parseInt(value, 10) : undefined))
  @IsNumber()
  @IsPositive()
  recipient: number;

}
