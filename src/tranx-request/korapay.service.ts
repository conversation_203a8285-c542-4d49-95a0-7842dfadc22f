import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import config from 'src/config';
@Injectable()
export class KorapayService {
  instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: config.korapay.baseUrl,
      headers: {
        Authorization: ` Bearer ${config.korapay.secretKey}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async getTransaction(reference: string) {
    try {
      const response = await this.instance.get(
        `/transactions/${reference}`,
        {},
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      if (error?.response?.data) {
        return error?.response?.data;
      }
      throw new UnprocessableEntityException('Error fetching transactions');
    }
  }
}
