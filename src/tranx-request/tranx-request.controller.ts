import { Controller, Get, Param, Query } from '@nestjs/common';
import { TranxRequestService } from './tranx-request.service';
import { GetApiKey } from '../airesource/decorator/api-key.decorator';
import { TransactionsQueryDTO } from './dto/get-transaction-query.dto';
import { TransferQueryDTO } from './dto/get-transfer-query.dto';

@Controller('tranx-request')
export class TranxRequestController {
  constructor(private readonly tranxRequestService: TranxRequestService) {}

  @Get('/paystack/transactions')
  transactions(
    @GetApiKey() apikey: string,
    @Query()
    queryParams: TransactionsQueryDTO,
  ) {
    console.log(queryParams);
    return this.tranxRequestService.paystackTransactions(queryParams);
  }

  @Get('/paystack/transfers')
  transfers(
    @GetApiKey() apikey: string,
    @Query()
    queryParams: TransferQueryDTO,
  ) {
    return this.tranxRequestService.paystackTransfers(queryParams);
  }

  @Get('/korapay/transaction/:reference')
  findOne(@GetApiKey() apikey: string, @Param('reference') reference: string) {
    return this.tranxRequestService.korapayFind(reference);
  }
}
