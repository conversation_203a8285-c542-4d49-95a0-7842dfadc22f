import { Module } from '@nestjs/common';
import { TranxRequestService } from './tranx-request.service';
import { TranxRequestController } from './tranx-request.controller';
import { KorapayService } from './korapay.service';
import { PaystackModule } from '@app/paystack';

@Module({
  imports: [PaystackModule],
  controllers: [TranxRequestController],
  providers: [TranxRequestService, KorapayService],
})
export class TranxRequestModule {}
