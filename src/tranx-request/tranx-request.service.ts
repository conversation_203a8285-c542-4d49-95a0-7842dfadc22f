import { Injectable } from '@nestjs/common';
import { KorapayService } from './korapay.service';
import { TransactionsQueryDTO } from './dto/get-transaction-query.dto';
import { TransferQueryDTO } from './dto/get-transfer-query.dto';
import { PaystackService } from '@app/paystack';

@Injectable()
export class TranxRequestService {
  constructor(
    private readonly paystackService: PaystackService,
    private readonly koraypayService: KorapayService,
  ) {}

  async paystackTransactions(queryParams: TransactionsQueryDTO) {
    return await this.paystackService.getTransactions(queryParams);
  }

  async paystackTransfers(queryParams: TransferQueryDTO) {
    return await this.paystackService.getTransfers(queryParams);
  }

  async korapayFind(reference: string) {
    return await this.koraypayService.getTransaction(reference);
  }
}
