import { Controller } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PersonalCardTransactions } from 'src/config/entities/PersonalCardTransactions';
import { Events } from 'src/utils';
import { NotificationService } from './notification.service';

@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @OnEvent(Events.SEND_NOTIFICATION)
  sendTransactionNotification(payload: PersonalCardTransactions) {
    this.notificationService.sendToQueue(payload);
  }
}
