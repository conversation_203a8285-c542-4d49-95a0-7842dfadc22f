// NotificationWebhook.ts

import { RabbitmqService, SendNotificationPayload } from '@crednet/utils';
import { Injectable } from '@nestjs/common';
import { PersonalCardTransactions } from 'src/config/entities/PersonalCardTransactions';
import {
  Events,
  Exchanges,
  NotificationTemplates,
} from 'src/utils';

@Injectable()
export class NotificationService {
  constructor(private readonly rmqService: RabbitmqService) {}
  
  sendToQueue(transaction: PersonalCardTransactions) {
    return this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.WALLET_NOTIFICATION,
      data: {
        template: NotificationTemplates.WALLET_TRANSACTION,
        userId: transaction?.userId ?? transaction?.userId,
        parameter: {
          amount: transaction.amount,
          category: transaction.category,
          currency: '₦',
          date: transaction.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          description: transaction.description,
          type: transaction.type,
          wallet: 'Credit Card',
        },
      } as SendNotificationPayload,
    });
  }

  sendReferralNotification(userId: string, amount: number, firstName: string, referredName: string) {
    console.log('Sending referral notification to user', userId);
    return this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.WALLET_NOTIFICATION,
      data: {
        template: NotificationTemplates.REFERRAL_BONUS,
        userId,
        parameter: {
          amount,
          firstName,
          referredName,
          // category: transaction.category,
 
        },
      } as SendNotificationPayload,
    })
  }
}
